package com.imile.attendance.infrastructure.repository.driver.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceMonthExportDTO
 * {@code @since:} 2024-01-25 16:51
 * {@code @description:}
 */
@Data
public class DriverAttendanceMonthTitleExportDTO implements Serializable {

    private static final long serialVersionUID = -8374242773476518968L;
    /**
     * 前端title
     */
    private String title;

    /**
     * 前端显示的值
     */
    private String name;

}
