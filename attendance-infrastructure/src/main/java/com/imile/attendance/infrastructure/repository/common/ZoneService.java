package com.imile.attendance.infrastructure.repository.common;

import com.imile.attendance.hermes.support.RpcHermesBusZoneListSupport;
import com.imile.hermes.business.dto.BusCityResultDTO;
import com.imile.hermes.business.dto.BusZoneListDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 区域服务
 */
@Component
public class ZoneService {

    @Resource
    private RpcHermesBusZoneListSupport rpcHermesBusZoneListSupport;

    /**
     * 根据父级ID获取区域列表
     *
     * @param parentId 父级ID
     * @return List<BusZoneListDTO>
     */
    public List<BusZoneListDTO> getZoneListByParentId(Long parentId) {
        return rpcHermesBusZoneListSupport.getZoneListByParentId(parentId);
    }

    /**
     * 根据区域名称和区域级别获取区域列表
     *
     * @param zoneNameList 区域名称列表
     * @param regionLevel  区域级别
     * @return List<BusZoneListDTO>
     */
    public List<BusZoneListDTO> getZoneListByNameAndLevel(List<String> zoneNameList, Integer regionLevel) {
        return rpcHermesBusZoneListSupport.getZoneListByNameAndLevel(zoneNameList, regionLevel);
    }

    /**
     * 根据国家ID获取下级城市列表
     *
     * @param country 国家
     * @return List<BusZoneListDTO>
     */
    public List<BusCityResultDTO> getCityListByCountry(String country) {
        return rpcHermesBusZoneListSupport.getCityListByCountry(country);
    }

    /**
     * 根据区域级别查询行政区域列表
     *
     * @param regionLevel 区域级别
     * @return List<BusZoneListDTO>
     */
    public List<BusZoneListDTO> getBusZoneByRegionLevel(Integer regionLevel) {
        return rpcHermesBusZoneListSupport.getBusZoneByRegionLevel(regionLevel);
    }


}
