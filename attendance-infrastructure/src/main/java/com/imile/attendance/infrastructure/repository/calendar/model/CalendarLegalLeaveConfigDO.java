package com.imile.attendance.infrastructure.repository.calendar.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 日历法定假期配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("calendar_legal_leave_config")
public class CalendarLegalLeaveConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 常驻地国家
     */
    @TableField("location_country")
    private String locationCountry;

    /**
     * 日历id
     */
    @TableField("attendance_config_id")
    private Long attendanceConfigId;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 法定假期名称
     */
    @TableField("legal_leave_name")
    private String legalLeaveName;

    /**
     * 法定假期开始时间：legal_leave_start_day_id 示例：20240124
     */
    @TableField("legal_leave_start_day_id")
    private Long legalLeaveStartDayId;

    /**
     * 法定假期结束时间：legal_leave_end_day_id 示例：20240124
     */
    @TableField("legal_leave_end_day_id")
    private Long legalLeaveEndDayId;

    /**
     * 法定假期时长
     */
    @TableField("legal_leave_duration")
    private Integer legalLeaveDuration;

    public void cleanLastUpd() {
        this.setLastUpdDate(null);
        this.setLastUpdUserCode(null);
        this.setLastUpdUserName(null);
    }

}
