package com.imile.attendance.infrastructure.repository.abnormal.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class StaffAttendanceMissQuery {

    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 截止日期
     */
    private Date endDate;
    /**
     * 是否司机
     */
    private Integer isDriver;
    /**
     * 员工类型
     */
    private List<String> employeeType;
}
