package com.imile.attendance.infrastructure.repository.abnormal.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 员工在该天的请假类型和时长
 * @author: taokang
 * @createDate: 2022-5-26
 * @version: 1.0
 */
@Data
public class LeaveHoursDTO {

    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 请假小时数
     */
    private BigDecimal leaveHours;

    /**
     * 该类型假期的所有图片
     */
    private List<String> picturePathList;
}
