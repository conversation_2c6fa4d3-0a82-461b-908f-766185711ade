package com.imile.attendance.infrastructure.repository.calendar.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalendarConfigRangeQuery implements Serializable {

    /**
     * 业务ID
     */
    private Long bizId;
    /**
     * 业务ID集合
     */
    private Collection<Long> bizIds;


    private String bizCode;
    private List<String> bizCodes;

    /**
     * 范围类型 DEPT,USER,AttendanceRange
     */
    private String rangeType;
    /**
     * 日历配置编码
     */
    private String calendarConfigNo;
    /**
     * 日历配置状态
     */
    private String calendarConfigStatus;

    /**
     * 排除的日历配置编码
     */
    private String notEqCalendarConfigNo;

    /**
     * 查询起始时间
     */
    private Date startTime;
    /**
     * 查询结束时间
     */
    private Date endTime;
}
