package com.imile.attendance.infrastructure.excel.header;

/**
 * <AUTHOR> chen
 * @Date 2025/4/25 
 * @Description Excel表头基础接口
 */
public interface ExcelHeaderBaseService {

    /**
     * 获取英文标题
     */
    String getEnglishTitle();

    /**
     * 获取中文标题
     */
    String getChineseTitle();

    /**
     * 转换为导出DTO
     */
    default ExcelTitleExportDTO toExportDTO(boolean isChinese) {
        return ExcelTitleExportDTO.of(
                getEnglishTitle(),
                isChinese ? getChineseTitle() : getEnglishTitle()
        );
    }
}
