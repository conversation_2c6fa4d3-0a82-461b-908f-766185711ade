package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigRangQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Description
 */
public interface CompanyLeaveConfigRangDao extends IService<CompanyLeaveConfigRangDO> {

    /**
     * 根据条件获取假期配置范围
     * @param leaveConfigRangQuery
     * @return
     */
    List<CompanyLeaveConfigRangDO> getLeaveConfigRangList(LeaveConfigRangQuery leaveConfigRangQuery);

    /**
     * 获取假期配置范围
     *
     * @param leaveConfigIdList 假期配置id
     * @return 假期配置范围
     */
    List<CompanyLeaveConfigRangDO> selectByLeaveId(List<Long> leaveConfigIdList);

    /**
     * 获取假期配置范围
     *
     * @param userCodeList 用户编码
     * @return 假期配置范围
     */
    List<CompanyLeaveConfigRangDO> selectRangByUserCode(List<String> userCodeList);

    void handlerCompanyLeaveConfigRang(List<CompanyLeaveConfigRangDO> addLeaveRang, List<CompanyLeaveConfigRangDO> updateLeaveRang);
}
