package com.imile.attendance.infrastructure.repository.zkteco.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.zkteco.model.AttendanceCompletionRateDO;

import java.util.List;

/**
 * <p>
 * 打卡信息统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public interface AttendanceCompletionRateDao extends IService<AttendanceCompletionRateDO> {

    /**
     * 通过dayId列表获取
     */
    List<AttendanceCompletionRateDO> selectListByDayIds(List<Long> dayIds);
}
