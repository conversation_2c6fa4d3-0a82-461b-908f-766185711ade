package com.imile.attendance.infrastructure.repository.driver.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailMonthDTO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailMonthQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
public interface DriverAttendanceDetailDao extends IService<DriverAttendanceDetailDO> {


    /**
     * 查询司机考勤
     */
    List<DriverAttendanceDetailDTO> queryDriverAttendance(DriverAttendanceDetailQuery query);

    /**
     * 根据day_id查询司机考勤
     */
    List<DriverAttendanceDetailDO> queryDriverAttendanceByCondition(DriverAttendanceDetailInfoQuery driverAttendanceDetailQuery);


    /**
     * todo hrms_user_info,hrms_user_entry_record,hrms_user_dimission_record
     */
    List<DriverAttendanceDetailMonthDTO> queryDriverMonthAttendance(DriverAttendanceDetailMonthQuery query);


    List<DriverAttendanceDetailDO> listByPage(int currentPage, int pageSize);



}

