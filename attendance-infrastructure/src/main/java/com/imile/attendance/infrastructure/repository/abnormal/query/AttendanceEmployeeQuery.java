package com.imile.attendance.infrastructure.repository.abnormal.query;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 出勤查询类
 * <AUTHOR>
 */
@Data
public class AttendanceEmployeeQuery extends ResourceQuery {
    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 员工姓名或邮箱
     */
    private String userNameOrEmail;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 部门ids
     */
    private List<Long> deptIds;

    /**
     * 用户id
     */
    private List<Long> userIds;

}
