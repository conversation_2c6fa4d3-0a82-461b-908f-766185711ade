package com.imile.attendance.infrastructure.repository.abnormal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;

import java.util.List;

/**
 * <p>
 * 员工异常考勤操作记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
public interface EmployeeAbnormalOperationRecordDao extends IService<EmployeeAbnormalOperationRecordDO> {

    List<EmployeeAbnormalOperationRecordDO> selectByAbnormalList(List<Long> abnormalList);

}
