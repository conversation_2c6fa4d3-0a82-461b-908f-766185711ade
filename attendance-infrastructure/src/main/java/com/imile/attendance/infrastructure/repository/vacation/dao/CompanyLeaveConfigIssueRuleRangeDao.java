package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Description
 */
public interface CompanyLeaveConfigIssueRuleRangeDao extends IService<CompanyLeaveConfigIssueRuleRangeDO> {
    /**
     * 获取假期配置发放规则范围
     *
     * @param issueRuleIdList 假期配置发放规则id
     * @return 假期配置发放规则范围
     */
    List<CompanyLeaveConfigIssueRuleRangeDO> selectByIssueRuleId(List<Long> issueRuleIdList);

    /**
     * 获取假期配置发放规则范围
     *
     * @param issueRuleId 假期配置发放规则id
     * @return 假期配置发放规则范围
     */
    List<CompanyLeaveConfigIssueRuleRangeDO> getLeaveConfigIssueRuleRangeList(Long issueRuleId);

}
