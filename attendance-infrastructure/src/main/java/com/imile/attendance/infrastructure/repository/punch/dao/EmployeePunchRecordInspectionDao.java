package com.imile.attendance.infrastructure.repository.punch.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordInspectionDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchRecordInspectionQuery;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} EmployeePunchRecordInspectionDao
 * {@code @since:} 2025-01-09 14:20
 * {@code @description:}
 */
public interface EmployeePunchRecordInspectionDao extends IService<EmployeePunchRecordInspectionDO> {
    /**
     * 根据条件查询打卡记录
     *
     * @param query 查询条件
     * @return 打卡记录
     */
    List<EmployeePunchRecordInspectionDO> queryByCondition(EmployeePunchRecordInspectionQuery query);
}
