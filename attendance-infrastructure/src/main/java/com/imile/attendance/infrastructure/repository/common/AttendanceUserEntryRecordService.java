package com.imile.attendance.infrastructure.repository.common;

import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 用户入职记录服务,todo 离职记录服务
 */
@Component
public class AttendanceUserEntryRecordService {

    @Resource
    private UserEntryRecordDao userEntryRecordDao;


    /**
     * 根据用户id查询入职信息
     */
    public List<AttendanceUserEntryRecord> selectUserEntryByUserIds(List<Long> userIds){
        List<UserEntryRecordDO> userEntryRecordDOS = userEntryRecordDao.listByUserIds(userIds);
        return CommonMapstruct.INSTANCE.mapToUserEntryRecord(userEntryRecordDOS);
    }

    /**
     * 根据用户id查询入职信息
     */
    public AttendanceUserEntryRecord selectUserEntryByUserId(Long userId){
        UserEntryRecordDO userEntryRecordDO = userEntryRecordDao.getById(userId);
        return CommonMapstruct.INSTANCE.mapToUserEntryRecord(userEntryRecordDO);
    }
}
