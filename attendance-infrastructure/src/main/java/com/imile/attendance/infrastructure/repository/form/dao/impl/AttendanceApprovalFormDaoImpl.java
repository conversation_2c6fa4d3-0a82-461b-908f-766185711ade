package com.imile.attendance.infrastructure.repository.form.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormDao;
import com.imile.attendance.infrastructure.repository.form.mapper.AttendanceApprovalFormMapper;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormQuery;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceApprovalFormDaoImpl extends ServiceImpl<AttendanceApprovalFormMapper, AttendanceApprovalFormDO> implements AttendanceApprovalFormDao {

    @Override
    public List<AttendanceApprovalFormDO> selectByCondition(ApprovalFormQuery approvalFormQuery) {
        if (ObjectUtil.isNull(approvalFormQuery)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AttendanceApprovalFormDO> query = Wrappers.lambdaQuery();
        query.in(CollectionUtils.isNotEmpty(approvalFormQuery.getFormIdList()), AttendanceApprovalFormDO::getId, approvalFormQuery.getFormIdList());
        query.eq(ObjectUtil.isNotEmpty(approvalFormQuery.getApplicationCode()), AttendanceApprovalFormDO::getApplicationCode, approvalFormQuery.getApplicationCode());
        query.eq(ObjectUtil.isNotEmpty(approvalFormQuery.getFormStatus()), AttendanceApprovalFormDO::getFormStatus, approvalFormQuery.getFormStatus());
        query.in(CollectionUtils.isNotEmpty(approvalFormQuery.getFormStatusList()), AttendanceApprovalFormDO::getFormStatus, approvalFormQuery.getFormStatusList());
        query.in(CollectionUtils.isNotEmpty(approvalFormQuery.getFormTypeList()), AttendanceApprovalFormDO::getFormType, approvalFormQuery.getFormTypeList());
        query.eq(ObjectUtil.isNotNull(approvalFormQuery.getDataSource()), AttendanceApprovalFormDO::getDataSource, approvalFormQuery.getDataSource());
        query.ge(ObjectUtil.isNotEmpty(approvalFormQuery.getStartDate()), AttendanceApprovalFormDO::getCreateDate, approvalFormQuery.getStartDate());
        query.le(ObjectUtil.isNotEmpty(approvalFormQuery.getEndDate()), AttendanceApprovalFormDO::getCreateDate, approvalFormQuery.getEndDate());
        query.eq(AttendanceApprovalFormDO::getIsDelete, BusinessConstant.N);
        query.orderByDesc(AttendanceApprovalFormDO::getCreateDate);
        return this.list(query);
    }
}

