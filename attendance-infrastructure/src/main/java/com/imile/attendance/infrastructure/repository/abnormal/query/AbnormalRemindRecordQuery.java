package com.imile.attendance.infrastructure.repository.abnormal.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AbnormalRemindRecordQuery {

    private String userCode;

    private Long dayId;

    private String abnormalType;

    private Long punchClassConfigId;

    private String punchClassItemConfigId;

    private Integer sendStatus;

    private Integer sendType;

    private String sendMsg;
}
