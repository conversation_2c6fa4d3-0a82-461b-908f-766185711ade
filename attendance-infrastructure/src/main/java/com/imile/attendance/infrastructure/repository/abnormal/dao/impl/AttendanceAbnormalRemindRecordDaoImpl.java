package com.imile.attendance.infrastructure.repository.abnormal.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceAbnormalRemindRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.mapper.AttendanceAbnormalRemindRecordMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceAbnormalRemindRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalRemindRecordQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 异常考勤发送提醒记录表 数据操作实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Service
public class AttendanceAbnormalRemindRecordDaoImpl extends ServiceImpl<AttendanceAbnormalRemindRecordMapper, AttendanceAbnormalRemindRecordDO> implements AttendanceAbnormalRemindRecordDao {

    @Override
    public List<AttendanceAbnormalRemindRecordDO> listOnly(AbnormalRemindRecordQuery query) {
        LambdaQueryWrapper<AttendanceAbnormalRemindRecordDO> queryWrapper = Wrappers.lambdaQuery();

        if (StringUtils.isNotBlank(query.getUserCode())) {
            queryWrapper.eq(AttendanceAbnormalRemindRecordDO::getUserCode, query.getUserCode());
        }
        if (Objects.nonNull(query.getDayId())) {
            queryWrapper.eq(AttendanceAbnormalRemindRecordDO::getDayId, query.getDayId());
        }
        if (Objects.nonNull(query.getPunchClassConfigId())) {
            queryWrapper.eq(AttendanceAbnormalRemindRecordDO::getPunchClassConfigId, query.getPunchClassConfigId());
        }
        if (Objects.nonNull(query.getSendStatus())) {
            queryWrapper.eq(AttendanceAbnormalRemindRecordDO::getSendStatus, query.getSendStatus());
        }
        if (Objects.nonNull(query.getSendType())) {
            queryWrapper.eq(AttendanceAbnormalRemindRecordDO::getSendType, BusinessConstant.N);
        }
        queryWrapper.eq(AttendanceAbnormalRemindRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.last("limit 1");
        return list(queryWrapper);
    }
}
