package com.imile.attendance.infrastructure.repository.calendar.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@Data
@NoArgsConstructor
public class CalendarConfigRangeIdsDTO {

    /**
     * 保存与其已关联部门ID
     */
    private Collection<Long> deptIds;
    /**
     * 保存与其已关联用户ID
     */
    private Collection<Long> userIds;


    private Collection<String> deptCodes;
    private Collection<String> userCodes;

    public CalendarConfigRangeIdsDTO(Collection<Long> deptIds, Collection<Long> userIds) {
        this.deptIds = deptIds;
        this.userIds = userIds;
    }

    /**
     * 部门使用的打卡规则名称
     * @param deptIds
     * @param userIds
     */
    Map<Long, String> deptNoMap = new HashMap<>();

    /**
     * 用户使用的打卡规则名称
     * @param deptIds
     * @param userIds
     */
    Map<Long, String> userNoMap = new HashMap<>();


    public static CalendarConfigRangeIdsDTO of(Collection<Long> deptIds,
                                               Collection<Long> userIds,
                                               Collection<String> deptCodes,
                                               Collection<String> userCodes) {
        CalendarConfigRangeIdsDTO dto = new CalendarConfigRangeIdsDTO();
        dto.setDeptIds(deptIds);
        dto.setUserIds(userIds);
        dto.setDeptCodes(deptCodes);
        dto.setUserCodes(userCodes);
        return dto;
    }
}
