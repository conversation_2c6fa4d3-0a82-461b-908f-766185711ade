package com.imile.attendance.infrastructure.repository.abnormal.dto;

import com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 员工考勤列表DTO类
 *
 * <AUTHOR>
 */
@Data
public class AttendanceDTO extends UserInformationDTO {

    private Long id;
    /**
     * 应出勤天数
     */
    private Integer acturllyAttendanceDays;
    /**
     * 出勤天数
     */
    private BigDecimal attendanceDays;
    /**
     * 工作日加班天数
     */
    private BigDecimal overtimeDaysOnPresentDay;
    /**
     * 工作日加班小时数
     */
    private BigDecimal overtimeHoursOnPresentDay;
    /**
     * 休息日加班天数
     */
    private BigDecimal overtimeDaysOnWeekend;
    /**
     * 休息日加班小时数
     */
    private BigDecimal overtimeHoursOnWeekend;
    /**
     * 节假日加班天数
     */
    private BigDecimal overtimeDaysOnHoliday;
    /**
     * 节假日加班小时数
     */
    private BigDecimal overtimeHoursOnHoliday;
    /**
     * 休息日天数
     */
    private Integer weekendCnt;
    /**
     * 节假日天数
     */
    private Integer holidayCnt;

    /**
     * 司机派件量
     */
    private Integer deliveryCount;
    /**
     * 派件签收数量
     */
    private Integer deliveredCount;
    /**
     * 收件数量
     */
    private Integer pickUpCount;

    /**
     * 派件数
     */
    //private Integer dldCnt;

    /**
     * 仓内员工扫件数
     */
    private Integer scanNumber;


    /**
     * 时间
     */
    private String timeCycle;

    /**
     * 员工状态
     */
    private String workStatus;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 状态
     */
    private String status;

    /**
     * 停用时间
     */
    private Date disabledDate;

    /**
     * 日历名称
     */
    private String attendanceConfigName;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;
}
