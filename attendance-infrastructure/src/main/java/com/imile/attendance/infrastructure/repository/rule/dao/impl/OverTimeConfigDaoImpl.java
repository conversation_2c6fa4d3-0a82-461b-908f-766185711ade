package com.imile.attendance.infrastructure.repository.rule.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.OverTimeConfigMapper;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Component
@RequiredArgsConstructor
public class OverTimeConfigDaoImpl extends ServiceImpl<OverTimeConfigMapper, OverTimeConfigDO>
        implements OverTimeConfigDao {

    @Override
    public OverTimeConfigDO getByName(String name) {
        LambdaQueryWrapper<OverTimeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OverTimeConfigDO::getConfigName, name)
                .eq(OverTimeConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(OverTimeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(OverTimeConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public OverTimeConfigDO getLatestByConfigNo(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return null;
        }
        LambdaQueryWrapper<OverTimeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OverTimeConfigDO::getConfigNo, configNo);
        queryWrapper.eq(OverTimeConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(OverTimeConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(OverTimeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<OverTimeConfigDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public List<OverTimeConfigDO> getByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OverTimeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OverTimeConfigDO::getCountry, country)
                .eq(OverTimeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(OverTimeConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(OverTimeConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<OverTimeConfigDO> listByCountries(List<String> countries) {
        if (CollectionUtils.isEmpty(countries)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OverTimeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OverTimeConfigDO::getCountry, countries);
        queryWrapper.eq(OverTimeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(OverTimeConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(OverTimeConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<OverTimeConfigDO> listLatestByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OverTimeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OverTimeConfigDO::getId, configIdList);
        queryWrapper.eq(OverTimeConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(OverTimeConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(OverTimeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<OverTimeConfigDO> listByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OverTimeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OverTimeConfigDO::getId, configIdList);
        queryWrapper.eq(OverTimeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<OverTimeConfigDO> listByConfigNo(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OverTimeConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OverTimeConfigDO::getConfigNo, configNo);
        queryWrapper.eq(OverTimeConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(OverTimeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<OverTimeConfigDO> listByQuery(OverTimeConfigQuery query) {
        LambdaQueryWrapper<OverTimeConfigDO> queryWrapper = Wrappers.lambdaQuery();

        if (CollectionUtils.isNotEmpty(query.getConfigIds())) {
            queryWrapper.in(OverTimeConfigDO::getId, query.getConfigIds());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(OverTimeConfigDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(OverTimeConfigDO::getCountry, query.getCountryList());
        }
        if (Objects.nonNull(query.getConfigNos())) {
            queryWrapper.in(OverTimeConfigDO::getConfigNo, query.getConfigNos());
        }
        if (StringUtils.isNotBlank(query.getConfigName())) {
            queryWrapper.like(OverTimeConfigDO::getConfigName, query.getConfigName());
        }
        if (Objects.nonNull(query.getDeptId())) {
            queryWrapper.and(i ->
                    i.apply("FIND_IN_SET('" + query.getDeptId().toString() + "', dept_ids)")
            );
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            queryWrapper.and(i -> query.getDeptIds()
                    .forEach(deptId -> i.apply("FIND_IN_SET('" + deptId.toString() + "', dept_ids)").or()));
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(OverTimeConfigDO::getStatus, query.getStatus());
        }
        queryWrapper.eq(OverTimeConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(OverTimeConfigDO::getIsLatest, BusinessConstant.Y);
        // 修改：按照创建时间倒序排序
        queryWrapper.orderByDesc(OverTimeConfigDO::getCreateDate);

        return list(queryWrapper);
    }

    @Override
    public List<OverTimeConfigDO> listCountryLevelConfigsByCountries(List<String> countryList) {
        return this.baseMapper.listCountryLevelConfigsByCountries(countryList);
    }

    @Override
    public List<OverTimeConfigDO> pageQuery(OverTimeConfigPageQuery query) {
        return this.baseMapper.pageQuery(query);
    }
}
