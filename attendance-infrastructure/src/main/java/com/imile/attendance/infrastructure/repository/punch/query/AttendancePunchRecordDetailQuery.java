package com.imile.attendance.infrastructure.repository.punch.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Date;


/**
 * <AUTHOR>
 * @menu
 * @since 2022/1/26
 */
@Data
public class AttendancePunchRecordDetailQuery {

    /**
     * 用户的id
     */
    private Long userId;

    /**
     * 日期  哪一天的打卡记录
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private Date dateTime;


    /**
     * 前端不需要传这个参数
     */
    private Long dayId;

    /**
     * userIds  前端不需要传这个参数
     */
    private Collection<Long> userIds;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 配置id 前端不需要传这个参数
     */
    private Long punchConfigId;

}
