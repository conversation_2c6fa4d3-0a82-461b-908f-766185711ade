package com.imile.attendance.infrastructure.repository.rule.query;

import com.imile.attendance.query.ResourceQuery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReissueCardConfigQuery extends ResourceQuery {

    /**
     * 补卡规则id列表
     */
    private List<Long> configIds;

    /**
     * 国家
     */
    private String country;

    /**
     * 补卡规则编码
     */
    private Set<String> configNos;

    /**
     * 补卡规则名称
     */
    private String configName;

    /**
     * 部门
     */
    private Long deptId;

    /**
     * 部门列表
     */
    private List<Long> deptIds;

    /**
     * 状态
     */
    private String status;

    private List<String> countryList;

    private List<Long> userIdList;
}
