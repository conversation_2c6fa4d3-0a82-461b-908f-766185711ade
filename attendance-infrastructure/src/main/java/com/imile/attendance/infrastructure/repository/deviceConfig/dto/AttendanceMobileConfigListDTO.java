package com.imile.attendance.infrastructure.repository.deviceConfig.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/17
 * @Description
 */
@Data
public class AttendanceMobileConfigListDTO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 用户姓名
     */
    private String userNameEn;

    /**
     * 用户姓名
     */
    private String userNameDesc;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 用户部门
     */
    private String deptName;

    /**
     * 考勤用户手机表主键id
     */
    private Long mobileConfigId;

    /**
     * 手机唯一标识
     */
    private String mobileUnicode;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 手机品牌
     */
    private String mobileBranch;

    /**
     * 手机系统版本
     */
    private String mobileVersion;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人编码
     */
    private String createUserCode;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 最后修改人编码
     */
    private String lastUpdUserCode;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;


}
