package com.imile.attendance.infrastructure.repository.abnormal.mapper;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceAbnormalRemindRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 异常提醒发送记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
@Repository
public interface AttendanceAbnormalRemindRecordMapper extends AttendanceBaseMapper<AttendanceAbnormalRemindRecordDO> {
}
