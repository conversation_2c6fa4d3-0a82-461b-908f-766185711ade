package com.imile.attendance.infrastructure.repository.abnormal.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class UserAttendanceQuery extends ResourceQuery {

    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 员工姓名或邮箱
     */
    private String userNameOrEmail;

    private String userCodeOrName;

    private String country;


    private List<String> countryList;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 是否有国家权限
     */
    private Boolean hasCountryPermission;
    /**
     * 是否有与国家、部门权限
     */
    private Boolean hasAndDeptAndCountryPermission;

    /**
     * 是否有或国家、部门权限
     */
    private Boolean hasOrDeptAndCountryPermission;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位id(多选)
     */
    private List<Long> postIds;
    /**
     * 部门ids
     */
    private List<Long> deptIds;
    /**
     * 用户id
     */
    private List<Long> userIds;
    /**
     * 是否司机
     */
    private Integer isDriver;
    /**
     * 是否仓内人员
     */
    private Integer isWarehouseStaff;
    /**
     * 员工类型
     */
    private List<String> employeeTypes;

    /**
     * 员工状态
     */
    private List<String> workStatus;

    /**
     * 员工状态 是否停用
     */
    private String status;

    /**
     * 员工状态
     */
    private String notInStatus;

    /**
     * 员工状态
     */
    private String notInWorkStatus;

    /**
     * 考勤日历编码
     */
    private String attendanceConfigNo;

    /**
     * 考勤日历编码(多选)
     */
    private List<String> attendanceConfigNoList;

    /**
     * 配置打卡No
     */
    private String punchConfigNo;

    /**
     * 配置打卡No(多选)
     */
    private List<String> punchConfigNoList;

    /**
     * 常住地城市(多选)
     */
    private List<String> locationCityList;

    /**
     * 前端是否选择了部门
     */
    private Boolean isChooseDept;
}
