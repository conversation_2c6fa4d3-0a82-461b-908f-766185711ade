package com.imile.attendance.infrastructure.repository.calendar.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseDayQuery {
    /**
     * 年
     */
    private Long year;

    /**
     *月
     */
    private Long month;

    /**
     * 开始时间
     */
    private Long startDay;

    /**
     * 结束时间
     */
    private Long endDay;
}
