package com.imile.attendance.infrastructure.repository.rule.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.common.enums.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7 
 * @Description
 */
@ApiModel(description = "考勤打卡规则表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("punch_config")
@FieldNameConstants
public class PunchConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 打卡规则编码
     */
    @ApiModelProperty(value = "打卡规则编码")
    private String configNo;

    /**
     * 打卡规则名称
     */
    @ApiModelProperty(value = "打卡规则名称")
    private String configName;

    /**
     * 打卡规则类型：1.免打卡 2.班次固定打卡 3.灵活打卡一次 4.灵活打卡两次
     */
    @ApiModelProperty(value = "打卡规则类型")
    private String configType;

    /**
     * 上下班打卡时间间隔 单位：小时（类型为灵活打卡两次时需设置）
     */
    @ApiModelProperty(value = "上下班打卡时间间隔")
    private BigDecimal punchTimeInterval;

    /**
     * 是否国家级配置
     */
    @ApiModelProperty(value = "是否国家级配置")
    private Integer isCountryLevel;

    /**
     * 状态 ACTIVE、DISABLED
     */
    @ApiModelProperty(value = "状态 ACTIVE、DISABLED")
    private String status;

    /**
     * 适用部门
     */
    @ApiModelProperty(value = "适用部门")
    private String deptIds;

    /**
     * 是否最新
     */
    @ApiModelProperty(value = "是否最新")
    private Integer isLatest;


    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    @ApiModelProperty(value = "失效时间")
    private Date expireTime;


    public Boolean areCountryLevel() {
        return Objects.equals(this.isCountryLevel, BusinessConstant.Y);
    }

    public List<Long> listDeptIds() {
        if (StringUtils.isNotBlank(this.deptIds)) {
            return Arrays.asList((Long[]) ConvertUtils.convert(this.deptIds.split(","), Long.class));
        }
        return new ArrayList<>();
    }

    public Boolean areLatest(){
        return Objects.equals(this.isLatest, BusinessConstant.Y);
    }

    public Boolean areActive() {
        return StringUtils.equals(this.status, StatusEnum.ACTIVE.getCode());
    }

    public Boolean areDisable() {
        return StringUtils.equals(this.status, StatusEnum.DISABLED.getCode());
    }
}

