package com.imile.attendance.infrastructure.repository.employee.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Data
public class UserInformationDTO {

    /**
     * 主键ID
     */
    private Long userId;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 工作状态 在职、离职等
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_STATUS, ref = "workStatusDesc")
    private String workStatus;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatusDesc;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 所属国
     */
    private String locationCountry;

    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 所属部门名称
     */
    private String deptNameEn;

    /**
     * 工作岗位id
     */
    private Long postId;

    /**
     * 工作岗位名称
     */
    private String postName;

    /**
     * 工作岗位名称
     */
    private String postNameEn;

    /**
     * 汇报上级编码
     */
    private Long leaderId;

    /**
     * 汇报上级名称
     */
    private String leaderName;


    /**
     * 员工性质
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 员工性质名称
     */
    private String employeeTypeDesc;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dimissionDate;

    /**
     * 最近操作时间：取司机打卡记录表某人最近的操作时间
     */
    private Date lastOperatingTime;
}
