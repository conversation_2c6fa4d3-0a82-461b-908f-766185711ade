package com.imile.attendance.infrastructure.repository.abnormal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceAbnormalRemindRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalRemindRecordQuery;

import java.util.List;

/**
 * <p>
 * 异常考勤发送提醒记录表 数据操作类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface AttendanceAbnormalRemindRecordDao extends IService<AttendanceAbnormalRemindRecordDO> {

    /**
     * 异常考勤发送提醒记录查询
     *
     * @param query
     * @return
     */
    List<AttendanceAbnormalRemindRecordDO> listOnly(AbnormalRemindRecordQuery query);

}
