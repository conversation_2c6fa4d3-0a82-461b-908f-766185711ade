package com.imile.attendance.infrastructure.repository.abnormal.mapper;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsAttendanceEmployeeDetailSnapshotMapper
 * {@code @since:} 2024-11-27 14:13
 * {@code @description:}
 */
@Mapper
@Repository
public interface AttendanceEmployeeDetailSnapshotMapper extends AttendanceBaseMapper<AttendanceEmployeeDetailSnapshotDO> {

}