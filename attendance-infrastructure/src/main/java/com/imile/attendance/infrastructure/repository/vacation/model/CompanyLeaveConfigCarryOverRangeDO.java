package com.imile.attendance.infrastructure.repository.vacation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国家假期结转规则失效范围表
 *
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@ApiModel(description = "国家假期结转规则失效范围表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("company_leave_config_carry_over_range")
public class CompanyLeaveConfigCarryOverRangeDO extends BaseDO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家假期规则结转表主键id
     */
    @ApiModelProperty(value = "国家假期规则结转表主键id")
    private Long carryOverId;

    /**
     * 左边符号：1：表示大于 2：表示大于等于
     */
    @ApiModelProperty(value = "左边符号：1：表示大于 2：表示大于等于")
    private Integer symbolLeft;

    /**
     * 左边日期(Mdd)：示例：0124
     */
    @ApiModelProperty(value = "左边日期")
    private Integer entryDateLeft;

    /**
     * 右边符号：1：表示小于 2：表示小于等于
     */
    @ApiModelProperty(value = "右边符号：1：表示小于 2：表示小于等于 ")
    private Integer symbolRight;

    /**
     * 右边日期(Mdd)：示例：0124
     */
    @ApiModelProperty(value = "右边日期")
    private Integer entryDateRight;

    /**
     * 结转失效年：0：次年 1：第三年 2：第四年
     */
    @ApiModelProperty(value = "结转失效年")
    private Integer invalidYear;

    /**
     * 失效日期(Mdd)：示例：0124
     */
    @ApiModelProperty(value = "失效日期")
    private Integer invalidDate;
}