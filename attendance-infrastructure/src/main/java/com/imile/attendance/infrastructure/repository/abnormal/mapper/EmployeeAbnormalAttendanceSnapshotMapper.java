package com.imile.attendance.infrastructure.repository.abnormal.mapper;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;


/**
 * {@code @author:} allen
 * {@code @className:} HrmsEmployeeAbnormalAttendanceSnapshotMapper
 * {@code @since:} 2024-11-27 14:36
 * {@code @description:}
 */
@Mapper
@Repository
public interface EmployeeAbnormalAttendanceSnapshotMapper extends AttendanceBaseMapper<EmployeeAbnormalAttendanceSnapshotDO> {

}