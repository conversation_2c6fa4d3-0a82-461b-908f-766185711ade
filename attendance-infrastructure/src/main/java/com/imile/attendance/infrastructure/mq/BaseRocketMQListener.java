package com.imile.attendance.infrastructure.mq;

import com.imile.attendance.infrastructure.mq.dto.MqRetryParam;
import com.imile.attendance.infrastructure.mq.helper.OperatorHelper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.filter.impl.Operand;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.BeanNameAware;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * RocketMQ消息监听器的基类，提供通用的消息处理和错误处理机制
 * <p>
 * 该基类实现了{@link RocketMQListener}接口，并提供了以下功能：
 * <ul>
 *   <li>消息处理异常捕获和日志记录</li>
 *   <li>失败消息的持久化和重试机制</li>
 *   <li>异步更新重试成功的消息状态</li>
 * </ul>
 * <p>
 * 子类需要实现{@link #doOnMessage(MessageExt)}方法来处理具体的消息逻辑。
 * 当消息处理失败时，基类会自动记录失败信息并支持后续的重试机制。
 * <p>
 * 注意：该类还实现了{@link BeanNameAware}接口，以获取当前监听器的Bean名称，
 * 用于在错误日志和重试记录中标识处理服务。
 *
 * <AUTHOR> chen
 * @date 2025/4/8
 */
@Slf4j
@Getter
public abstract class BaseRocketMQListener implements RocketMQListener<MessageExt>, BeanNameAware {

    /**
     * 消息失败记录服务，用于持久化失败的消息和管理重试机制
     */
    @Resource
    private MqFailRecordService mqFailRecordService;

    /**
     * 业务任务线程池，用于异步执行非关键路径的操作，如更新重试成功的消息状态
     */
    @Resource
    private Executor bizTaskThreadPool;

    /**
     * 当前监听器的Bean名称
     */
    private String beanName;

    /**
     * 设置当前监听器的Bean名称
     *
     * @param name 当前监听器的Bean名称
     */
    @Override
    public void setBeanName(@NotNull String name) {
        this.beanName = name;
    }

    /**
     * 处理RocketMQ消息的主要方法
     * <p>
     * 该方法实现了{@link RocketMQListener}接口的onMessage方法，包含以下流程：
     * <ol>
     *   <li>尝试调用子类实现的{@link #doOnMessage(MessageExt)}方法处理消息</li>
     *   <li>捕获并记录处理过程中的异常，并将失败信息保存到数据库中</li>
     *   <li>同步更新重试成功的消息状态，确保状态一致性</li>
     * </ol>
     *
     * @param messageExt RocketMQ消息对象，包含消息内容和元数据
     */
    @Override
    public void onMessage(MessageExt messageExt) {
        try {
            OperatorHelper.putAdminInfoIfNoUser();
            // 调用子类实现的消息处理方法
            doOnMessage(messageExt);

            // 同步更新重试成功状态，避免异步更新可能导致的状态不一致
            mqFailRecordService.updateRetrySuccessResultIfNecessary(messageExt.getMsgId());
        } catch (Exception e) {
            // 记录异常日志并保存失败记录用于后续重试
            log.error("处理消息失败,msgId:{},服务:{}", messageExt.getMsgId(), beanName, e);
            mqFailRecordService.saveOrUpdateMqRecord(
                    MqRetryParam.builder()
                            .messageExt(messageExt)
                            .exception(e)
                            .retryServiceBean(beanName)
                            .build()
            );
        }
    }

    /**
     * 实际处理消息的抽象方法，需要由子类实现
     * <p>
     * 子类应在该方法中实现具体的消息处理逻辑。当该方法抛出异常时，
     * 基类会自动捕获并记录异常信息，并将失败的消息保存到数据库中以便后续重试。
     *
     * @param messageExt RocketMQ消息对象，包含消息内容和元数据
     * @throws Exception 处理消息过程中可能抛出的异常，将由基类捕获并处理
     */
    public abstract void doOnMessage(MessageExt messageExt) throws Exception;


}
