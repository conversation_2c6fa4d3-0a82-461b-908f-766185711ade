package com.imile.attendance.infrastructure.repository.abnormal.query;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class AttendanceEmployeeDetailQuery {
    /**
     * userId
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 年
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long year;

    /**
     * 月
     */
    private Long month;

}
