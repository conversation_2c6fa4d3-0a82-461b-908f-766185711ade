package com.imile.attendance.infrastructure.repository.rule.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.ReissueCardConfigMapper;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Component
@RequiredArgsConstructor
public class ReissueCardConfigDaoImpl extends ServiceImpl<ReissueCardConfigMapper, ReissueCardConfigDO>
        implements ReissueCardConfigDao {

    @Override
    public ReissueCardConfigDO getByName(String name) {
        LambdaQueryWrapper<ReissueCardConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigDO::getConfigName, name)
                .eq(ReissueCardConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public ReissueCardConfigDO getLatestByConfigNo(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return null;
        }
        LambdaQueryWrapper<ReissueCardConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigDO::getConfigNo, configNo);
        queryWrapper.eq(ReissueCardConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(ReissueCardConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(ReissueCardConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<ReissueCardConfigDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public List<ReissueCardConfigDO> getByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigDO::getCountry, country)
                .eq(ReissueCardConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigDO> listByCountries(List<String> countries) {
        if (CollectionUtils.isEmpty(countries)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigDO::getCountry, countries)
                .eq(ReissueCardConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ReissueCardConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(ReissueCardConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigDO> listLatestByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigDO::getId, configIdList);
        queryWrapper.eq(ReissueCardConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(ReissueCardConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(ReissueCardConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigDO> listByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReissueCardConfigDO::getId, configIdList);
        queryWrapper.eq(ReissueCardConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigDO> listByConfigNo(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ReissueCardConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReissueCardConfigDO::getConfigNo, configNo);
        queryWrapper.eq(ReissueCardConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(ReissueCardConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigDO> listByQuery(ReissueCardConfigQuery query) {
        LambdaQueryWrapper<ReissueCardConfigDO> queryWrapper = Wrappers.lambdaQuery();

        if (CollectionUtils.isNotEmpty(query.getConfigIds())) {
            queryWrapper.in(ReissueCardConfigDO::getId, query.getConfigIds());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(ReissueCardConfigDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(ReissueCardConfigDO::getCountry, query.getCountryList());
        }
        if (Objects.nonNull(query.getConfigNos())) {
            queryWrapper.in(ReissueCardConfigDO::getConfigNo, query.getConfigNos());
        }
        if (StringUtils.isNotBlank(query.getConfigName())) {
            queryWrapper.like(ReissueCardConfigDO::getConfigName, query.getConfigName());
        }
        if (Objects.nonNull(query.getDeptId())) {
            queryWrapper.and(i ->
                    i.apply("FIND_IN_SET('" + query.getDeptId().toString() + "', dept_ids)")
            );
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            queryWrapper.and(i -> query.getDeptIds()
                    .forEach(deptId -> i.apply("FIND_IN_SET('" + deptId.toString() + "', dept_ids)").or()));
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(ReissueCardConfigDO::getStatus, query.getStatus());
        }
        queryWrapper.eq(ReissueCardConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(ReissueCardConfigDO::getIsLatest, BusinessConstant.Y);
        // 修改：按照创建时间倒序排序
        queryWrapper.orderByDesc(ReissueCardConfigDO::getCreateDate);

        return list(queryWrapper);
    }

    @Override
    public List<ReissueCardConfigDO> pageQuery(ReissueCardConfigPageQuery query) {
        return this.baseMapper.pageQuery(query);
    }
}
