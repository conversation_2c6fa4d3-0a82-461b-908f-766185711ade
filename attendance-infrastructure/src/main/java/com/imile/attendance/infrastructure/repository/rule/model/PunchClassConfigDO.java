package com.imile.attendance.infrastructure.repository.rule.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.common.annotation.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@ApiModel(description = "考勤班次规则表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("punch_class_config")
@FieldNameConstants
public class PunchClassConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 班次性质（FIXED_CLASS,MULTIPLE_CLASS）
     */
    @ApiModelProperty(value = "班次性质（FIXED_CLASS,MULTIPLE_CLASS）")
    private String classNature;

    /**
     * 班次规则编码
     */
    @ApiModelProperty(value = "班次规则编码")
    private String configNo;

    /**
     * 班次名称
     */
    @ApiModelProperty(value = "班次名称")
    private String className;

    /**
     * 班次类型: 0表示未选择，1:早班,2:晚班
     */
    @ApiModelProperty(value = "班次类型: 0表示未选择，1:早班,2:晚班")
    private Integer classType;

    /**
     * 法定工作时长（总工作时长？）
     */
    @ApiModelProperty(value = "法定工作时长（总工作时长？）")
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长
     */
    @ApiModelProperty(value = "出勤时长")
    private BigDecimal attendanceHours;

    /**
     * 时段数
     */
    @ApiModelProperty(value = "时段数")
    private Integer itemNum;

    /**
     * 状态 ACTIVE、DISABLED
     */
    @ApiModelProperty(value = "状态 ACTIVE、DISABLED")
    private String status;

    /**
     * 适用国家
     */
    @ApiModelProperty(value = "适用国家")
    private String country;

    /**
     * 适用部门
     */
    @ApiModelProperty(value = "适用部门")
    private String deptIds;

    /**
     * 是否最新
     */
    @ApiModelProperty(value = "是否最新")
    private Integer isLatest;

    /**
     * 是否为国家级别规则
     */
    @ApiModelProperty(value = "是否为国家级别规则")
    private Integer isCountryLevel;

    /**
     * 生效时间
     */
    @Deprecated
    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    /**
     * 失效时间
     */
    @Deprecated
    @ApiModelProperty(value = "失效时间")
    private Date expireTime;
}

