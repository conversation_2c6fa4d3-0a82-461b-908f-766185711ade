<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.common.AttendanceCommonMapper">
    <select id="maxValue" resultType="java.lang.Long" parameterType="java.lang.String">
        select max(${columnName}) from  ${tableName}
    </select>

    <sql id="dataSearchTypeSql">
        <if test="startTime != null">
            <choose>
                <when test="dateSearchType != null and dateSearchType == 'entry'">
                    and huer.entry_date >=#{startTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'transfer'">
                    and hutr.transfer_time >=#{startTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'dimission'">
                    and hudr.plan_dimission_date >=#{startTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'salaryUploadItem'">
                    and hseui.salary_date >=#{startTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'salaryEmployeeDetail'">
                    and hsed.end_time >=#{startTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'DIMISSION'">
                    and hudr.actual_dimission_date >=#{startTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'CANCEL_DIMISSION'">
                    and hudr.last_upd_date >=#{startTime}
                </when>

            </choose>
        </if>
        <if test="endTime != null">
            <choose>
                <when test="dateSearchType != null and dateSearchType == 'entry'">
                    and huer.entry_date &lt;=#{endTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'transfer'">
                    and hutr.transfer_time &lt;=#{endTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'dimission'">
                    and hudr.plan_dimission_date &lt;=#{endTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'salaryUploadItem'">
                    and hseui.salary_date &lt;=#{endTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'salaryEmployeeDetail'">
                    and hsed.end_time &lt;=#{endTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'DIMISSION'">
                    and hudr.actual_dimission_date &lt;=#{endTime}
                </when>
                <when test="dateSearchType != null and dateSearchType == 'CANCEL_DIMISSION'">
                    and hudr.last_upd_date &lt;=#{endTime}
                </when>
            </choose>
        </if>
    </sql>

    <sql id="resourceSql">
        <if test="resourceType!=null">
            <!--无数据权限时-->
            <if test="resourceType=='NONE'">
                AND hui.id = -1
            </if>
            <!--部门数据权限-->
            <if test="resourceType=='DEPT_ID'">
                <!--0，标识全部数据权限-->
                <if test="!organizationIds.contains(0L)">
                    <foreach collection="organizationIds" item='id' open="and hui.dept_id in (" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
            </if>
            <!--全部数据权限，不做任何操作-->
            <if test="resourceType=='ALL'">

            </if>
        </if>
    </sql>

</mapper>
