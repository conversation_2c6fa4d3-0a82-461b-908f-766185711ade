<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigIssueRuleRangeMapper">
  <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="issue_rule_id" jdbcType="BIGINT" property="issueRuleId" />
    <result column="symbol_left" jdbcType="TINYINT" property="symbolLeft" />
    <result column="year_left" jdbcType="INTEGER" property="yearLeft" />
    <result column="symbol_right" jdbcType="TINYINT" property="symbolRight" />
    <result column="year_right" jdbcType="INTEGER" property="yearRight" />
    <result column="issue_quota" jdbcType="DECIMAL" property="issueQuota" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date, 
    last_upd_user_code, last_upd_user_name, issue_rule_id, symbol_left, year_left, symbol_right, 
    year_right, issue_quota
  </sql>
</mapper>