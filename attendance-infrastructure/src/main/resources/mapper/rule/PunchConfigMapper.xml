<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.rule.mapper.PunchConfigMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO">
        <!--@mbg.generated-->
        <!--@Table attendance.punch_config-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="config_no" jdbcType="VARCHAR" property="configNo" />
        <result column="config_name" jdbcType="VARCHAR" property="configName" />
        <result column="config_type" jdbcType="VARCHAR" property="configType" />
        <result column="punch_time_interval" jdbcType="DECIMAL" property="punchTimeInterval" />
        <result column="is_country_level" jdbcType="TINYINT" property="isCountryLevel" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="country" jdbcType="VARCHAR" property="country" />
        <result column="dept_ids" jdbcType="VARCHAR" property="deptIds" />
        <result column="is_latest" jdbcType="TINYINT" property="isLatest" />
        <result column="effect_time" property="effectTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, config_no, config_name, config_type, punch_time_interval, is_country_level,
        `status`, country, dept_ids, is_latest, effect_time,expire_time,
        is_delete, record_version, create_date, create_user_code,
        create_user_name, last_upd_date, last_upd_user_code, last_upd_user_name
    </sql>

    <sql id="Join_Column_List">
        pc.id, pc.config_no, pc.config_name, pc.config_type, pc.punch_time_interval, pc.is_country_level,
    pc.status, pc.country, pc.dept_ids, pc.is_latest, pc.effect_time, pc.expire_time,
    pc.is_delete, pc.record_version, pc.create_date, pc.create_user_code,
    pc.create_user_name, pc.last_upd_date, pc.last_upd_user_code, pc.last_upd_user_name
    </sql>

    <select id="listCountryLevelConfigsByCountries"
            resultType="com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO">
        SELECT
        <include refid="Join_Column_List"/>
        FROM
        punch_config pc
        LEFT JOIN punch_config_range pcr ON pc.id = pcr.rule_config_id
        WHERE
        pcr.id IS NULL
        AND pc.is_latest = 1 AND pc.is_delete = 0
        AND ( pc.dept_ids IS NULL OR pc.dept_ids = '' )
        <if test="countryList != null and countryList.size() != 0">
            <foreach collection="countryList" item="country" open="AND pc.country in (" close=")" separator=",">
                #{country}
            </foreach>
        </if>
    </select>

    <select id="pageQuery"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery"
            resultType="com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        punch_config
        where is_delete = 0 and is_latest = 1
        <if test="punchConfigName!=null and punchConfigName!=''">
            and config_name = #{punchConfigName}
        </if>
        <if test="status!=null and status!=''">
            and status = #{status}
        </if>
        <if test="punchConfigIds!=null and punchConfigIds.size()>0">
            <foreach collection="punchConfigIds" item="punchConfigId" open="and id in (" close=")" separator=",">
                #{punchConfigId}
            </foreach>
        </if>
        <!-- 同时具有部门和国家权限 -->
        <if test="hasDeptPermission == true and hasCountryPermission == true">
            <if test="deptIds != null and deptIds.size() > 0">
                and (
                <foreach collection="deptIds" item="deptId" separator=" or ">
                    FIND_IN_SET(#{deptId}, dept_ids)
                </foreach>
                <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                    or country in
                    <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                        #{country}
                    </foreach>
                </if>
                )
            </if>
            <if test="deptIds == null or deptIds.size() == 0">
                <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                    and country in
                    <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                        #{country}
                    </foreach>
                </if>
            </if>
        </if>
        <!-- 只有部门权限 -->
        <if test="hasDeptPermission == true and hasCountryPermission == false">
            and (
            <foreach collection="deptIds" item="deptId" separator=" or ">
                FIND_IN_SET(#{deptId}, dept_ids)
            </foreach>
            )
        </if>

        <!-- 只有国家权限 -->
        <if test="hasDeptPermission == false and hasCountryPermission == true">
            and country in
            <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                #{country}
            </foreach>
        </if>

        <!-- 选择部门的特殊处理 -->
        <if test="isChooseDept == true">
            <if test="deptIds != null and deptIds.size() > 0">
                and (
                <foreach collection="deptIds" item="deptId" separator=" or ">
                    FIND_IN_SET(#{deptId}, dept_ids)
                </foreach>
                )
            </if>
        </if>
        order by create_date desc
    </select>
</mapper>
