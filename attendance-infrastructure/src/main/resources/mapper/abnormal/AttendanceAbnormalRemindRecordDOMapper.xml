<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.abnormal.mapper.AttendanceAbnormalRemindRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceAbnormalRemindRecordDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="day_id" property="dayId" />
        <result column="user_code" property="userCode" />
        <result column="abnormal_type" property="abnormalType" />
        <result column="punch_class_config_id" property="punchClassConfigId" />
        <result column="punch_class_item_config_id" property="punchClassItemConfigId" />
        <result column="send_status" property="sendStatus" />
        <result column="send_msg" property="sendMsg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        day_id, user_code, abnormal_type, punch_class_config_id, punch_class_item_config_id, sendStatus, send_msg
    </sql>

</mapper>
