<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.abnormal.mapper.EmployeeAbnormalAttendanceSnapshotMapper">
    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO">
        <!--@mbg.generated-->
        <!--@Table hrms.hrms_employee_abnormal_attendance_snapshot-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="post_id" jdbcType="BIGINT" property="postId"/>
        <result column="date" jdbcType="TIMESTAMP" property="date"/>
        <result column="day_id" jdbcType="BIGINT" property="dayId"/>
        <result column="leader_id" jdbcType="BIGINT" property="leaderId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="location_country" jdbcType="VARCHAR" property="locationCountry"/>
        <result column="staff_type" jdbcType="VARCHAR" property="staffType"/>
        <result column="employee_type" jdbcType="VARCHAR" property="employeeType"/>
        <result column="scan_count" jdbcType="VARCHAR" property="scanCount"/>
        <result column="scan_type" jdbcType="VARCHAR" property="scanType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="abnormal_type" jdbcType="VARCHAR" property="abnormalType"/>
        <result column="attendance_type" jdbcType="VARCHAR" property="attendanceType"/>
        <result column="punch_config_id" jdbcType="BIGINT" property="punchConfigId"/>
        <result column="punch_class_config_id" jdbcType="BIGINT" property="punchClassConfigId"/>
        <result column="punch_class_item_config_id" jdbcType="BIGINT" property="punchClassItemConfigId"/>
        <result column="extend" jdbcType="VARCHAR" property="extend"/>
        <result column="attendance_duration" jdbcType="DECIMAL" property="attendanceDuration"/>
        <result column="absence_duration" jdbcType="DECIMAL" property="absenceDuration"/>
        <result column="last_modify_time" jdbcType="TIMESTAMP" property="lastModifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, company_id, dept_id, post_id, `date`, day_id, leader_id, user_id, staff_type, location_country,
        employee_type, scan_count, scan_type, `status`, is_delete, create_date, create_user_code,
        create_user_name, last_upd_user_code, last_upd_user_name, last_upd_date, record_version,
        abnormal_type, attendance_type, punch_config_id, punch_class_config_id, punch_class_item_config_id,
        extend, attendance_duration, absence_duration, last_modify_time
    </sql>
</mapper>