<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.abnormal.mapper.EmployeeAbnormalOperationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO">
        <id column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="record_version" property="recordVersion"/>
        <result column="abnormal_id" property="abnormalId"/>
        <result column="operation_type" property="operationType"/>
        <result column="form_id" property="formId"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="attachment" jdbcType="VARCHAR" property="attachment"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete
        ,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_user_code,
        last_upd_user_name,
        last_upd_date,
        record_version,
        id, abnormal_id, operation_type, form_id, reason, attachment
    </sql>

</mapper>
