<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.calendar.mapper.CalendarLegalLeaveConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="location_country" property="locationCountry" />
        <result column="attendance_config_id" property="attendanceConfigId" />
        <result column="year" property="year" />
        <result column="legal_leave_name" property="legalLeaveName" />
        <result column="legal_leave_start_day_id" property="legalLeaveStartDayId" />
        <result column="legal_leave_end_day_id" property="legalLeaveEndDayId" />
        <result column="legal_leave_duration" property="legalLeaveDuration" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, location_country, attendance_config_id, year, legal_leave_name, legal_leave_start_day_id, legal_leave_end_day_id, legal_leave_duration
    </sql>

    <select id="selectCalendarLegalLeaveConfigList" resultMap="BaseResultMap"
            parameterType="com.imile.attendance.infrastructure.repository.calendar.query.CalendarLegalLeaveConfigQuery">
        select T1.*
        from calendar_legal_leave_config as T1
        where T1.is_delete = 0
        <if test="locationCountry != null and locationCountry != ''">
            and T1.location_country = #{locationCountry}
        </if>
        <if test="locationCountryList!=null and locationCountryList.size()>0">
            <foreach collection="locationCountryList" item="locationCountry" separator="," open="and T1.location_country in (" close=")">
                #{locationCountry}
            </foreach>
        </if>
        <if test="year != null and year != ''">
            and T1.year = #{year}
        </if>
        ORDER BY T1.id desc
    </select>
</mapper>
