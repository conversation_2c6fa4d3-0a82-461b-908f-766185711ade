package com.imile.attendance.third.zkteco;

import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoAreasDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeAreaDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
public interface ZktecoManage {

    List<ZktecoAreasDO> selectSnByArea();

    List<ZktecoEmployeeDO> selectEmployee(List<String> empCodes);

    List<ZktecoAreasDO> selectAreasByName(String areaName);

    List<ZktecoAreasDO> selectAreasByAreaName(String areaName);

    List<ZktecoEmployeeAreaDO> selectEmployeeAreas(List<Integer> areaIdList);

    List<ZktecoEmployeeDO> selectEmployeeByIds(List<Integer> idList);

    List<Integer> selectIClockEmployeeIdByIds(List<Integer> employeeIdList);
}

