package com.imile.attendance.third.zkteco.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoAreasDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeAreaDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeDO;
import com.imile.attendance.infrastructure.repository.zkteco.mapper.ZktecoMapper;
import com.imile.attendance.third.zkteco.ZktecoManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
@DS(AttendanceProperties.Database.ZKT_URL_VERSION_8)
public class ZktecoVersion8ManageImpl implements ZktecoManage {

    @Autowired
    private ZktecoMapper zktecoMapper;

    @Override
    public List<ZktecoAreasDO> selectSnByArea() {
        return zktecoMapper.selectSnByArea();
    }

    @Override
    public List<ZktecoEmployeeDO> selectEmployee(List<String> empCodes) {
        return zktecoMapper.selectEmployee(empCodes);
    }

    @Override
    public List<ZktecoAreasDO> selectAreasByName(String areaName) {
        return zktecoMapper.selectAreasByName(areaName);
    }

    @Override
    public List<ZktecoAreasDO> selectAreasByAreaName(String areaName) {
        return zktecoMapper.selectAreasByAreaName(areaName);
    }

    @Override
    public List<ZktecoEmployeeAreaDO> selectEmployeeAreas(List<Integer> areaIdList) {
        if (CollectionUtils.isEmpty(areaIdList)) {
            return new ArrayList<>();
        }
        return zktecoMapper.selectEmployeeAreas(areaIdList);
    }

    @Override
    public List<ZktecoEmployeeDO> selectEmployeeByIds(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return zktecoMapper.selectEmployeeByIds(idList);
    }

    @Override
    public List<Integer> selectIClockEmployeeIdByIds(List<Integer> employeeIdList) {
        if (CollectionUtils.isEmpty(employeeIdList)) {
            return new ArrayList<>();
        }
        return zktecoMapper.selectEmployeeIdByIds(employeeIdList);
    }

}

