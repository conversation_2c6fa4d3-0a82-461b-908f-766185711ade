package com.imile.attendance.form.bo;


import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description 请假/外勤等业务对象
 */
@Data
public class AttendanceFormDetailBO {

    private AttendanceFormDO formDO;

    private List<AttendanceFormAttrDO> attrDOList;

    private List<AttendanceFormRelationDO> relationDOList;

    public static AttendanceFormDetailBO of(AttendanceFormDO formDO,
                                            List<AttendanceFormAttrDO> attrDOList,
                                            List<AttendanceFormRelationDO> relationDOList) {
        AttendanceFormDetailBO attendanceFormDetailBO = new AttendanceFormDetailBO();
        attendanceFormDetailBO.setFormDO(formDO);
        attendanceFormDetailBO.setAttrDOList(attrDOList);
        attendanceFormDetailBO.setRelationDOList(relationDOList);
        return attendanceFormDetailBO;
    }
}
