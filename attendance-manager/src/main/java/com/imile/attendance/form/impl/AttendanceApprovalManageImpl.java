package com.imile.attendance.form.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormUserInfoDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormRelationDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceUserCardConfigDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceUserCardConfigDO;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.warehourse.model.WarehouseRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-25
 * @version: 1.0
 */
@Service
public class AttendanceApprovalManageImpl implements AttendanceApprovalManage {
    @Resource
    private AttendanceFormDao attendanceFormDao;
    @Resource
    private AttendanceFormAttrDao attendanceFormAttrDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;
    @Resource
    private AttendanceFormRelationDao attendanceFormRelationDao;
    @Resource
    private AttendanceUserCardConfigDao attendanceUserCardConfigDao;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private UserLeaveRecordDao userLeaveRecordDao;
    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;
    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;
    @Resource
    private AttendanceApprovalFormDao approvalFormDao;
    @Resource
    private AttendanceApprovalFormUserInfoDao approvalFormUserInfoDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void formAdd(AttendanceFormDO formDO,
                        List<AttendanceFormRelationDO> relationDOS,
                        List<AttendanceFormAttrDO> formAttrArrayList,
                        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                        EmployeeAbnormalAttendanceDO abnormalAttendance,
                        List<UserLeaveStageDetailDO> userLeaveStageDetailList,
                        UserLeaveRecordDO userLeaveRecord) {
        if (formDO != null) {
            attendanceFormDao.save(formDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOS)) {
            attendanceFormRelationDao.saveBatch(relationDOS);
        }
        if (CollectionUtils.isNotEmpty(formAttrArrayList)) {
            attendanceFormAttrDao.saveBatch(formAttrArrayList);
        }
        if (employeeAbnormalOperationRecord != null) {
            employeeAbnormalOperationRecordDao.save(employeeAbnormalOperationRecord);
        }
        if (abnormalAttendance != null) {
            employeeAbnormalAttendanceDao.updateById(abnormalAttendance);
        }
        if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
            userLeaveStageDetailDao.updateBatchById(userLeaveStageDetailList);
        }
        if (ObjectUtil.isNotNull(userLeaveRecord)) {
            userLeaveRecordDao.save(userLeaveRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void formUpdate(AttendanceFormDO formDO,
                           List<AttendanceFormRelationDO> relationDOS,
                           List<AttendanceFormAttrDO> formAttrArrayList) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOS)) {
            attendanceFormRelationDao.updateBatchById(relationDOS);
        }
        if (CollectionUtils.isNotEmpty(formAttrArrayList)) {
            attendanceFormAttrDao.updateBatchById(formAttrArrayList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(AttendanceFormDO formDO,
                       EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                       AttendanceUserCardConfigDO userCardConfigDO,
                       List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                       UserLeaveRecordDO userLeaveRecord) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (abnormalAttendanceDO != null) {
            employeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
        if (userCardConfigDO != null) {
            attendanceUserCardConfigDao.updateById(userCardConfigDO);
        }
        if (CollUtil.isNotEmpty(userLeaveStageDetailInfoList)) {
            userLeaveStageDetailDao.updateBatchById(userLeaveStageDetailInfoList);
        }
        if (ObjectUtil.isNotNull(userLeaveRecord)) {
            userLeaveRecordDao.save(userLeaveRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(AttendanceFormDO formDO,
                       List<AttendanceFormRelationDO> relationDOS,
                       List<AttendanceFormAttrDO> attrDOS) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOS)) {
            attendanceFormRelationDao.updateBatchById(relationDOS);
        }
        if (CollectionUtils.isNotEmpty(attrDOS)) {
            attendanceFormAttrDao.updateBatchById(attrDOS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reissueFormAdd(AttendanceFormDO formDO,
                               List<AttendanceFormRelationDO> relationDOS,
                               List<AttendanceFormAttrDO> formAttrArrayList,
                               EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                               AttendanceUserCardConfigDO userCardConfigDO,
                               EmployeeAbnormalAttendanceDO abnormalAttendance) {
        if (formDO != null) {
            attendanceFormDao.save(formDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOS)) {
            attendanceFormRelationDao.saveBatch(relationDOS);
        }
        if (CollectionUtils.isNotEmpty(formAttrArrayList)) {
            attendanceFormAttrDao.saveBatch(formAttrArrayList);
        }
        if (employeeAbnormalOperationRecord != null) {
            employeeAbnormalOperationRecordDao.save(employeeAbnormalOperationRecord);
        }
        if (userCardConfigDO != null) {
            attendanceUserCardConfigDao.updateById(userCardConfigDO);
        }
        if (abnormalAttendance != null) {
            employeeAbnormalAttendanceDao.updateById(abnormalAttendance);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDurationAdd(AttendanceFormDO formDO,
                               List<AttendanceFormRelationDO> relationList,
                               List<AttendanceFormAttrDO> applicationFormAttrArrayList,
                               EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                               EmployeeAbnormalAttendanceDO abnormalAttendance) {
        if (formDO != null) {
            attendanceFormDao.save(formDO);
        }
        if (CollectionUtils.isNotEmpty(relationList)) {
            attendanceFormRelationDao.saveBatch(relationList);
        }
        if (CollectionUtils.isNotEmpty(applicationFormAttrArrayList)) {
            attendanceFormAttrDao.saveBatch(applicationFormAttrArrayList);
        }
        if (employeeAbnormalOperationRecord != null) {
            employeeAbnormalOperationRecordDao.save(employeeAbnormalOperationRecord);
        }
        if (abnormalAttendance != null) {
            employeeAbnormalAttendanceDao.updateById(abnormalAttendance);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reissueCardMqPassUpdate(AttendanceFormDO formDO,
                                        EmployeePunchRecordDO employeePunchRecordDO,
                                        EmployeeAbnormalAttendanceDO abnormalAttendance,
                                        WarehouseRecordDO warehouseRecordDO) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (employeePunchRecordDO != null) {
            employeePunchRecordDao.save(employeePunchRecordDO);
        }
        if (abnormalAttendance != null) {
            employeeAbnormalAttendanceDao.updateById(abnormalAttendance);
        }
        // TODO 仓内逻辑
//        if (warehouseRecordDO != null) {
//            hrmsWarehouseRecordDao.save(warehouseRecordDO);
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaveMqPassUpdate(AttendanceFormDO formDO,
                                  EmployeeAbnormalAttendanceDO abnormalAttendance) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (abnormalAttendance != null) {
            employeeAbnormalAttendanceDao.updateById(abnormalAttendance);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeReissueCardMqPassUpdate(List<AttendanceFormDO> formDOList,
                                              AttendanceFormAttrDO attrDO,
                                              List<EmployeePunchRecordDO> employeePunchRecordDOS,
                                              AttendanceUserCardConfigDO cardConfigDO) {
        if (CollectionUtils.isNotEmpty(formDOList)) {
            attendanceFormDao.updateBatchById(formDOList);
        }
        if (attrDO != null) {
            attendanceFormAttrDao.updateById(attrDO);
        }
        if (CollectionUtils.isNotEmpty(employeePunchRecordDOS)) {
            employeePunchRecordDao.updateBatchById(employeePunchRecordDOS);
        }
        if (cardConfigDO != null) {
            attendanceUserCardConfigDao.updateById(cardConfigDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeLeaveMqPassUpdate(List<AttendanceFormDO> formDOList,
                                        AttendanceFormAttrDO attrDO,
                                        List<UserLeaveStageDetailDO> updateStageList,
                                        List<UserLeaveRecordDO> addLeaveRecordList,
                                        List<AttendanceEmployeeDetailDO> employeeDetailDOS) {
        if (CollectionUtils.isNotEmpty(formDOList)) {
            attendanceFormDao.updateBatchById(formDOList);
        }
        if (attrDO != null) {
            attendanceFormAttrDao.updateById(attrDO);
        }
        if (CollectionUtils.isNotEmpty(updateStageList)) {
            userLeaveStageDetailDao.updateBatchById(updateStageList);
        }
        if (CollectionUtils.isNotEmpty(addLeaveRecordList)) {
            userLeaveRecordDao.saveBatch(addLeaveRecordList);
        }
        if (CollectionUtils.isNotEmpty(employeeDetailDOS)) {
            attendanceEmployeeDetailDao.updateBatchById(employeeDetailDOS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalFormAdd(AttendanceApprovalFormDO approvalForm,
                                List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList) {
        if (ObjectUtil.isNotNull(approvalForm)) {
            approvalFormDao.save(approvalForm);
        }
        if (CollUtil.isNotEmpty(approvalFormUserInfoList)) {
            approvalFormUserInfoDao.saveBatch(approvalFormUserInfoList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalFormAddAndUpdate(AttendanceApprovalFormDO approvalForm,
                                         List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList) {
        if (ObjectUtil.isNotNull(approvalForm)) {
            approvalFormDao.updateById(approvalForm);
        }
        List<AttendanceApprovalFormUserInfoDO> deleteList = approvalFormUserInfoList.stream().filter(item -> IsDeleteEnum.YES.getCode() == item.getIsDelete()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(deleteList)) {
            approvalFormUserInfoDao.updateBatchById(deleteList);
        }
        List<AttendanceApprovalFormUserInfoDO> insertList = approvalFormUserInfoList.stream().filter(item -> IsDeleteEnum.YES.getCode() != item.getIsDelete()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(insertList)) {
            approvalFormUserInfoDao.saveBatch(insertList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalFormUpdate(AttendanceApprovalFormDO approvalForm,
                                   List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList) {
        if (ObjectUtil.isNotNull(approvalForm)) {
            approvalFormDao.updateById(approvalForm);
        }
        if (CollUtil.isNotEmpty(approvalFormUserInfoList)) {
            approvalFormUserInfoDao.updateBatchById(approvalFormUserInfoList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalFormOrLeaveUpdate(AttendanceApprovalFormDO approvalForm,
                                          List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList,
                                          List<UserLeaveStageDetailDO> updateUserLeaveStageDetail,
                                          List<UserLeaveRecordDO> addUserLeaveRecord) {
        if (ObjectUtil.isNotNull(approvalForm)) {
            approvalFormDao.updateById(approvalForm);
        }
        if (CollUtil.isNotEmpty(approvalFormUserInfoList)) {
            approvalFormUserInfoDao.updateBatchById(approvalFormUserInfoList);
        }
        if (CollUtil.isNotEmpty(updateUserLeaveStageDetail)) {
            userLeaveStageDetailDao.updateBatchById(updateUserLeaveStageDetail);
        }
        if (CollUtil.isNotEmpty(addUserLeaveRecord)) {
            userLeaveRecordDao.saveBatch(addUserLeaveRecord);
        }
    }
}
