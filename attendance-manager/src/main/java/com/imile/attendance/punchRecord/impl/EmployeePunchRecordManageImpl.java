package com.imile.attendance.punchRecord.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.punchRecord.EmployeePunchRecordManage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/20 
 * @Description
 */
@Component
public class EmployeePunchRecordManageImpl implements EmployeePunchRecordManage {

    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;

    @Override
    public List<EmployeePunchRecordDO> getUserPunchRecords(String userCode, List<Long> dayIds) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(dayIds)) {
            return Collections.emptyList();
        }
        //获取打卡规则的起始结束时间的打卡数据
        EmployeePunchCardRecordQuery punchCardRecordQuery = new EmployeePunchCardRecordQuery();
        punchCardRecordQuery.setDayIds(
                dayIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList())
        );
        punchCardRecordQuery.setUserCode(userCode);
        List<EmployeePunchRecordDO> allPunchRecordList = employeePunchRecordDao.listRecords(punchCardRecordQuery)
                .stream()
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allPunchRecordList)) {
            return Collections.emptyList();
        }
        return allPunchRecordList;
    }
}
