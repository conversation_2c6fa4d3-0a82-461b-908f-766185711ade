package com.imile.attendance.abnormal;

import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Component
public class EmployeeAbnormalAttendanceManage {

    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;

    public Map<Long, List<EmployeeAbnormalAttendanceDO>> mapByUserIdsAndDayIds(List<Long> userIdList, List<Long> dayIdList) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, dayIdList)
                .stream()
                .filter(item -> !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(item.getStatus())).collect(Collectors.toList());
        return abnormalAttendanceDOList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getUserId));
    }

    /**
     * 查询用户当前考勤日的所有异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdAndDayId(Long userId, Long dayId){
        return employeeAbnormalAttendanceDao.selectAbnormalByUserIdAndDayId(userId, dayId);
    }
}
