package com.imile.attendance.punch;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Component
public class EmployeePunchRecordManage {

    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;

    public Map<String, List<UserPunchRecordBO>> mapByUserCodesAndTimeRange(Date startTime, Date endTime, List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)){
            return Collections.emptyMap();
        }
        EmployeePunchCardRecordQuery query = new EmployeePunchCardRecordQuery();
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.setUserCodes(userCodeList);
        List<EmployeePunchRecordDO> allPunchRecordList = employeePunchRecordDao.listRecords(query);
        List<UserPunchRecordBO> userPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordBO userPunchRecordDTO = new UserPunchRecordBO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
            userPunchRecordDTOList.add(userPunchRecordDTO);
        }
       return userPunchRecordDTOList.stream().distinct().collect(Collectors.groupingBy(UserPunchRecordBO::getUserCode));
    }
}
