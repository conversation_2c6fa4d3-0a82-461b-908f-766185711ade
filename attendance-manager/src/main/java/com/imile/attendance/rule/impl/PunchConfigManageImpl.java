package com.imile.attendance.rule.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.bo.CountryPunchConfig;
import com.imile.attendance.rule.bo.PunchConfigBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/8
 * @Description
 */
@Component
public class PunchConfigManageImpl implements PunchConfigManage {

    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private PunchClassConfigRangeDao punchClassConfigRangeDao;

    @Override
    @Transactional
    public void configRangeUpdateOrAdd(List<PunchConfigRangeDO> updateList, List<PunchConfigRangeDO> addList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            punchConfigRangeDao.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            punchConfigRangeDao.saveBatch(addList);
        }
    }

    @Override
    @Transactional
    public void configUpdateAndAdd(PunchConfigDO updateConfig, PunchConfigDO addConfig) {
        if (null != updateConfig) {
            punchConfigDao.updateById(updateConfig);
        }
        if (null != addConfig) {
            punchConfigDao.save(addConfig);
        }
    }

    @Override
    @Transactional
    public void configUpdateAndAdd(PunchConfigDO updateConfig, PunchConfigDO addConfig,
            List<PunchConfigRangeDO> updatedConfigRanges, List<PunchConfigRangeDO> addConfigRanges) {
        if (null != updateConfig) {
            punchConfigDao.updateById(updateConfig);
        }
        if (null != addConfig) {
            punchConfigDao.save(addConfig);
        }
        if (CollectionUtils.isNotEmpty(updatedConfigRanges)) {
            punchConfigRangeDao.updateBatchById(updatedConfigRanges);
        }
        if (CollectionUtils.isNotEmpty(addConfigRanges)) {
            punchConfigRangeDao.saveBatch(addConfigRanges);
        }
    }

    @Override
    public PunchConfigBO getPunchConfigBO(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return null;
        }
        PunchConfigDO punchConfigDO = punchConfigDao.getLatestByConfigNo(configNo);
        if (null == punchConfigDO) {
            return null;
        }
        List<PunchConfigRangeDO> punchConfigRangeDOS = punchConfigRangeDao.listByConfigId(punchConfigDO.getId());
        return PunchConfigBO.of(punchConfigDO, punchConfigRangeDOS);
    }

    @Override
    public CountryPunchConfig getCountryConfig(String country) {
        if (StringUtils.isEmpty(country)) {
            return CountryPunchConfig.empty();
        }
        List<PunchConfigDO> countryPunchConfigs = punchConfigDao.getByCountry(country);
        return CountryPunchConfig.of(country, countryPunchConfigs);
    }

    @Override
    public List<CountryPunchConfig> getCountryConfigList(List<String> countries) {
        List<CountryPunchConfig> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(countries)) {
            return list;
        }
        List<PunchConfigDO> countryPunchConfigs = punchConfigDao.getByCountries(countries);
        if (CollectionUtils.isEmpty(countryPunchConfigs)) {
            return list;
        }
        Map<String, List<PunchConfigDO>> countryPunchConfigMap = countryPunchConfigs.stream()
                .collect(Collectors.groupingBy(PunchConfigDO::getCountry));
        countryPunchConfigMap.forEach((country, configList) -> {
            list.add(CountryPunchConfig.of(country, configList));
        });
        return list;
    }


    @Override
    public Map<Long, PunchConfigDO> getConfigListByUserIdList(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        // 查询用户
        List<AttendanceUser> users = userService.listUsersByIds(userIds);
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyMap();
        }
        Map<Long, PunchConfigDO> userConfigMap = new HashMap<>();
        // 查询用户是否在范围表里
        List<PunchConfigRangeDO> punchConfigRangeDOS = punchConfigRangeDao.listConfigRanges(userIds);
        if (CollectionUtils.isEmpty(punchConfigRangeDOS)) {
            return userConfigMap;
        }

        // 查询<userId, PunchConfigRangeDO>
        Map<Long, PunchConfigRangeDO> rangeMap = punchConfigRangeDOS.stream()
                .collect(Collectors.toMap(PunchConfigRangeDO::getBizId, Function.identity(), (a, b) -> a));

        // 查询<ruleConfigId, PunchConfigDO>
        Map<Long, PunchConfigDO> configMap = punchConfigDao.listLatestByConfigIds(
                punchConfigRangeDOS.stream()
                        .map(PunchConfigRangeDO::getRuleConfigId)
                        .distinct()
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(PunchConfigDO::getId, Function.identity(), (a, b) -> a));

        // 查询在配置范围内的用户
        List<Long> inRangeUserIdList = new ArrayList<>(rangeMap.keySet());
        inRangeUserIdList.forEach(inRangeUserId -> {
            userConfigMap.put(inRangeUserId, configMap.get(rangeMap.get(inRangeUserId).getRuleConfigId()));
        });
        return userConfigMap;
    }

/*    @Override
    public PunchConfigBO getConfigBOByUserId(Long userId) {
        if (null == userId) {
            return null;
        }
        AttendanceUser attendanceUser = userService.getByUserId(userId);
        if (null == attendanceUser) {
            return null;
        }
        // 查询用户是否在范围表里
        List<PunchConfigRangeDO> punchConfigRangeDOList = punchConfigRangeDao.listConfigRanges(Collections.singletonList(userId));
        // 如果范围表里没有用户，则查询用户常驻国的国家级别的规则
        if (CollectionUtils.isEmpty(punchConfigRangeDOList)) {
            List<PunchConfigDO> countryLevelConfigs = punchConfigDao.listCountryLevelConfigsByCountries(
                    Collections.singletonList(attendanceUser.getLocationCountry()));
            if (CollectionUtils.isEmpty(countryLevelConfigs)){
                return null;
            }
            //返回国家级规则
            return PunchConfigBO.buildCountryLevelConfig(countryLevelConfigs.get(0));
        }
        PunchConfigRangeDO punchConfigRangeDO = punchConfigRangeDOList.get(0);
        // 根据规则编码查询规则业务
        return getPunchConfigBO(punchConfigRangeDO.getRuleConfigNo());
    }*/

    @Override
    public List<RuleConfigModifyDTO> selectAllByBizId(Long bizId) {
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectAllByBizId(bizId);
        if (CollectionUtils.isEmpty(punchClassConfigRangeDOList)) {
            return Collections.emptyList();
        }
        List<Long> ruleConfigIdList = punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getRuleConfigId).distinct().collect(Collectors.toList());
        Map<Long, PunchClassConfigDO> punchClassConfigDOMap = punchClassConfigDao.selectByIds(ruleConfigIdList)
                .stream().collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity()));
        return punchClassConfigRangeDOList.stream().map(range -> {
            RuleConfigModifyDTO modifyDTO = new RuleConfigModifyDTO();
            modifyDTO.setCalendarName(punchClassConfigDOMap.getOrDefault(range.getRuleConfigId(), new PunchClassConfigDO()).getClassName());
            modifyDTO.setStartDate(range.getEffectTime());
            modifyDTO.setEndDate(range.getExpireTime());
            modifyDTO.setCreateUserName(range.getCreateUserName());
            return modifyDTO;
        }).collect(Collectors.toList());
    }
}
