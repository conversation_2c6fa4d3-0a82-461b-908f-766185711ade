package com.imile.attendance.rule.bo;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description 补卡规则BO
 */
@Data
public class ReissueCardConfigBO {

    /**
     * 补卡规则DO
     */
    private ReissueCardConfigDO configDO;

    /**
     * 补卡规则适用范围DO列表
     */
    private List<ReissueCardConfigRangeDO> rangeDOList;

    /**
     * 构造方法
     * 
     * @param configDO    补卡规则DO
     * @param rangeDOList 补卡规则适用范围DO列表
     * @return 补卡规则BO
     */
    public static ReissueCardConfigBO of(ReissueCardConfigDO configDO, List<ReissueCardConfigRangeDO> rangeDOList) {
        ReissueCardConfigBO reissueCardConfigBO = new ReissueCardConfigBO();
        reissueCardConfigBO.setConfigDO(configDO);
        reissueCardConfigBO.setRangeDOList(rangeDOList);
        return reissueCardConfigBO;
    }

    /**
     * 检查补卡规则是否为国家级规则
     * 
     * @return 是否为国家级规则
     */
    public Boolean checkConfigIsCountryLevel() {
        if (null == this.configDO) {
            return false;
        }
        return configDO.areCountryLevel();
    }

    /**
     * 查询所有适用范围的ID列表
     * 
     * @return 适用范围ID列表
     */
    public List<Long> queryAllBizRangeIds() {
        if (CollectionUtils.isEmpty(this.rangeDOList)) {
            return Collections.emptyList();
        }
        return this.rangeDOList.stream()
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 查询用户适用范围的ID列表
     * 
     * @return 用户适用范围ID列表
     */
    public List<Long> queryUserRangeIds() {
        if (CollectionUtils.isEmpty(this.rangeDOList)) {
            return Collections.emptyList();
        }
        return this.rangeDOList.stream()
                .filter(ReissueCardConfigRangeDO::areUserRange)
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 查询部门适用范围的ID列表
     * 
     * @return 部门适用范围ID列表
     */
    public List<Long> queryDeptRangeIds() {
        if (CollectionUtils.isEmpty(this.rangeDOList)) {
            return Collections.emptyList();
        }
        return this.rangeDOList.stream()
                .filter(ReissueCardConfigRangeDO::areDeptRange)
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 查询国家适用范围的ID列表
     *
     * @return 国家适用范围ID列表
     */
    public List<Long> queryCountryRangeIds(){
        if (CollectionUtils.isEmpty(this.rangeDOList)) {
            return Collections.emptyList();
        }
        return this.rangeDOList.stream()
                .filter(ReissueCardConfigRangeDO::areCountryRange)
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }


    public Boolean verifyContainsDeptId(Long deptId) {
        if (null == this.configDO) {
            return false;
        }
        List<Long> deptIds = configDO.listDeptIds();
        return deptIds.contains(deptId);
    }

    public Boolean verifyRangeContainsUserId(Long userId) {
        if (null == this.configDO) {
            return false;
        }
        if (CollectionUtils.isEmpty(this.rangeDOList)) {
            return false;
        }
        return this.rangeDOList.stream()
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList())
                .contains(userId);
    }

    /**
     * 根据用户ID获取补卡规则适用范围DO
     * 
     * @param userId 用户ID
     * @return 补卡规则适用范围DO
     */
    public ReissueCardConfigRangeDO getRangeDOByUserId(Long userId) {
        if (null == userId) {
            return null;
        }
        if (null == this.configDO) {
            return null;
        }
        if (CollectionUtils.isEmpty(this.rangeDOList)) {
            return null;
        }
        return this.rangeDOList.stream()
                .filter(o -> Objects.equals(o.getBizId(), userId))
                .findFirst()
                .orElse(null);
    }
    
}
