package com.imile.attendance.shift.impl;

import com.imile.attendance.infrastructure.repository.shift.dao.ShiftTaskRecordDao;
import com.imile.attendance.infrastructure.repository.shift.dao.ShiftTaskRelateUserDao;
import com.imile.attendance.infrastructure.repository.shift.model.ShiftTaskRecordDO;
import com.imile.attendance.infrastructure.repository.shift.model.ShiftTaskRelateUserDO;
import com.imile.attendance.shift.ShiftTaskManage;
import com.imile.attendance.shift.bo.ShiftTaskBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/19 
 * @Description
 */
@Component
public class ShiftTaskManageImpl implements ShiftTaskManage {

    @Resource
    private ShiftTaskRecordDao shiftTaskRecordDao;
    @Resource
    private ShiftTaskRelateUserDao shiftTaskRelateUserDao;

    @Override
    @Transactional
    public void saveBO(ShiftTaskBO shiftTaskBO) {
        ShiftTaskRecordDO shiftTaskRecordDO = shiftTaskBO.getShiftTaskRecordDO();
        List<ShiftTaskRelateUserDO> shiftTaskRelateUserDOList = shiftTaskBO.getShiftTaskRelateUserDOList();
        if (shiftTaskRecordDO != null && CollectionUtils.isNotEmpty(shiftTaskRelateUserDOList)) {
            shiftTaskRecordDao.save(shiftTaskRecordDO);
            shiftTaskRelateUserDao.saveBatch(shiftTaskRelateUserDOList);
        }
    }

    @Override
    @Transactional
    public void updateBO(ShiftTaskBO shiftTaskBO) {
        ShiftTaskRecordDO shiftTaskRecordDO = shiftTaskBO.getShiftTaskRecordDO();
        List<ShiftTaskRelateUserDO> shiftTaskRelateUserDOList = shiftTaskBO.getShiftTaskRelateUserDOList();
        if (shiftTaskRecordDO != null && CollectionUtils.isNotEmpty(shiftTaskRelateUserDOList)) {
            shiftTaskRecordDao.updateById(shiftTaskRecordDO);
            shiftTaskRelateUserDao.updateBatchById(shiftTaskRelateUserDOList);
        }
    }
}
