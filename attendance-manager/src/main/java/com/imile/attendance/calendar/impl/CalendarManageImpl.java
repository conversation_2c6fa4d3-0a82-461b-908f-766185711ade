package com.imile.attendance.calendar.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.calendar.helper.TimeRangeHelper;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.CycleHelpDTO;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarLegalLeaveConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarRangeCountDTO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigRangeQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.common.enums.StatusEnum;
import com.imile.ucenter.api.context.RequestInfoHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21
 * @Description
 */
@Component(value = "calendarManage")
public class CalendarManageImpl implements CalendarManage {

    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private CalendarConfigDetailDao calendarConfigDetailDao;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarLegalLeaveConfigDao calendarLegalLeaveConfigDao;
//    private final PunchConfigRangeDao punchConfigRangeDao;
//    private final HrmsAttendanceConfigRangeDao attendanceConfigRangeDao;

    @Resource
    private AttendanceUserService userService;

    //todo 替换
    private static final String DATE_FORMAT = "yyyy/MM/dd";

    @Override
    public CalendarConfigDO getActiveCalendarConfigById(Long id) {
        return calendarConfigDao.getActiveById(id);
    }


    @Override
    public List<CalendarConfigDO> getActiveCalendarConfigByName(String calendarName) {
        CalendarConfigQuery query = new CalendarConfigQuery();
        query.setStatus(StatusEnum.ACTIVE.getCode());
        query.setAttendanceConfigName(calendarName);
        return calendarConfigDao.listByQuery(query);
    }

    @Override
    public List<CalendarConfigDO> getActiveCalendarConfigByCountry(String country, String calendarName) {
        CalendarConfigQuery query = new CalendarConfigQuery();
        query.setStatus(StatusEnum.ACTIVE.getCode());
        query.setCountry(country);
        query.setAttendanceConfigName(calendarName);
        return calendarConfigDao.listByQuery(query);
    }

    @Override
    public List<UserInfoDO> listOnJobNoDriverUsersExcludeRangeConfigured(RuleRangeUserQuery ruleRangeUserQuery) {
        return calendarConfigRangeDao.listOnJobNoDriverUsersExcludeRangeConfigured(ruleRangeUserQuery);
    }


    @Override
    public List<CalendarConfigDetailDO> getCalendarRangeRecords(Long userId, Long year, String country) {
        if (userId == null) {
            return Collections.emptyList();
        }
        Date nowTime = getDate(year);
        //循环开始时间
        Date startTime = DateUtil.beginOfYear(nowTime);
        //循环终止条件时间
        Date endTime = DateUtil.endOfYear(
                DateUtil.offset(nowTime, DateField.YEAR, -BusinessConstant.DEFAULT_OFFSET));
        return getUserCalendarDetailsWithinPeriod(userId, startTime, endTime, country);
    }

    @Override
    public List<CalendarConfigDetailDO> getCalendarRangeRecords(Long userId, Long year, Long month, String country) {
        if (userId == null) {
            return Collections.emptyList();
        }
        StringBuilder append = new StringBuilder(String.valueOf(year))
                .append("/")
                .append(month)
                .append("/")
                .append("1");
        Date monthTime = DateUtil.parse(append, DATE_FORMAT);
        //循环开始时间
        Date startTime = DateUtil.beginOfMonth(monthTime);
        //循环终止条件时间
        Date endTime = DateUtil.endOfMonth(monthTime);
        return getUserCalendarDetailsWithinPeriod(userId, startTime, endTime, country);
    }

    @Override
    public Map<Long, CalendarConfigDetailDO> getCalendarDetailConfigMap(Long userId, Long year) {
        //返回结果
        Map<Long, CalendarConfigDetailDO> res = new HashMap<>();
        if (userId == null) {
            return res;
        }
        List<AttendanceUser> userList = userService.listUsersByIds(Arrays.asList(userId));
        if (CollectionUtils.isEmpty(userList)) {
            return res;
        }
        Date nowTime = getDate(year);
        //循环开始时间
        Date startTime = DateUtil.beginOfYear(nowTime);
        //循环终止条件时间
        Date endTime = DateUtil.endOfYear(nowTime);
        //获取用户的考勤配置
        CalendarConfigRangeQuery rangeQuery = CalendarConfigRangeQuery.builder()
                .bizId(userId)
                .startTime(startTime)
                .endTime(endTime)
                .build();
        List<CalendarConfigRangeDO> configByUserId = calendarConfigRangeDao.listAllRecords(rangeQuery);

        //获取员工默认考勤配置
//        List<HrmsAttendanceConfigDO> config = hrmsAttendanceConfigDao.config(ATTENDANCE_CONFIG_TYPE, userInfoDOList.get(0).getLocationCountry());
        //todo 待优化获取用户的常驻地国家方法
        String locationCountry = userList.get(0).getLocationCountry();
        List<CalendarConfigDO> defaultCalendarConfigs = calendarConfigDao.getCountryCalendarByType(AttendanceTypeEnum.DEFAULT, locationCountry);
        //用户级别转化将attendanceDetail转化成map
        listDetail(year, res, configByUserId);
        //默认级别转化
        List<CalendarConfigDetailDO> detailDOS = getDefault(year, defaultCalendarConfigs.get(0).getId());
        putRes(res, detailDOS);
        return res;
    }

    @Override
    public List<CalendarConfigDetailDO> getUserCalendarDetailsWithinPeriod(Long userId, Date startTime, Date endTime, String country) {
        // 检查用户是否存在
        List<AttendanceUser> userList = userService.listUsersByIds(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyList();
        }
        //返回结果
        List<CalendarConfigDetailDO> res = new ArrayList<>();

        //获取用户的日历配置范围列表
        CalendarConfigRangeQuery rangeQuery = CalendarConfigRangeQuery.builder()
                .bizId(userId)
                .startTime(startTime)
                .endTime(endTime)
                .build();
        List<CalendarConfigRangeDO> configRanges = calendarConfigRangeDao.listAllRecords(rangeQuery);

        //获取当前国家的默认日历配置
        List<CalendarConfigDO> defaultCalendarConfigs = calendarConfigDao.getCountryCalendarByType(AttendanceTypeEnum.DEFAULT, country);

        CycleHelpDTO cycleHelpDTO = new CycleHelpDTO();
        cycleHelpDTO.setSearchStartTime(startTime);
        cycleHelpDTO.setUserIndex(0);
        cycleHelpDTO.setDeptIndex(0);
        //当startTime 与 endTime 是同一天的时候退出循环
        while (startTime.before(endTime)) {
            //每次搜索设置search结束时间为结束时间为结束时间
            cycleHelpDTO.setSearchEndTime(endTime);

            CalendarConfigRangeDO configRangeDO = getConfigModel(cycleHelpDTO, configRanges);
            //获取该模版对应的考勤配置明细
            List<CalendarConfigDetailDO> details = getUserCalendarDetailsWithinPeriod(defaultCalendarConfigs, cycleHelpDTO, configRangeDO);
            //添加明细
            res.addAll(details);
            //设置循环开始时间为所搜结束时间
            startTime = cycleHelpDTO.getSearchEndTime();
            //设置下一次开始时间为 cycleHelpDTO.getSearchEndTime()
            cycleHelpDTO.setSearchStartTime(startTime);
        }
        return res;
    }

    private List<CalendarConfigDetailDO> getUserCalendarDetailsWithinPeriod(List<CalendarConfigDO> config,
                                                                            CycleHelpDTO cycleHelpDTO,
                                                                            CalendarConfigRangeDO configRangeDO) {
        if (CollectionUtils.isEmpty(config)) {
            return Collections.emptyList();
        }
        CalendarConfigDetailQuery query = new CalendarConfigDetailQuery();
        Long configId = configRangeDO != null ? configRangeDO.getAttendanceConfigId() : config.get(0).getId();
        query.setCalendarConfigId(configId);
        query.setStartTime(cycleHelpDTO.getSearchStartTime());
        query.setEndTime(cycleHelpDTO.getSearchEndTime());
        return calendarConfigDetailDao.listRecords(query);
    }

    @Override
    public List<CalendarConfigDetailDO> getMultiUserCalendarDetailsWithinPeriod(List<Long> userIdList, Date startTime, Date endTime, String country) {
        // 检查用户是否存在
        List<AttendanceUser> userList = userService.listUsersByIds(userIdList);
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyList();
        }
        userIdList = userList.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());
        //返回结果
        List<CalendarConfigDetailDO> res = new ArrayList<>();

        //获取用户的日历配置范围列表
        CalendarConfigRangeQuery rangeQuery = CalendarConfigRangeQuery.builder()
                .bizIds(userIdList)
                .startTime(startTime)
                .endTime(endTime)
                .build();
        List<CalendarConfigRangeDO> configRanges = calendarConfigRangeDao.listAllRecords(rangeQuery);

        //获取当前国家的默认日历配置
        List<CalendarConfigDO> defaultCalendarConfigs = calendarConfigDao.getCountryCalendarByType(AttendanceTypeEnum.DEFAULT, country);

        CycleHelpDTO cycleHelpDTO = new CycleHelpDTO();
        cycleHelpDTO.setSearchStartTime(startTime);
        cycleHelpDTO.setUserIndex(0);
        cycleHelpDTO.setDeptIndex(0);
        //当startTime 与 endTime 是同一天的时候退出循环
        while (startTime.before(endTime)) {
            //每次搜索设置search结束时间为结束时间为结束时间
            cycleHelpDTO.setSearchEndTime(endTime);
            //
            CalendarConfigRangeDO configRangeDO = getConfigModel(cycleHelpDTO, configRanges);
            //获取该模版对应的考勤配置明细
            List<CalendarConfigDetailDO> details = getUserCalendarDetailsWithinPeriod(defaultCalendarConfigs, cycleHelpDTO, configRangeDO);
            //添加明细
            res.addAll(details);
            //设置循环开始时间为所搜结束时间
            startTime = cycleHelpDTO.getSearchEndTime();
            //设置下一次开始时间为 cycleHelpDTO.getSearchEndTime()
            cycleHelpDTO.setSearchStartTime(startTime);
        }
        return res;
    }

    @Override
    public CalendarConfigDetailDO getLatestCalendarConfigDetail(Long userId, Date punchTime) {
        Long effectiveCalendarConfigId = this.getEffectiveCalendarConfigId(userId,
                DateUtil.offsetHour(punchTime, BusinessConstant.DEFAULT_TIMEZONE - RequestInfoHolder.getTimeZoneOffset()));
        return effectiveCalendarConfigId == null ?
                null :
                getCalendarConfigDetail(effectiveCalendarConfigId,
                        Long.parseLong(DateUtil.format(punchTime, DatePattern.PURE_DATE_PATTERN)));
    }

    @Override
    public List<CalendarConfigDO> getByCalendarConfigIds(List<Long> calendarConfigIds) {
        return calendarConfigDao.getByCalendarConfigIds(calendarConfigIds);
    }

    @Override
    public List<CalendarConfigDO> getCalendarConfigsByCountries(List<String> countries) {
        return calendarConfigDao.selectByCountryList(countries);
    }

    @Override
    public List<CalendarConfigDO> getLatestCalendarConfigsByIds(List<Long> calendarConfigIds) {
        if (CollectionUtils.isEmpty(calendarConfigIds)) {
            return Collections.emptyList();
        }
//        LambdaQueryWrapper<HrmsAttendanceConfigDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(HrmsAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
//        queryWrapper.eq(HrmsAttendanceConfigDO::getIsLatest, BusinessConstant.Y);
//        queryWrapper.in(HrmsAttendanceConfigDO::getId, ids);
        CalendarConfigQuery calendarConfigQuery = new CalendarConfigQuery();
        calendarConfigQuery.setIds(calendarConfigIds);
        return calendarConfigDao.listByQuery(calendarConfigQuery);
    }

    @Override
    public CalendarConfigDetailDO getMobileLatestCalendarConfigDetail(Long userId, Date punchTime) {
        Long effectiveCalendarConfigId = this.getEffectiveCalendarConfigId(userId, punchTime);
        return effectiveCalendarConfigId == null ?
                null :
                getCalendarConfigDetail(effectiveCalendarConfigId,
                        Long.parseLong(DateUtil.format(punchTime, DatePattern.PURE_DATE_PATTERN)));
    }

    @Override
    public List<CalendarConfigDetailDO> selectCalendarDetailsByConfigIds(List<Long> calendarConfigIds) {
        return calendarConfigDetailDao.selectListByConfigIds(calendarConfigIds);
    }

    @Override
    public List<CalendarConfigDetailDO> selectCalendarDetailsByIdsAndYear(List<Long> calendarConfigIds, List<Integer> years) {
        return calendarConfigDetailDao.selectListByConfigIdsAndYear(calendarConfigIds, years);
    }

    @Override
    public List<CalendarConfigRangeDO> selectCalendarConfigRange(List<Long> bizIds) {
        return calendarConfigRangeDao.selectConfigRange(bizIds);
    }

    @Override
    public List<CalendarConfigRangeDO> selectCalendarConfigRangeByConfigIds(List<Long> calendarConfigIds) {
        return calendarConfigRangeDao.selectCalendarConfigByIds(calendarConfigIds);
    }

    @Override
    public List<CalendarConfigRangeDO> selectCalendarConfigByDate(CalendarConfigDateQuery query) {
        return calendarConfigRangeDao.selectCalendarConfigByDate(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calendarConfigRangeUpdate(List<CalendarConfigRangeDO> updateList, List<CalendarConfigRangeDO> addList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            calendarConfigRangeDao.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            calendarConfigRangeDao.saveBatch(addList);
        }
    }


//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void userCalendarAndPunchUpdate(List<CalendarConfigRangeDO> addCalendarConfigRangeDOList,
//                                           List<PunchConfigRangeDO> addPunchConfigRanges,
//                                           List<CalendarConfigRangeDO> updateCalendarConfigRangeDOList,
//                                           List<PunchConfigRangeDO> updatePunchConfigRanges,
//                                           List<HrmsCompanyLeaveConfigRangDO> addLeaveRang,
//                                           List<HrmsCompanyLeaveConfigRangDO> updateLeaveRang,
//                                           List<HrmsAttendanceUserLeaveConfigHistoryDO> addUserLeaveHistory,
//                                           List<HrmsAttendanceUserLeaveConfigHistoryDO> updateUserLeaveHistory) {
//        if (CollectionUtils.isNotEmpty(addCalendarConfigRangeDOList)) {
//            calendarConfigRangeDao.saveBatch(addCalendarConfigRangeDOList);
//        }
//        if (CollectionUtils.isNotEmpty(addPunchConfigRanges)) {
//            punchConfigRangeDao.saveBatch(addPunchConfigRanges);
//        }
//        if (CollectionUtils.isNotEmpty(updateCalendarConfigRangeDOList)) {
//            calendarConfigRangeDao.updateBatchById(updateCalendarConfigRangeDOList);
//        }
//        if (CollectionUtils.isNotEmpty(updatePunchConfigRanges)) {
//            punchConfigRangeDao.updateBatchById(updatePunchConfigRanges);
//        }
////        if(CollectionUtils.isNotEmpty(addLeaveRang)){
////            hrmsCompanyLeaveConfigRangDao.saveBatch(addLeaveRang);
////        }
////        if(CollectionUtils.isNotEmpty(updateLeaveRang)){
////            hrmsCompanyLeaveConfigRangDao.updateBatchById(updateLeaveRang);
////        }
////        if (CollectionUtils.isNotEmpty(addUserLeaveHistory)) {
////            userLeaveConfigHistoryDao.saveBatch(addUserLeaveHistory);
////        }
////        if(CollectionUtils.isNotEmpty(updateUserLeaveHistory)){
////            userLeaveConfigHistoryDao.updateBatchById(updateUserLeaveHistory);
////        }
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLegalLeaveConfigAndAttendanceDetail(List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigList,
                                                        List<CalendarConfigDetailDO> needUpdateCalendarConfigDetailDetailList,
                                                        List<CalendarConfigDetailDO> needSaveCalendarConfigDetailDetailList) {
        if (CollUtil.isNotEmpty(calendarLegalLeaveConfigList)) {
            // 保存法定假期配置
            calendarLegalLeaveConfigDao.saveBatch(calendarLegalLeaveConfigList);
        }
        if (CollUtil.isNotEmpty(needUpdateCalendarConfigDetailDetailList)) {
            // 更新考勤配置
            calendarConfigDetailDao.updateBatchById(needUpdateCalendarConfigDetailDetailList);
        }
        if (CollUtil.isNotEmpty(needSaveCalendarConfigDetailDetailList)) {
            // 保存考勤配置
            calendarConfigDetailDao.saveBatch(needSaveCalendarConfigDetailDetailList);
        }
    }

    @Override
    public List<CalendarRangeCountDTO> countCalendarRange(List<Long> calendarConfigIds) {
        return calendarConfigRangeDao.countCalendarRange(calendarConfigIds);
    }


    /**
     * todo
     *
     * @param bizId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRangeByBizId(Long bizId) {
        calendarConfigRangeDao.deleteByBizId(bizId);
//        attendanceConfigRangeDao.deleteByBizId(bizId);
    }

    /**
     * 获取该年随机一天
     *
     * @param year
     * @return
     */
    private Date getDate(Long year) {
        StringBuilder append = new StringBuilder(String.valueOf(year))
                .append("/")
                .append("5")
                .append("/")
                .append("1");
        return DateUtil.parse(append, DATE_FORMAT);
    }

    /**
     * 获取适用于当前时间段的日历配置范围
     */
    private CalendarConfigRangeDO getConfigModel(CycleHelpDTO dto, List<CalendarConfigRangeDO> config) {
        if (CollectionUtils.isEmpty(config)) {
            return null;
        }
        //获取用户级别的config,用户模版不为空且用户模版index小于size
        if (!CollUtil.isEmpty(config) && config.size() > dto.getUserIndex()) {
            CalendarConfigRangeDO userConfig = config.get(dto.getUserIndex());
            Date startDate = userConfig.getStartDate();
            Date endDate = userConfig.getEndDate();
            if ((startDate.before(dto.getSearchStartTime()) || startDate.equals(dto.getSearchStartTime())) && endDate.after(dto.getSearchStartTime())) {
                //设置搜索结束时间为用户模版结束时间获取当前搜索结束时间
                dto.setSearchEndTime(userConfig.getEndDate().before(dto.getSearchEndTime()) ? userConfig.getEndDate() : dto.getSearchEndTime());
                //下一次遍历的用户模版index+1
                dto.setUserIndex(dto.getUserIndex() + 1);
                //本次循环使用的模版类型为用户模版
                return userConfig;
            }
            //设置本次循环所搜结束时间为用户模版开始时间
            dto.setSearchEndTime(userConfig.getStartDate());
        }
        //否则为使用默认模版
        return null;
    }

    /**
     * 获取指定时间段内的日历明细
     *
     * @param defaultConfigs 默认日历配置
     * @param helper         时间范围辅助对象
     * @param configRange    适用的日历配置范围
     * @return 日历明细列表
     */
    private List<CalendarConfigDetailDO> getCalendarDetailsForPeriod(List<CalendarConfigDO> defaultConfigs,
                                                                     TimeRangeHelper helper,
                                                                     CalendarConfigRangeDO configRange) {
        if (CollectionUtils.isEmpty(defaultConfigs)) {
            return Collections.emptyList();
        }

        CalendarConfigDetailQuery query = new CalendarConfigDetailQuery();
        Long calendarConfigId = configRange != null ?
                configRange.getAttendanceConfigId() :
                defaultConfigs.get(0).getId();

        query.setCalendarConfigId(calendarConfigId);
        query.setStartTime(helper.getSearchStartTime());
        query.setEndTime(helper.getSearchEndTime());

        return calendarConfigDetailDao.listRecords(query);
    }

    private void listDetail(Long year, Map<Long, CalendarConfigDetailDO> res, List<CalendarConfigRangeDO> configRanges) {
        if (CollectionUtils.isEmpty(configRanges)) {
            return;
        }
        for (CalendarConfigRangeDO configRangeDO : configRanges) {
            //获取该配置生效时间内的日历配置明细
            Function<CalendarConfigRangeDO, List<CalendarConfigDetailDO>> function = listCalendarConfigDetails(
                    year,
                    CalendarConfigRangeDO::getAttendanceConfigId,
                    CalendarConfigRangeDO::getStartDate,
                    CalendarConfigRangeDO::getEndDate
            );
            List<CalendarConfigDetailDO> configDetails = function.apply(configRangeDO);
            putRes(res, configDetails);
        }
    }

    private void putRes(Map<Long, CalendarConfigDetailDO> res, List<CalendarConfigDetailDO> configDetails) {
        for (CalendarConfigDetailDO configDetailDO : configDetails) {
            if (!res.containsKey(configDetailDO.getDayId())) {
                res.put(configDetailDO.getDayId(), configDetailDO);
            }
        }
    }

    /**
     * 获取考勤detail
     *
     * @param year
     * @param idGetter
     * @param startGetter
     * @param endGetter
     * @param <T>
     * @return
     */
    private <T> Function<T, List<CalendarConfigDetailDO>> listCalendarConfigDetails(Long year,
                                                                                    Function<T, Long> idGetter,
                                                                                    Function<T, Date> startGetter,
                                                                                    Function<T, Date> endGetter) {
        return bean -> {
            CalendarConfigDetailQuery query = new CalendarConfigDetailQuery();
            Long configId = idGetter.apply(bean);
            query.setCalendarConfigId(configId);
            query.setStartTime(startGetter.apply(bean));
            query.setEndTime(endGetter.apply(bean));
            query.setYear(year);
            return calendarConfigDetailDao.listRecords(query);
        };
    }


    private List<CalendarConfigDetailDO> getDefault(Long year, Long calendarConfigId) {
        CalendarConfigDetailQuery query = new CalendarConfigDetailQuery();
        query.setCalendarConfigId(calendarConfigId);
        query.setYear(year);
        return calendarConfigDetailDao.listRecords(query);
    }

    /**
     * 获取当前生效的日历配置id
     *
     * @param userId
     * @param punchTime
     * @return
     */
    private Long getEffectiveCalendarConfigId(Long userId, Date punchTime) {
        List<AttendanceUser> userList = userService.listUsersByIds(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(userList)) {
            return null;
        }
        //获取用户的日历配置范围列表
        CalendarConfigRangeQuery rangeQuery = CalendarConfigRangeQuery.builder()
                .bizId(userId)
                .startTime(punchTime)
                .endTime(punchTime)
                .build();
        List<CalendarConfigRangeDO> configRanges = calendarConfigRangeDao.listAllRecords(rangeQuery);
        if (CollectionUtils.isNotEmpty(configRanges)) {
            //获取该模版对应的日历配置id
            return configRanges.get(0).getAttendanceConfigId();
        }
        return null;
    }

    private CalendarConfigDetailDO getCalendarConfigDetail(Long calendarConfigId, Long dayId) {
        CalendarConfigDetailQuery query = new CalendarConfigDetailQuery();
        query.setCalendarConfigId(calendarConfigId);
        query.setDayId(dayId);
        List<CalendarConfigDetailDO> details = calendarConfigDetailDao.listRecords(query);
        return CollectionUtils.isEmpty(details) ? null : details.get(0);
    }

    @Override
    public CalendarLegalLeaveConfigDO getLegalLeaveByCalendarConfigIdAndDayId(Long calendarConfigId, Long dayId) {
        return calendarLegalLeaveConfigDao.getByCalendarConfigIdAndDayId(calendarConfigId, dayId);
    }

    @Override
    public List<CalendarLegalLeaveConfigDO> selectListByConfigIdsAndYear(List<Long> calendarConfigIds, List<Integer> years) {
        return calendarLegalLeaveConfigDao.selectListByConfigIdsAndYear(calendarConfigIds, years);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void userCalendarRangeUpdate(List<CalendarConfigRangeDO> updateList, List<CalendarConfigRangeDO> addList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            calendarConfigRangeDao.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            calendarConfigRangeDao.saveBatch(addList);
        }
    }

    @Override
    public List<CalendarConfigModifyDTO> selectAllByBizId(Long userId) {
        List<CalendarConfigRangeDO> calendarConfigRangeDOList = calendarConfigRangeDao.selectAllByBizId(userId);
        if (CollectionUtils.isEmpty(calendarConfigRangeDOList)) {
            return Collections.emptyList();
        }
        List<Long> attendanceConfigIdList = calendarConfigRangeDOList.stream().map(CalendarConfigRangeDO::getAttendanceConfigId).distinct().collect(Collectors.toList());
        Map<Long, CalendarConfigDO> calendarConfigMap = calendarConfigDao.getByCalendarConfigIds(attendanceConfigIdList)
                .stream().collect(Collectors.toMap(CalendarConfigDO::getId, Function.identity()));
        return calendarConfigRangeDOList.stream().map(range->{
            CalendarConfigModifyDTO modifyDTO = new CalendarConfigModifyDTO();
            modifyDTO.setCalendarName(calendarConfigMap.getOrDefault(range.getAttendanceConfigId(),new CalendarConfigDO()).getAttendanceConfigName());
            modifyDTO.setStartDate(range.getStartDate());
            modifyDTO.setEndDate(range.getEndDate());
            modifyDTO.setCreateUserName(range.getCreateUserName());
            return modifyDTO;
        }).collect(Collectors.toList());
    }
}
