package com.imile.attendance.driver;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.DriverAttendanceOperationTypeEnum;
import com.imile.attendance.enums.DriverAttendanceTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverAttendanceDetailDao;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverAttendanceOperateRecordDao;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@Slf4j
@Component
public class DriverAttendanceManage {

    @Resource
    private DriverAttendanceDetailDao driverAttendanceDetailDao;
    @Resource
    protected DefaultIdWorker defaultIdWorker;
    @Resource
    private DriverPunchRecordManage hrmsDriverPunchRecordManage;
    @Resource
    private DriverAttendanceOperateRecordDao driverAttendanceOperateRecordDao;


    @Transactional(rollbackFor = Exception.class)
    public void batchSaveOrUpdateDriverAttendanceDetail(List<DriverAttendanceDetailDO> addDriverAttendanceDetailInfoList,
                                                        List<DriverAttendanceDetailDO> delDriverAttendanceDetailInfoList,
                                                        DriverAttendanceOperateRecordDO driverAttendanceOperateRecord) {
        if (CollectionUtils.isNotEmpty(delDriverAttendanceDetailInfoList)) {
            driverAttendanceDetailDao.updateBatchById(delDriverAttendanceDetailInfoList);
        }
        if (CollectionUtils.isNotEmpty(addDriverAttendanceDetailInfoList)) {
            driverAttendanceDetailDao.saveBatch(addDriverAttendanceDetailInfoList);
        }
        if (ObjectUtil.isNotNull(driverAttendanceOperateRecord)) {
            driverAttendanceOperateRecordDao.save(driverAttendanceOperateRecord);
        }
    }

    public List<DriverAttendanceDetailDO> queryDriverAttendanceByCondition(DriverAttendanceDetailInfoQuery query){
        return driverAttendanceDetailDao.queryDriverAttendanceByCondition(query);
    }

    /**
     * 构建司机考勤数据
     *
     * @param dayId                             每一天
     * @param userCode                          用户编码
     * @param locationCountry                   常驻国
     * @param addDriverAttendanceDetailInfoList 需要删除的司机考勤数据
     * @param delDriverAttendanceDetailInfoList 需要新增的司机考勤数据
     */
    public void buildDriverAttendanceDetailData(Long dayId,
                                                String userCode,
                                                String locationCountry,
                                                List<DriverAttendanceDetailDO> addDriverAttendanceDetailInfoList,
                                                List<DriverAttendanceDetailDO> delDriverAttendanceDetailInfoList){
        DateTime dateTime = DateUtil.beginOfDay(DateUtil.parse(dayId.toString(), DatePattern.PURE_DATE_PATTERN));
        // 这边时间就不需要转换时区，因为你修改考勤修改的是哪一天就是哪一天
        int year = DateUtil.year(dateTime);
        int month = DateUtil.month(dateTime) + 1;
        int day = DateUtil.dayOfMonth(dateTime);
        Long locusPunchNumber = 0L;
        Date locusLocalDateTime = null;
        Long dldSignNumber = 0L;
        Date dldLocalDateTime = null;
        boolean flag = false;

        // 1. 查询司机考勤数据
        DriverAttendanceDetailInfoQuery query = DriverAttendanceDetailInfoQuery.builder().dayId(dayId).userCode(userCode).build();
        List<DriverAttendanceDetailDO> driverAttendanceDetailList = driverAttendanceDetailDao.queryDriverAttendanceByCondition(query);
        if (CollectionUtils.isNotEmpty(driverAttendanceDetailList)) {
            // 删除旧的司机考勤数据
            driverAttendanceDetailList.forEach(item -> {
                item.setIsDelete(BusinessConstant.Y);
                BaseDOUtil.fillDOUpdate(item);
                delDriverAttendanceDetailInfoList.add(item);
            });
        }
        DriverPunchRecordDetailQuery driverPunchRecordDetailQuery = new DriverPunchRecordDetailQuery();
        driverPunchRecordDetailQuery.setDayId(dayId);
        driverPunchRecordDetailQuery.setUserCode(userCode);
        // 获取司机打卡记录
        List<DriverPunchRecordDO> driverPunchRecordList = hrmsDriverPunchRecordManage.selectPunchRecordDetail(driverPunchRecordDetailQuery);
        Map<Integer, List<DriverPunchRecordDO>> operationTypeMap = driverPunchRecordList.stream()
                .collect(Collectors.groupingBy(DriverPunchRecordDO::getOperationType));
        if (CollectionUtils.isNotEmpty(driverPunchRecordList)) {
            // 计算考勤之前，需要先看打卡记录里面是否存在轨迹打卡以及签收的记录，如果存在，那么就需要先暂存轨迹打卡次数以及签收次数，方便后面计算考勤的时候，存入考勤数据
            for (DriverPunchRecordDO driverPunchRecord : driverPunchRecordList) {
                if (ObjectUtil.equal(driverPunchRecord.getOperationType(), DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType())) {
                    locusPunchNumber = driverPunchRecord.getNumber();
                }
                if (ObjectUtil.equal(driverPunchRecord.getOperationType(), DriverAttendanceOperationTypeEnum.DLD_SIGN.getType())) {
                    dldSignNumber = driverPunchRecord.getNumber();
                }
            }

            // 按照创建时间倒序排序，获取最后一次操作的打卡记录
            List<DriverPunchRecordDO> lastDriverRecordList = driverPunchRecordList.stream()
                    .sorted(Comparator.comparing(DriverPunchRecordDO::getCreateDate).reversed())
                    .collect(Collectors.toList());
            // 获取最后一次创建的打卡记录
            DriverPunchRecordDO driverPunchRecordInfo = lastDriverRecordList.get(0);
            if (ObjectUtil.isNotNull(driverPunchRecordInfo)) {
                // 如果最新的一条数据数据是修改考勤，那么就生成修改考勤的数据
                if (ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.MODIFY_ATTENDANCE.getType())) {
                    buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, driverPunchRecordInfo.getModifyAttendanceType(),
                            dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                    return;
                }
                // 如果最新的一条数据数据是请假，那么就生成请假的考勤数据
                if (ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.LEAVE.getType())) {
                    buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.LEAVE.getType(),
                            dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                    return;
                }
                // 如果最新的一条数据数据是轨迹打卡或者DLD签收，那么就生成轨迹打卡 + DLD签收的考勤数据
                if (ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType())
                        || ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.DLD_SIGN.getType())) {
                    // 获取轨迹打卡的打卡记录
                    List<DriverPunchRecordDO> daLocusPunch = operationTypeMap.get(DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType());
                    // 获取DLD签收的打卡记录
                    List<DriverPunchRecordDO> dldSign = operationTypeMap.get(DriverAttendanceOperationTypeEnum.DLD_SIGN.getType());
                    // 如果轨迹打卡和DLD签收都不为空，那么就计算轨迹打卡和DLD签收的考勤数据，其他情况都是异常数据，记录考勤异常
                    if (CollectionUtils.isNotEmpty(daLocusPunch)) {
                        DriverPunchRecordDO daLocusRecord = daLocusPunch.get(0);
                        if (ObjectUtil.isNotNull(daLocusRecord)) {
                            locusPunchNumber = daLocusRecord.getNumber();
                            // 这里使用创建时间来记录司机考勤里面的最近操作记录【最近操作记录其实就是最后创建的时间】operatingTime 字段暂时无用
                            locusLocalDateTime = daLocusRecord.getCreateDate();
                            flag = true;
                        }
                    }
                    if (CollectionUtils.isNotEmpty(dldSign)) {
                        DriverPunchRecordDO dldSignRecord = dldSign.get(0);
                        if (ObjectUtil.isNotNull(dldSignRecord)) {
                            dldSignNumber = dldSignRecord.getNumber();
                            dldLocalDateTime = dldSignRecord.getCreateDate();
                            flag = true;
                        }
                    }
                }
                // 如果都不是，那么就是异常数据，记录考勤异常
                if (flag) {
                    // mex特殊逻辑
                    if (ObjectUtil.equal(locationCountry, CountryCodeEnum.MEX.getCode())) {
                        // 如果国家是mex，只要有轨迹打卡就算满勤
                        if (locusPunchNumber.compareTo(1L) >= 0 || dldSignNumber.compareTo(1L) >= 0) {
                            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.PRESENT.getType(),
                                    dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                        } else {
                            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.ABSENT.getType(),
                                    dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                        }
                    } else {
                        // 其他国家
                        if (dldSignNumber.compareTo(1L) >= 0 && locusPunchNumber.compareTo(1L) >= 0) {
                            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.PRESENT.getType(),
                                    dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                        } else {
                            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.ABSENT.getType(),
                                    dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                        }
                    }
                }
            }
        } else {
            log.info("司机账号：{}，时间：{}，不存在司机打卡记录,所以创建缺勤打卡记录", userCode, dayId);
            DriverPunchRecordDO driverPunchRecordInfo = new DriverPunchRecordDO();
            driverPunchRecordInfo.setOperationType(DriverAttendanceOperationTypeEnum.DEFAULT.getType());
            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.ABSENT.getType(),
                    dldSignNumber, locusPunchNumber, null, null, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
        }
    }


    public void saveOperateRecord(DriverAttendanceOperateRecordDO driverAttendanceOperateRecord){
        if (ObjectUtil.isNotNull(driverAttendanceOperateRecord)) {
            driverAttendanceOperateRecordDao.save(driverAttendanceOperateRecord);
        }
    }


    /**
     * 生成司机考勤数据
     *
     * @param userCode                          司机账号
     * @param year                              年
     * @param month                             月
     * @param day                               日
     * @param dateTime                          日期
     * @param dayId                             day_id
     * @param attendanceType                    考勤类型
     * @param dldSignNumber                     DLD签收次数
     * @param locusPunchNumber                  轨迹打卡次数
     * @param dldLocalDateTime                  DLD签收时间
     * @param locusLocalDateTime                轨迹打卡时间
     * @param driverPunchRecordInfo             司机打卡记录
     * @param addDriverAttendanceDetailInfoList 新增司机考勤信息
     */
    private void buildDriverAttendanceData(String userCode, int year, int month, int day, Date dateTime, Long dayId,
                                           Integer attendanceType, Long dldSignNumber, Long locusPunchNumber, Date dldLocalDateTime,
                                           Date locusLocalDateTime, DriverPunchRecordDO driverPunchRecordInfo,
                                           List<DriverAttendanceDetailDO> addDriverAttendanceDetailInfoList) {

        DriverAttendanceDetailDO detail = new DriverAttendanceDetailDO();
        detail.setId(defaultIdWorker.nextId());
        detail.setUserCode(userCode);
        detail.setYear(year);
        detail.setMonth(month);
        detail.setDay(day);
        detail.setDate(dateTime);
        detail.setDayId(dayId);
        detail.setAttendanceType(attendanceType);
        detail.setDldNumber(dldSignNumber);
        detail.setLocusNumber(locusPunchNumber);
        // 如果是修改考勤或者请假，最近操作时间就是操作时间
        if (ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.LEAVE.getType()) ||
                ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.MODIFY_ATTENDANCE.getType())) {
            // driverPunchRecordInfo.getOperatingTime() 这个操作时间字段暂时没用
            //if (ObjectUtil.isNotNull(driverPunchRecordInfo.getOperatingTime())) {
            //    detail.setLastOperatingTime(DateUtil.parse(driverPunchRecordInfo.getOperatingTime().toString(), DatePattern.NORM_DATETIME_PATTERN));
            //}
            detail.setLastOperatingTime(driverPunchRecordInfo.getCreateDate());
        } else {
            if (ObjectUtil.isNotNull(dldLocalDateTime) && ObjectUtil.isNotNull(locusLocalDateTime)) {
                if (dldLocalDateTime.after(locusLocalDateTime)) {
                    detail.setLastOperatingTime(dldLocalDateTime);
                } else {
                    detail.setLastOperatingTime(locusLocalDateTime);
                }
            }else {
                detail.setLastOperatingTime(ObjectUtil.isNotNull(dldLocalDateTime) ?
                        dldLocalDateTime :
                        (ObjectUtil.isNotNull(locusLocalDateTime) ? locusLocalDateTime : null));

            }
        }

        BaseDOUtil.fillDOInsert(detail);
        addDriverAttendanceDetailInfoList.add(detail);
    }
}
