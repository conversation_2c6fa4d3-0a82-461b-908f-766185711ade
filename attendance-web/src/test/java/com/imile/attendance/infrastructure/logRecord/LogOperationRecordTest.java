package com.imile.attendance.infrastructure.logRecord;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.log.dao.LogOperationRecordDao;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
public class LogOperationRecordTest extends BaseTest {

    @Resource
    private LogOperationRecordDao logOperationRecordDao;

    @Test
    public void test() {
        List<LogOperationRecordDO> list = logOperationRecordDao.list();
        System.out.println(list);
    }
}
