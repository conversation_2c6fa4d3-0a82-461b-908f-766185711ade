package com.imile.attendance.common;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.util.DateHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DateHelper工具类的单元测试
 * <AUTHOR> Assistant
 * @Date 2025/4/4
 * @Description
 */
public class DateHelperTest {

    /**
     * 测试获取年份
     */
    @Test
    public void testYear() {
        Date date = DateUtil.parse("2023-05-15");
        Assertions.assertEquals(2023, DateHelper.year(date));
    }

    /**
     * 测试获取月份
     */
    @Test
    public void testMonth() {
        Date date = DateUtil.parse("2023-05-15");
        assertEquals(5, DateHelper.month(date));
    }

    /**
     * 测试获取日期是当月的第几天
     */
    @Test
    public void testDayOfMonth() {
        Date date = DateUtil.parse("2023-05-15");
        assertEquals(15, DateHelper.dayOfMonth(date));
    }

    /**
     * 测试将日期转为dayId
     */
    @Test
    public void testGetDayId() {
        Date date = DateUtil.parse("2023-05-15");
        assertEquals(20230515, DateHelper.getDayId(date));
    }

    /**
     * 测试将Long类型dayId转换为date
     */
    @Test
    public void testTransferLongDayIdToDate() {
        Long dayId = 20230515L;
        Date date = DateHelper.transferDayIdToDate(dayId);
        assertNotNull(date);
        assertEquals("2023-05-15", DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
        
        // 测试null值
        assertNull(DateHelper.transferDayIdToDate((Long) null));
    }

    /**
     * 测试将String类型dayId转换为date
     */
    @Test
    public void testTransferStringDayIdToDate() {
        String dayId = "20230515";
        Date date = DateHelper.transferDayIdToDate(dayId);
        assertNotNull(date);
        assertEquals("2023-05-15", DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
        
        // 测试空字符串
        assertNull(DateHelper.transferDayIdToDate(""));
        // 测试null值
        assertNull(DateHelper.transferDayIdToDate((String) null));
    }

    /**
     * 测试将打卡时间添加默认日期前缀
     */
    @Test
    public void testAppendDefaultDateStrToTimeStr() {
        String timeStr = "08:30:00";
        String result = DateHelper.appendDefaultDateStrToTimeStr(timeStr);
        assertEquals("1970-01-01 08:30:00", result);
        
        // 测试空字符串
        assertEquals("", DateHelper.appendDefaultDateStrToTimeStr(""));
        // 测试null值
        assertEquals("", DateHelper.appendDefaultDateStrToTimeStr(null));
    }

    /**
     * 测试将打卡时间添加默认日期前缀并转为Date
     */
    @Test
    public void testAppendDefaultDateToTime() {
        String timeStr = "08:30:00";
        Date result = DateHelper.appendDefaultDateToTime(timeStr);
        assertNotNull(result);
        assertEquals("1970-01-01 08:30:00", DateUtil.format(result, DatePattern.NORM_DATETIME_PATTERN));
        
        // 测试空字符串
        assertNull(DateHelper.appendDefaultDateToTime(""));
        // 测试null值
        assertNull(DateHelper.appendDefaultDateToTime(null));
    }

    /**
     * 测试判断两个时间是否跨天
     */
    @Test
    public void testJudgeCrossDay() {
        // 不跨天的情况
        Date firstTime = DateUtil.parse("2023-05-15 08:00:00");
        Date secondTime = DateUtil.parse("2023-05-15 18:00:00");
        assertEquals(BusinessConstant.N, DateHelper.judgeCrossDay(firstTime, secondTime));
        
        // 跨天的情况（第一个时间在第二个时间之后）
        Date laterTime = DateUtil.parse("2023-05-16 08:00:00");
        assertEquals(BusinessConstant.Y, DateHelper.judgeCrossDay(laterTime, firstTime));
    }

    /**
     * 测试格式化时间为HH:mm:ss
     */
    @Test
    public void testFormatHHMMSS() {
        Date date = DateUtil.parse("2023-05-15 08:30:45");
        assertEquals("08:30:45", DateHelper.formatHHMMSS(date));
        
        // 测试null值
        assertEquals("", DateHelper.formatHHMMSS(null));
    }

    /**
     * 测试格式化日期为yyyy-MM-dd
     */
    @Test
    public void testFormatYYYYMMDD() {
        Date date = DateUtil.parse("2023-05-15 08:30:45");
        assertEquals("2023-05-15", DateHelper.formatYYYYMMDD(date));
        
        // 测试null值
        assertEquals("", DateHelper.formatYYYYMMDD(null));
    }

    /**
     * 测试格式化日期时间为yyyy-MM-dd HH:mm:ss
     */
    @Test
    public void testFormatYYYYMMDDHHMMSS() {
        Date date = DateUtil.parse("2023-05-15 08:30:45");
        assertEquals("2023-05-15 08:30:45", DateHelper.formatYYYYMMDDHHMMSS(date));
        
        // 测试null值
        assertEquals("", DateHelper.formatYYYYMMDDHHMMSS(null));
    }

    /**
     * 测试连接日期和时间字符串
     */
    @Test
    public void testConcatDateAndTimeStr() {
        String dateStr = "2023-05-15";
        String timeStr = "08:30:45";
        assertEquals("2023-05-15 08:30:45", DateHelper.concatDateAndTimeStr(dateStr, timeStr));
    }

    /**
     * 测试解析yyyy-MM-dd HH:mm:ss格式的日期时间字符串
     */
    @Test
    public void testParseYYYYMMDDHHMMSS() {
        String dateStr = "2023-05-15 08:30:45";
        Date date = DateHelper.parseYYYYMMDDHHMMSS(dateStr);
        assertNotNull(date);
        assertEquals(dateStr, DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN));
        
        // 测试空字符串
        assertNull(DateHelper.parseYYYYMMDDHHMMSS(""));
        // 测试null值
        assertNull(DateHelper.parseYYYYMMDDHHMMSS(null));
    }

    /**
     * 测试日期推移
     */
    @Test
    public void testPushDate() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse("2023-05-15");
        
        // 向后推1天
        Date pushed = DateHelper.pushDate(date, 1);
        assertEquals("2023-05-16", sdf.format(pushed));
        
        // 向前推1天
        pushed = DateHelper.pushDate(date, -1);
        assertEquals("2023-05-14", sdf.format(pushed));
        
        // 测试null值，应该使用当前日期
        Date now = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);
        cal.add(Calendar.DATE, 1);
        Date tomorrow = cal.getTime();
        
        Date pushedNow = DateHelper.pushDate(null, 1);
        // 由于测试运行时间可能有微小差异，只比较日期部分
        assertEquals(sdf.format(tomorrow), sdf.format(pushedNow));
    }

    /**
     * 测试根据时区转换日期
     */
    @Test
    public void testConvertDateByTimeZonePlus() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = sdf.parse("2023-05-15 12:00:00");
        
        // 测试默认时区（如果不提供时区，默认使用8）
        Date convertedDefault = DateHelper.convertDateByTimeZonePlus(null, date);
        assertNotNull(convertedDefault);
        
        // 测试不同时区
        Date convertedDubai = DateHelper.convertDateByTimeZonePlus("4", date);
        assertNotNull(convertedDubai);
        
        // 测试负时区
        Date convertedNegative = DateHelper.convertDateByTimeZonePlus("-5", date);
        assertNotNull(convertedNegative);
        
        // 测试null日期
        assertNull(DateHelper.convertDateByTimeZonePlus("8", null));
    }
}
