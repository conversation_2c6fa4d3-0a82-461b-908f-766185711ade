package com.imile.attendance.rpc;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.hermes.RpcHermesEntOcClient;
import com.imile.attendance.hermes.support.RpcHermesEntOcClientSupport;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class RpcHermesEntOcClientTest extends BaseTest {

    @Mock
    private RpcHermesEntOcClient rpcHermesEntOcClient;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RMapCache<String, EntOcApiDTO> cache;

    @InjectMocks
    private RpcHermesEntOcClientSupport rpcHermesEntOcClientSupport;

    @BeforeEach
    void setUp() {
        when(redissonClient.getMapCache(Constants.CacheKey.OC_CACHE_KEY))
                .thenReturn((RMapCache) cache); // 强制类型转换
    }

    @Test
    void testGetOcByCode_CacheHit() {
        // Arrange
        String ocCode = "OC001";
        EntOcApiDTO expected = new EntOcApiDTO();
        when(cache.get(ocCode)).thenReturn(expected);

        // Act
        EntOcApiDTO actual = rpcHermesEntOcClientSupport.getOcByCode(ocCode);

        // Assert
        assertEquals(expected, actual);
        verify(cache).get(ocCode);
    }

    @Test
    void testGetOcByCode_CacheMiss() {
        // Arrange
        String ocCode = "OC001";
        EntOcApiDTO expected = new EntOcApiDTO();
        when(cache.get(ocCode)).thenReturn(null);
        when(rpcHermesEntOcClient.getOcByCode(Constants.ORG_ID, ocCode)).thenReturn(expected);

        // Act
        EntOcApiDTO actual = rpcHermesEntOcClientSupport.getOcByCode(ocCode);

        // Assert
        assertEquals(expected, actual);
        verify(cache).get(ocCode);
        verify(rpcHermesEntOcClient).getOcByCode(Constants.ORG_ID, ocCode);
        verify(cache).fastPut(ocCode, expected, 5, TimeUnit.MINUTES);
    }

    @Test
    void testGetOcByCode_NullInput() {
        // Act
        EntOcApiDTO actual = rpcHermesEntOcClientSupport.getOcByCode(null);

        // Assert
        assertNull(actual);
    }

    @Test
    void testGetOcByCodes_EmptyInput() {
        // Act
        List<EntOcApiDTO> actual = rpcHermesEntOcClientSupport.getOcByCodes(Collections.emptyList());

        // Assert
        assertTrue(actual.isEmpty());
    }

    @Test
    void testGetOcByCodes_PartialCacheHit() {
        // Arrange
        String ocCode1 = "OC001";
        String ocCode2 = "OC002";
        EntOcApiDTO cached = new EntOcApiDTO();
        EntOcApiDTO rpcResult = new EntOcApiDTO();
        when(cache.get(ocCode1)).thenReturn(cached);
        when(cache.get(ocCode2)).thenReturn(null);
        when(rpcHermesEntOcClient.getOcByCodes(Constants.ORG_ID, Collections.singletonList(ocCode2)))
                .thenReturn(Collections.singletonList(rpcResult));

        // Act
        List<EntOcApiDTO> actual = rpcHermesEntOcClientSupport.getOcByCodes(Arrays.asList(ocCode1, ocCode2));

        // Assert
        assertEquals(2, actual.size());
        assertTrue(actual.contains(cached));
        assertTrue(actual.contains(rpcResult));
        verify(cache).get(ocCode1);
        verify(cache).get(ocCode2);
        verify(rpcHermesEntOcClient).getOcByCodes(Constants.ORG_ID, Collections.singletonList(ocCode2));
//        verify(cache).fastPut(ocCode2, rpcResult, 5, TimeUnit.MINUTES);
    }

    @Test
    void testGetOcMapByCodes() {
        // Arrange
        String ocCode1 = "OC001";
        String ocCode2 = "OC002";
        EntOcApiDTO dto1 = new EntOcApiDTO();
        dto1.setOcCode(ocCode1);
        EntOcApiDTO dto2 = new EntOcApiDTO();
        dto2.setOcCode(ocCode2);
        when(cache.get(ocCode1)).thenReturn(dto1);
        when(cache.get(ocCode2)).thenReturn(dto2);

        // Act
        Map<String, EntOcApiDTO> actual = rpcHermesEntOcClientSupport.getOcMapByCodes(Arrays.asList(ocCode1, ocCode2));

        // Assert
        assertEquals(2, actual.size());
        assertEquals(dto1, actual.get(ocCode1));
        assertEquals(dto2, actual.get(ocCode2));
    }
}
