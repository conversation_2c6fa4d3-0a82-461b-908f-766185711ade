exception.commonMessage=System Error,Please contact administrator.
exception.paramValidError=Param Valid Exception
data.not.exist=Data not exist
data.duplicate=Duplicate record
param.null={0} Cannot be empty
none.system.login.permission=None System login permission,Please contact administrator
no.access.auth=None data access permission
max.select.number.over=The maximum number of queries at once cannot exceed 1000
import.field.required=[{0}] required
import.field.length.limit=The [{0}] cannot exceed {1} characters
import.field.data.not.exist=The [{0}] ({1}) does not exist in the system
import.field.data.unavailable=The [{0}] ({1}) is unavailable
import.field.data.duplicate=The [{0}] ({1}) has multiple data entries in the system
import.field.pattern.error=The filling standard for [{0}] is {1}
import.field.associated.required=[{0}] need to be updated simultaneously
import.field.permission.limit=You do not have the permission to update {0}
import.field.value.duplicated=Duplicate [{0}] ({1}) are not allowed in the same document
import.field.unmodifiable=The [{0}] current cannot be modified
import.field.associated.limit=When [{0}] is ({1}), it is required to fill in [{2}] simultaneously

mac.address.already.exists=The mac address already exists
gps.address.already.exists=The gps address already exists
the.city.does.not.exist=The city does not exist

##### 打卡相关 #####
no.present.day.no.need.punch = no need to punch in weekDay or holiday
no.punch.data = no punch config for this employee
punch.on.limit.error = you have punched on today
no.punch.on.error = have not punched on today
punch.range.error = punch range outside
punch.in.time.too.early = punch in time too early
punch.out.time.too.late =  punch out time too late
punch.out.not.permission = punch out not permission
time.stamp.error = time verification error
sign.error = sign verification error
punch.rule.not.configured = The punching rule is not configured
supplement.number.over.limit = The number of supplementary punch-in&out reaches the upper limit:{0}
supplement.days.over.limit = The days of supplementary punch-in&out reaches the upper limit:{0}
punch.over.time.error = only one punch is allow in 15s
attendance.punch.is.empty=attendance punch is empty
user.punch.error=user punch error
version.has.changed=The version has changed. Please refresh the applet version

default.punch.not.exits=A company default clock in rule does not exist, Please configure first
custom.punch.not.close = Cannot close. This clock in rule is used by existing employees
default.punch.not.close = The default clock in rule cannot be closed
class.employee.not.edit = Los empleados que han desactivado las reglas de punzonado por turno ya no pueden editar
punch.type.not.edit = Rule type cannot be changed
class.not.delete = This class is being used and cannot be deleted


#假期相关
company.leave.repeat.error=company leave repeat error
the.start.time.of.a.statutory.holiday.cannot.be.greater.than.the.end.time=the start time of a statutory holiday cannot be greater than the end time
the.name.of.the.statutory.holiday.already.exists=the name of the statutory holiday already exists
if.you.select.Require.a.leave.certificate.the.leave.condition.is.required=if you select Require a leave certificate,the leave condition is required
the.duration.of.statutory.holidays.overlaps.with.those.of.existing.statutory.holidays=the duration of statutory holidays overlaps with those of existing statutory holidays
there.is.a.temporal.overlap.in.statutory.holiday.data=there is a temporal overlap in statutory holiday data
a.one-time.payment.then.the.payment.time.must.be.the.employee's.entry.date=a one-time payment,then the payment time must be the employee's entry date
periodic.release.then.the.release.time.must.be.a.fixed.day.of.the.year.or.a.fixed.day.of.the.month=periodic release,then the release time must be a fixed day of the year or a fixed day of the month
if.the.credit.type.is.collar.increment.range.data.is.required=if the credit type is collar increment,range data is required
fixed.quota.then.the.quota.must.be.filled=if the quota is fixed,then the quota must be filled
if.the.leave.is.permanently.valid.is.No.then.the.validity.period.must.be.filled.in.the.renewal.period.must.be.filled.in.and.whether.the.leave.can.be.carried.forward.must.be.yes=if the leave is permanently valid is No,then the validity period must be filled in,the renewal period must be filled in,and whether the leave can be carried forward must be yes
country.of.dispatch.required=Country of dispatch required
the.name.cannot.contain.the.special.identifier.-Dispatch=The name cannot contain the special identifier "-Dispatch"
the.current.country.does.not.maintain.the.corresponding.nationality.please.maintain.the.nationality.first=The current country does not maintain the corresponding nationality, please maintain the nationality first
the.names.of.statutory.holidays.are.repeated=the names of statutory holidays are repeated
the.year.of.the.holiday.cannot.be.empty=the Statutory Holiday Year parameter cannot be empty
the.fake.details.parameter.cannot.be.empty=the fake details parameter cannot be empty
the.calendar.details.data.year.cannot.be.empty=the calendar details data year cannot be empty
calendar.details.data.cannot.be.empty=calendar details data cannot be empty
leave.name.is.not.empty=Leave name is not empty

the.dtl.account.to.be.changed.does.not.exist=The DTL account to be changed does not exist
new.dtl.account.does.not.exist=New DTL account does not exist
no.da.belong.to.the.dtl=No DA belong to the DTL
must.be.different.dtl=must be different DTL
dtl.must.be.same.station=DTL must be same station
da.must.be.same.station=DA must be same station
approval.status.has.changed=The status has changed. Please refresh and try again
approval.has.unfinished=There are already unfinished approval, please do not submit them again
punch.class.save.not.empty=punch class save not empty
punch.class.not.interval=punch class not interval
user.salary.config.data.error=user salary config data error
the.schedule.off.day.exceeds.the.available.off.day=the schedule off day exceeds the available off day
all.off.days.need.to.be.scheduled=all off days need to be scheduled
off.days.more.than.residue.attendance.day=off days more than residue attendance day

#考勤审批
leave.start.date.not.empty=leave start date not empty
leave.end.date.not.empty=leave end date not empty
expected.leave.time.not.empty=expected leave time not empty
remark.not.empty=remark not empty
exist.clash.time.period=exist clash time period
user.not.have.salary.config=The employee has not configured the payroll program, the attendance cycle is defaulted to the current month, if it is not the current month, please contact HR for processing.
extended.leave.period=Attendance cycle {0}-{1} has ended, no longer support the operation of the application in this time period, please adjust the time to submit
user.not.have.attendance.punch.config=user not have attendance punch config
calendar.is.empty=calendar is empty
punch.class.record.is.empty=punch class record is empty
application.form.is.empty=application form is empty
form.status.not.cancel=form status not cancel
punch.class.config.is.empty=punch class config is empty
out.of.office.start.date.not.empty=out of office start date not empty
out.of.office.end.date.not.empty=out of office end date not empty
abnormal.id.not.empty=abnormal id not empty
abnormal.has.been.handler=abnormal has been handler
abnormal.only.one.day=abnormal only one day
user.not.have.reissue.card.config=user not have reissue card config
user.not.have.attendance.reissue.card.config=user not have attendance reissue card config
user.attendance.reissue.card.count.is.over=user attendance reissue card count is over
abnormal.record.date.not.empty=abnormal record date not empty
user.attendance.day.not.have.cycle=user attendance day not have cycle
attendance.and.legal.working.hours.not.empty=attendance and legal working hours not empty
attendance.hours.not.able=attendance hours not able
reissue.card.not.handler.this.abnormal.type=reissue card not handler this abnormal type
user.day.punch.time.not.exist=user day punch time not exist
attendance.approval.basic.info.empty=attendance approval basic info empty
reissue.card.not.relation.abnormal=reissue card not relation abnormal
revoke.form.relation.form.empty=revoke form relation form empty
not.revoke.repeat=not revoke repeat
only.stage.status.can.be.delete=only stage status can be delete
punch.class.item.is.empty=punch class item is empty
abnormal.in.review=abnormal in review
dept.exist.in.other.attendance.config=dept exist in {0} config
user.exist.in.other.attendance.config=user exist in {0} config
attendance.config.name.exist=attendance config name exist
country.not.have.default.attendance.config=country not have default attendance config
the.leave.end.time.must.be.after.the.start.time=the leave end time must be after the start time
the.leave.balance.is.insufficient=the leave balance is insufficient
the.balance.of.leave.cannot.be.less.than.the.length.of.leave=The balance of leave cannot be less than the length of leave
there.is.no.leave.type.information.for.the.details.of.the.application.form=there is no leave type information for the details of the application form
there.is.no.leave.start.or.end.time.for.the.details.of.the.application.form=there is no leave start or end time for the details of the application form
the.user.lacks.detailed.data.for.the.leave.type=the user lacks detailed data for the leave type
leave.time.cannot.be.zero=leave time cannot be zero
overtime.requests.are.not.allowed.on.weekdays=this time period is normal working hours!
please.do.not.repeat.the.overtime.process=you have already submitted an overtime order, please do not repeat the overtime process!
the.imported.data.is.empty=the imported overtime data is empty
overtime.days.are.not.on.the.same.day=overtime days are not on the same day
warehouse.employee.not.allow.revoke=El personal propio del almacén no apoya la venta de vacaciones por el momento.
warehouse.not.allow.out.of.work=El personal del almacén no apoya el campo por el momento.
warehouse.not.allow.card.revoke=El personal del almacén no apoya la revocación de la tarjeta de reposición por el momento.
leave.day.cannot.be.zero=The end date does not fully cover your schedule (across night shifts), please adjust the end date backwards, thank you!
out.of.office.is.not.allowed.to.take.more.than.7.days=According to company policy, Each out of office request can not exceed 7 working days.
out.of.office.time.can.not.be.zero=out of office time can not be zero
the.leave.duration.cannot.be.less.than.the.minimum.leave.duration.of.the.leave.type=The leave duration cannot be less than the minimum leave duration of the leave type

scheduling.limit = Mexican warehouse labor staff restriction page scheduling
monthly.report.push.is.required=monthly report push is required
attendance.cycle.data.repeat=data on the scope of the population or type of employment and the existing attendance cycle are duplicated
attendance.cycle.custom.end.date.lack=customize the cycle frequency, and the end time of the cycle range is required
attendance.cycle.no.exists=attendance cycle records do not exist
attendance.cycle.status.no.change=the attendance cycle status does not change
attendance.cycle.config.have.exists={0} country already has an {1} attendance cycle, please check and set it again!
user.not.have.attendance.cycle.config=The employee's country doesn't have an attendance cycle plan configured, the attendance cycle is defaulted to the current month, if it is not the current month, please contact HR for processing.
attendance.cycle.config.get.cycle.type.enum=The attendance cycle type cannot be null

user.work.info.not.exits=Employee information is incomplete and cannot onboard
user.certificate.duplicated=Certificate is duplicated, please review the certificate details
bpm.approval.error=create bpm approval error
preview.bpm.approval.error=preview bpm approval error
grade.is.used=There are staffs under this rank type. Please switch all staffs under this rank type before deactivating it
dept.is.disabled=This department has been deactivated, please reselect it.
offer.letter.upload.limit=The number of offer letter exceeds the limit
only.pause.can.resume=Only by pausing can it be resumed.
dept.disabled=This department has been deactivated, please reselect it.

face.query.oss.file=Falló el enlace de descarga del archivo de consulta basado en OSS filekey
face.url.convert.inputStream.failed=Remote URL conversion to input stream failed
the.branch.administrator.already.exists=Administrdor existido, no añada repetidamente
the.person.has.resigned=esta persona despedito, contacte primero HR para la incorporación
this.account.has.been.deactivated=Cuenta desactivada, contacte primero HR para solucionar
the.user.account.does.not.exist=No existe este usuario/ID
not.recognition.or.upload.cannot.in={0} No pueden entrar sin pasar la cara 
not.in.cannot.out={0} No entrada en el almacén, no puede salir
not.recognition.cannot.out={0} No escanear la cara, no salir de almacén
in.cannot.in={0} ya entrado a almacén, no pueden entrar otra vez
out.other.oc.warehouse.then.in=La persona ya ha ingresado al almacén en la estación [{0}]. Por favor, salga del almacén primero y luego elija una nueva estación para ingresar
after.out.then.in=Existen registros pendientes de salida del almacén, por favor realiza la salida antes de proceder con la entrada
abnormal.attendance.days.obtained=anormal de calcular dias de asistencia
face.in.first=Pase su cara para entrar y salir del almacén primero
attendance.group.not.config=Contacta al HRBP para configurar la información del grupo de asistencia
out.warehouse.then.in=La persona ya ha entrado en el almacén con [{0}], por favor salga del almacén antes de seleccionar un nuevo turno para entrar en el almacén
ocr.scanning.failed=Error de escaneado, vuelva a escanear o introduzca
warehouse.entry.failure=Error al ingresar, empleado no perteneciente a Maquila
duplicate.ID.number=Número de identificación duplicado, no se puede registrar
unsupported.countries=Unsupported countries
update.failed.non.labor.dispatch.employee=Falla en la actualización, empleado que no está en el despacho
face.engine.active.failed=Falló la activación del motor facial
face.engine.initialize.failed=Falló la inicialización del motor facial
face.engine.obtain.failed=Error en la adquisición del motor frontal
face.detect.failed=Error en la detección de rostros
face.feature.extract.failed=Falló la extracción de rasgos faciales
face.feature.compare.failed=La comparación de rasgos faciales falló
face.feature.library.matching.failed=La base de datos de características faciales coincide de manera anormal. Primero inicialice los datos
please.initialize.face.feature.library.first=Primero inicialice los datos de la base de datos de rostros
face.feature.data.save.failed=No se pudieron guardar los datos de rasgos faciales
face.liveness.detect.failed=Falló la detección de vida de la cara
the.result.of.face.liveness.detection.non.living=Resultado de la detección de vitalidad facial: no es un ente vivo
the.result.of.face.liveness.detection.unknown=Se desconoce el resultado de la detección de actividad facial
the.result.of.face.liveness.detection.beyond.the.face=Los resultados de detección de actividad facial superan la cara
facial.data.has.been.deleted=Los datos faciales se han eliminado, borre el caché simultáneamente
version.outdated=La versión del applet wpm está atrasada, por favor actualice a tiempo
driver.cant.use.wpm=El conductor no puede usar el wpm, por favor use la función de asistencia del conductor
cant.change.vendor=El personal ha estado trabajando bajo el proveedor hoy, [{0}] no se puede reemplazar
quick.out.must.in.24.hours=Se permite tomar fotos fuera del almacén dentro de las 24 horas del tiempo de almacenamiento
before.class.earliestPunchInTime=La hora de registro más temprana para el turno no está disponible. Favor de seleccionar un nuevo turno. Si no hay un turno adecuado, comuníquese con HRBP para la configuración
vendor.classes.confirm.repeat=El resultado del turn actual de maquilla ha sido confirmado, por favor no repita la operación.
warehouse.no.attendance.record=No hay registro de asistencia de personal en el almacén.
not.yet.the.end.of.shift=No se puede confirmar antes de la hora de salida del turno
warehouse.attendance.config.name.exist=Ya existe el nombre de la regla de gestión de asistencia al almacén
dept.exist.in.other.warehouse.attendance.config=El departamento ya existe dentro de las reglas de gestión de asistencia del almacén.
already.working.in.the.warehouse=No ha podido registrar la entrada al almacén, la persona ya ha sido registrado hoy y no puede volver a entrar a trabajar en el almacén.
already.working.out.the.warehouse=No se ha podido registrar la salida del almacén, la persona ya ha sido registrado hoy y no puede volver a entrar a trabajar en el almacén.
non.warehouse.employees.cant.use.wpm=Las personas que no están en el almacén no pueden entrar en el almacén para operar.

#人脸识别
face.recognition.duplicate.facial=Duplicate facial photos, duplicate personnel account is {0}