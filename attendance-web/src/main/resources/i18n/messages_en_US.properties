success=success
request.handle.fail=request handle fail
no.access=no access
param.validate.fail=param validate fail
system.processing.exception=system processing exception
Network.exception=Network exception
param.not.exists=param not exists
http.status.not.200=http status not 200
access.token.not.found=access token not found
limited.by.third=limited by third
system.error=System Error,Please contact administrator
data.duplicate=data duplicate
exception.paramValidError=Param Valid Exception
data.not.exist=Data not exist
none.system.login.permission=None System login permission,Please contact administrator
no.access.auth=None data access permission


repeat.submit=repeat submit
account.not.exist=account does not exist
country.not.exists=country not exists
account.repeat.handler.error=being processed, please wait ...
param.not.null=param can not be null

country.not.empty=country not empty

log.record.module.not.exist=Log record module not exist or not support

zkteco.area.and.terminal.sn.not.exist=zkteco area and terminal sn not exist
punch.card.version.behind.please.refresh.in.time=punch card version behind, please refresh in time
export.time.range.cannot.exceed.one.month=export time range cannot exceed one month

attendance.cycle.no.exists=attendance cycle records do not exist
attendance.cycle.status.no.change=the attendance cycle status does not change
attendance.cycle.country.not.support.week= {0} country does not support weekly attendance cycle
attendance.cycle.config.have.exists={0} country already has an {1} attendance cycle, please check and set it again!
user.not.have.attendance.cycle.config=The employee's country doesn't have an attendance cycle plan configured, the attendance cycle is defaulted to the current month, if it is not the current month, please contact HR for processing.

leave.start.date.not.empty=leave start date not empty
leave.end.date.not.empty=leave end date not empty
expected.leave.time.not.empty=expected leave time not empty
remark.not.empty=remark not empty
exist.clash.time.period=exist clash time period

extended.leave.period=Attendance cycle {0}-{1} has ended, no longer support the operation of the application in this time period, please adjust the time to submit
attendance.cycle.config.get.cycle.type.enum=The attendance cycle type cannot be null

application.form.is.empty=application form is empty
only.stage.status.can.be.delete=only stage status can be delete
not.revoke.repeat=not revoke repeat


user.not.have.calendar=user not have calendar
date.format.error=date format error
dept.exist.in.other.attendance.config=dept exist in {0} config
user.exist.in.other.attendance.config=user exist in {0} config
attendance.config.name.exist=calendar config name exist
the.year.of.the.holiday.cannot.be.empty=the Statutory Holiday Year parameter cannot be empty
the.fake.details.parameter.cannot.be.empty=the calendar details data year cannot be empty
the.names.of.statutory.holidays.are.repeated=the names of statutory holidays are repeated
the.start.time.of.a.statutory.holiday.cannot.be.greater.than.the.end.time=the start time of a statutory holiday cannot be greater than the end time
the.calendar.details.data.year.cannot.be.empty=the calendar details data year cannot be empty
calendar.details.data.cannot.be.empty=calendar details data cannot be empty
default.calendar.not.exist=Default calendar not exist,Please create Default calendar
country.not.have.default.attendance.config=country not have default attendance config
the.leave.end.time.must.be.after.the.start.time=the leave end time must be after the start time
the.leave.balance.is.insufficient=the leave balance is insufficient
the.balance.of.leave.cannot.be.less.than.the.length.of.leave=the balance of leave cannot be less than the length of leave
there.is.no.leave.type.information.for.the.details.of.the.application.form=there is no leave type information for the details of the application form
there.is.no.leave.start.or.end.time.for.the.details.of.the.application.form=there is no leave start or end time for the details of the application form
the.user.lacks.detailed.data.for.the.leave.type=the user lacks detailed data for the leave type
leave.time.cannot.be.zero=leave time cannot be zero
overtime.requests.are.not.allowed.on.weekdays=this time period is normal working hours!
please.do.not.repeat.the.overtime.process=you have already submitted an overtime order, please do not repeat the overtime process!
the.imported.data.is.empty=the imported overtime data is empty
overtime.days.are.not.on.the.same.day=overtime days are not on the same day

leave.day.cannot.be.zero=The end date does not fully cover your schedule (across night shifts), please adjust the end date backwards, thank you!
out.of.office.is.not.allowed.to.take.more.than.7.days=According to company policy, Each out of office request can not exceed 7 working days.
out.of.office.time.can.not.be.zero=out of office time can not be zero
the.leave.duration.cannot.be.less.than.the.minimum.leave.duration.of.the.leave.type=The leave duration cannot be less than the minimum leave duration of the leave type
custom.attendance.not.close=Cannot close. This attendance calendar is used by existing employees
there.is.a.temporal.overlap.in.statutory.holiday.data=there is a temporal overlap in statutory holiday data
the.name.of.the.statutory.holiday.already.exists=the name of the statutory holiday already exists
if.you.select.Require.a.leave.certificate.the.leave.condition.is.required=if you select Require a leave certificate,the leave condition is required
the.duration.of.statutory.holidays.overlaps.with.those.of.existing.statutory.holidays=the duration of statutory holidays overlaps with those of existing statutory holidays
startTime.cannot.be.empty=start time cannot be empty
endTime.cannot.be.empty=end time cannot be empty
startTime.cannot.be.later.than.endTime=start time cannot be later than end time
time.range.limited.days=time range limited days

the.holiday.adjustment.amount.needs.to.be.between.-999.and.999=the holiday adjustment amount needs to be between -999 and 999


punch.config.name.exist=punch config name exist
dept.exist.in.other.punch.config=department scope is duplicated with [{0}], please do not create repeatedly
user.exist.in.other.punch.config=This person is bound to [{0}]. To make adjustments, please remove them from the original rule first.
punch.config.country.exist=country scope is duplicated with [{0}], please do not create repeatedly
punch.config.country.not.exist=country scope punch rule is not exist, country:[{0}]
punch.config.country.can.not.change=punch config country can not change
punch.config.disable.can.not.update=disabled punch config can not update
punch.config.country.level.can.not.disabled=country level punch config can not disabled
disabled.config.user.in.other.config=can not enable, the disabled config user in other config
country.level.rule.can.not.change.range= country level rule can not change range

reissue.card.config.name.exist = reissue card config name exist
dept.exist.in.other.reissue.card.config=department scope is duplicated with [{0}], please do not create repeatedly
user.exist.in.other.reissue.card.config= This person is bound to [{0}]. To make adjustments, please remove them from the original rule first.
reissue.card.config.country.exist=reissue card config country scope is duplicated with [{0}], please do not create repeatedly
reissue.card.config.country.can.not.change=reissue card config country can not change
reissue.card.config.disable.can.not.update=disabled reissue card config can not update
reissue.card.config.country.level.can.not.disabled=country level reissue card config can not disabled


overtime.config.name.exist=overtime config name exist
dept.exist.in.other.overtime.config=department scope is duplicated with [{0}], please do not create repeatedly
user.exist.in.other.overtime.config= This person is bound to [{0}]. To make adjustments, please remove them from the original rule first.
overtime.config.country.can.not.change=overtime config country can not change
overtime.config.disable.can.not.update=disabled overtime config can not update
overtime.country.level.can.not.disabled=country level overtime config can not disabled
overtime.config.country.exist=overtime config country scope is duplicated with [{0}], please do not create repeatedly


scheduling.limit = Mexican warehouse labor staff restriction page scheduling
punch.class.save.not.empty=punch class save not empty
batch.shift.user.not.match=[{0}] people have been selected, of which [{1}] do not meet the requirements
user.are.occupied.by.running.task=user are occupied by running task
cycle.shift.user.must.belong.to.multi.class=cycle shift user must all belong to multi class
cycle.shift.user.class.not.match.selected.class=cycle shift user class does not match the selected class
cycle.shift.user.class.not.have.relate.punch.class.rule=cycle shift user does not have any related punch class rule

# 考勤设备模块校验
mac.address.already.exists=The mac address already exists
gps.address.already.exists=The gps address already exists
gps.name.already.exists=The gps name already exists
please.enter.the.correct.MAC.address=Please enter the correct MAC address format such as 02:10:18:02:40:7b

classType.cannot.be.empty=MEX, BRA and other countries,Shift type is required
classNature.illegality=The nature of the shift is illegal
className.duplicate=Duplicate shift names
restHours.abnormal=Please modify the time to ensure that the attendance hours are equal to the working hours plus the rest hours
legalWorkingHours.abnormal=Please modify the time to ensure that the sum of working hours in each time period is equal to the total working hours
attendanceHours.abnormal=Please modify the time to ensure that the sum of attendance hours in each time period is equal to the total attendance hours
account.mismatch=Mismatch of applicable personnel
class.info.duplicate=The shift time period information overlaps with the [{0}] shift. Please do not create it again and adjust the scope of application based on the original shift
className.length.limit.of.100.characters=The length limit for shift names is 100 characters
class.info.CLASS_NOT_EXISTS=The shift does not exist
class.status.already.disabled=The shift status has been deactivated, please do not repeat the operation
class.status.already.enable=The shift status has been activated, please do not repeat the operation
class.enable.className.duplicate=Cannot be enabled, there is a new rule for class names
class.enable.range.duplicate=Cannot be enabled, there are new rules for personnel within the scope of application
prohibit.disabled.class=Cannot be disabled, current personnel on this shift are using it
user.shift.is.occupied=There are other unfinished scheduling tasks for users within the applicable scope of the shift. Please take action later
disable.shift.prohibit.editing=The shutdown status of the shift cannot be edited. Please enable the shift status first
# 假期模块校验
company.leave.repeat.error=company leave repeat error
no.company.leave.exists.error=no company leave exists error
country.of.dispatch.required=Country of dispatch required
the.current.country.does.not.maintain.the.corresponding.nationality.please.maintain.the.nationality.first=The current country does not maintain the corresponding nationality, please maintain the nationality first
leave.name.is.not.empty=Leave name is not empty
user.not.have.this.leave.type=user not have this leave type






