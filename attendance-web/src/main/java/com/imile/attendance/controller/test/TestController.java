package com.imile.attendance.controller.test;

import com.imile.attendance.deviceConfig.AttendanceGpsConfigService;
import com.imile.attendance.deviceConfig.AttendanceWifiConfigService;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigImportDTO;
import com.imile.attendance.infrastructure.excel.header.ExcelTitleExportDTO;
import com.imile.attendance.infrastructure.mq.MqFailRecordService;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;

import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;

import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.PunchConfigQueryService;
import com.imile.attendance.rule.PunchConfigService;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.RuleConfigQueryService;
import com.imile.attendance.rule.command.PunchConfigAddCommand;
import com.imile.attendance.rule.command.PunchConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.PunchConfigUpdateCommand;
import com.imile.attendance.rule.dto.PunchConfigDetailDTO;
import com.imile.attendance.rule.dto.PunchConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigSelectDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.query.PunchConfigUserQuery;
import com.imile.attendance.rule.query.RuleConfigSelectQuery;
import com.imile.attendance.shift.UserShiftService;
import com.imile.attendance.shift.dto.UserShiftImportDTO;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.shift.factory.CycleShiftConfigFactory;
import com.imile.attendance.shift.param.FixedClassAutoRenewalParam;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.sync.hrms.HrUserInfoTableSyncHandler;
import com.imile.attendance.util.DateHelper;
import com.imile.util.date.DateUtils;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26
 * @Description
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Resource
    private HrUserInfoTableSyncHandler hrUserInfoTableSyncHandler;
    @Resource
    private PunchConfigService punchConfigService;
    @Resource
    private MqFailRecordService mqFailRecordService;
    @Resource
    private Executor bizTaskThreadPool;
    @Resource
    private PunchConfigQueryService punchConfigQueryService;
    @Resource
    private UserShiftService userShiftService;
    @Resource
    private AutoShiftConfigFactory autoShiftConfigFactory;
    @Resource
    private CycleShiftConfigFactory cycleShiftConfigFactory;
    @Resource
    private RuleConfigQueryService ruleConfigQueryService;
    @Resource
    private AttendanceGpsConfigService attendanceGpsConfigService;
    @Resource
    private AttendanceWifiConfigService attendanceWifiConfigService;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;

    /**
     * todo 测试
     *
     * @param param
     */
    @GetMapping("/syncHrUserInfo")
    @NoLoginRequired
    public void syncHrUserInfo(String param) {
        hrUserInfoTableSyncHandler.syncHrUserInfo(param);
    }

    @PostMapping("/addPunchConfig")
    @NoLoginRequired
    public Result<Void> addPunchConfig(@RequestBody PunchConfigAddCommand addCommand) {
        punchConfigService.add(addCommand);
        return Result.ok();
    }


    @PostMapping("/checkUpdateRule")
    @NoLoginRequired
    public Result<UpdateRuleReflectResult> checkUpdateRule(@RequestBody PunchConfigUpdateCommand updateCommand) {
        return Result.ok(punchConfigService.checkUpdateRule(updateCommand));
    }

    /**
     * 更新考勤配置
     *
     * @param updateCommand 包含更新信息的命令对象
     * @return 操作结果
     */
    @PostMapping("/updatePunchConfig")
    @NoLoginRequired
    public Result<Void> updatePunchConfig(@RequestBody PunchConfigUpdateCommand updateCommand) {
        punchConfigService.update(updateCommand);
        return Result.ok();
    }


    @PostMapping("/checkStatusSwitch")
    @NoLoginRequired
    public Result<UpdateRuleReflectResult> checkStatusSwitch(@RequestBody PunchConfigStatusSwitchCommand statusSwitchCommand) {
        return Result.ok(punchConfigService.checkStatusSwitch(statusSwitchCommand));
    }

    @PostMapping("/statusSwitch")
    @NoLoginRequired
    public Result<Void> statusSwitch(@RequestBody PunchConfigStatusSwitchCommand statusSwitchCommand) {
        punchConfigService.statusSwitch(statusSwitchCommand);
        return Result.ok();
    }

    @PostMapping("/listPunchList")
    @NoLoginRequired
    public Result<PaginationResult<PunchConfigPageDTO>> listPunchList(@RequestBody PunchConfigPageQuery query) {
        return Result.ok(punchConfigService.pagePunchConfigList(query));
    }

    @PostMapping("/pagePunchConfigUserList")
    @NoLoginRequired
    public Result<PaginationResult<RuleConfigUserInfoDTO>> pagePunchConfigUserList(@RequestBody PunchConfigUserQuery query) {
        return Result.ok(punchConfigService.pagePunchConfigUserList(query));
    }

    /**
     * 查询打卡配置详情
     *
     * @param configNo 配置编号
     * @return Result<PunchConfigDetailDTO>
     */
    @GetMapping("/queryPunchConfigDetail")
    @NoLoginRequired
    public Result<PunchConfigDetailDTO> queryPunchConfigDetail(String configNo) {
        return Result.ok(punchConfigService.queryPunchConfigDetail(configNo));
    }


    @PostMapping("/shift/page")
    @NoLoginRequired
    public Result<PaginationResult<UserShiftConfigDTO>> page(@RequestBody UserShiftConfigQuery query) {
        return Result.ok(userShiftService.page(query));
    }

    /**
     * 自动排班
     *
     * @param userAutoShiftParam 自动排班参数
     * @return 操作结果
     */
    @PostMapping("/shift/autoShift")
    @NoLoginRequired
    public Result<Void> autoShift(@RequestBody UserAutoShiftParam userAutoShiftParam) {
        autoShiftConfigFactory.autoShift(userAutoShiftParam);
        return Result.ok();
    }

    @PostMapping("/gps/export")
    @NoLoginRequired
    public Result<PaginationResult<AttendanceGpsConfigExportDTO>> gpsExport(@RequestBody AttendanceGpsConfigQuery query) {
        PaginationResult<AttendanceGpsConfigExportDTO> export = attendanceGpsConfigService.export(query);
        return Result.ok(export);
    }

    @PostMapping("/wifi/export")
    @NoLoginRequired
    public Result<PaginationResult<AttendanceWifiConfigExportDTO>> wifiExport(@RequestBody AttendanceWifiConfigQuery query) {
        PaginationResult<AttendanceWifiConfigExportDTO> export = attendanceWifiConfigService.export(query);
        return Result.ok(export);
    }

    @GetMapping("/mqRetryByMsgId")
    @NoLoginRequired
    public void mqRetryByMsgId(String msgId) {
        mqFailRecordService.mqRetryByMsgId(msgId);
    }

    @PostMapping("/wifi/import")
    @NoLoginRequired
    public Result<List<AttendanceWifiConfigImportDTO>> wifiImport(@RequestBody AttendanceWifiConfigImportDTO param) {
        List<AttendanceWifiConfigImportDTO> resultList = attendanceWifiConfigService.importWifiConfig(Arrays.asList(param));
        return Result.ok(resultList);
    }

    /**
     * 导出用户班次配置标题
     *
     * @param query 用户班次配置查询条件
     * @return 导出结果
     */

    @PostMapping("/shift/titleExport")
    @NoLoginRequired
    public Result<List<ExcelTitleExportDTO>> titleExport(@RequestBody UserShiftConfigQuery query) {
        return Result.ok(userShiftService.titleExport(query));
    }

    /**
     * 查询规则配置下拉选择
     *
     * @param query 规则配置下拉选择查询条件
     * @return 规则配置下拉选择结果
     */
    @PostMapping("/queryRuleConfigSelect")
    @NoLoginRequired
    public Result<List<RuleConfigSelectDTO>> queryRuleConfigSelect(@RequestBody RuleConfigSelectQuery query) {
        return Result.ok(ruleConfigQueryService.queryRuleConfigSelect(query, true));
    }

    /**
     * 查询规则配置用户ID列表
     *
     * @param ruleConfigSelectDTO 规则配置下拉选择DTO
     * @return 规则配置用户ID列表
     */
    @PostMapping("/queryRuleConfigUsers")
    @NoLoginRequired
    public Result<List<Long>> queryRuleConfigUsers(@RequestBody RuleConfigSelectDTO ruleConfigSelectDTO) {
        return Result.ok(ruleConfigQueryService.queryRuleConfigUsers(ruleConfigSelectDTO));
    }

    @GetMapping("/queryUserPunchConfig")
    @NoLoginRequired
    public Result<Map<Long, PunchConfigDO>> queryUserPunchConfig(@RequestParam List<Long> userIds) {
        return Result.ok(punchConfigManage.getConfigListByUserIdList(userIds));
    }

    @GetMapping("/queryUserReissueCardConfig")
    @NoLoginRequired
    public Result<Map<Long, ReissueCardConfigDO>> queryUserReissueCardConfig(@RequestParam List<Long> userIds) {
        return Result.ok(reissueCardConfigManage.getConfigMapByUserIdList(userIds));
    }

    /**
     * 测试循环排班自动续期
     * <p>
     * 该方法用于测试循环排班自动续期功能。如果不提供日期参数，将使用当前日期。
     * 方法会计算当前日期的dayId，并调用循环排班自动续期方法。
     * </p>
     *
     * @param dateStr 可选的日期参数，格式为yyyy-MM-dd
     * @return 操作结果
     */
    @GetMapping("/cycleShiftAutoRenewal")
    @NoLoginRequired
    public Result<Void> cycleShiftAutoRenewal(@RequestParam(required = false) String dateStr) {
        Date date;
        if (StringUtils.isNotBlank(dateStr)) {
            try {
                date = DateHelper.parseYYYYMMDD(dateStr);
            } catch (Exception e) {
                date = new Date();
            }
        } else {
            date = new Date();
        }
        Long dayId = DateHelper.getDayId(date);
        cycleShiftConfigFactory.cycleShiftAutoRenewal(date, dayId);
        return Result.ok();
    }

    /**
     * 测试固定班次自动排班续期
     * <p>
     * 该方法用于测试固定班次自动排班续期功能。可以指定国家列表、用户编码、开始时间和结束时间等参数。
     * 如果不提供时间参数，将使用当前日期作为开始时间，并自动计算结束时间。
     * </p>
     *
     * @param countryList  国家列表，逗号分隔（可选）
     * @param userCodes    用户编码列表，逗号分隔（可选）
     * @param startTimeStr 开始时间，格式为yyyy-MM-dd（可选）
     * @param endTimeStr   结束时间，格式为yyyy-MM-dd（可选）
     * @param pageSize     分页大小（可选，默认为500）
     * @return 操作结果
     */
    @GetMapping("/fixedClassAutoRenewal")
    @NoLoginRequired
    public Result<Void> fixedClassAutoRenewal(
            @RequestParam(required = false) String countryList,
            @RequestParam(required = false) String userCodes,
            @RequestParam(required = false) String startTimeStr,
            @RequestParam(required = false) String endTimeStr,
            @RequestParam(required = false) Integer pageSize) {

        // 创建参数对象
        FixedClassAutoRenewalParam param = new FixedClassAutoRenewalParam();
        param.setCountryList(countryList);
        param.setUserCodes(userCodes);

        // 处理开始时间
        if (StringUtils.isNotBlank(startTimeStr)) {
            try {
                Date startTime = DateHelper.parseYYYYMMDD(startTimeStr);
                param.setStartTime(startTime);
            } catch (Exception e) {
                log.info("解析开始时间失败: {}", startTimeStr, e);
            }
        }

        // 处理结束时间
        if (StringUtils.isNotBlank(endTimeStr)) {
            try {
                Date endTime = DateHelper.parseYYYYMMDD(endTimeStr);
                param.setEndTime(endTime);
            } catch (Exception e) {
                log.info("解析结束时间失败: {}", endTimeStr, e);
            }
        }

        // 设置分页大小
        if (pageSize != null && pageSize > 0) {
            param.setPageSize(pageSize);
        }

        // 调用固定班次自动排班续期方法
        autoShiftConfigFactory.fixedClassAutoRenewal(param);

        return Result.ok();
    }


    @PostMapping("/shift/import")
    @NoLoginRequired
    public Result<List<UserShiftImportDTO>> shiftImport(@RequestBody List<UserShiftImportDTO> importList) {
//        List<UserShiftImportDTO> importList = JSON.parseArray(jsonStr, UserShiftImportDTO.class);
        if (CollectionUtils.isEmpty(importList)) {
            return Result.ok(Collections.emptyList());
        }
        List<UserShiftImportDTO> failImportList = userShiftService.shiftImport(importList);
        return Result.ok(failImportList);
    }
}
