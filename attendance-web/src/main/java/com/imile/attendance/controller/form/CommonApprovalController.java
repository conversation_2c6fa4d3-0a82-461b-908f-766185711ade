package com.imile.attendance.controller.form;

import com.imile.attendance.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: HR考勤审批所有入口
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/attendance/approval")
public class CommonApprovalController extends BaseController {
    // TODO 等异常、打卡、假期完全做完才能有相关接口写审批
//    @Autowired
//    private HrmsAttendanceApprovalService hrmsAttendanceApprovalService;
//    @Autowired
//    private ImileRedisClient redissonClient;
//
//    private static final String SYNC_LOCK = "HRMS:LOCK:ATTENDANCE_SYNC:";
//
//    /**
//     * 管理员可管理的员工
//     */
//    @PostMapping("/user/auth/list")
//    @NoLoginAuthRequired
//    @NoHrmsLoginAuthRequired
//    public Result<List<UserAuthVO>> userAuthList(@RequestBody @Validated UserAuthParam param) {
//        List<UserAuthVO> resultVOList = hrmsAttendanceApprovalService.userAuthList(param);
//        return Result.ok(resultVOList);
//    }
//
//    /**
//     * 请假/外勤获取冲突单据和具体时长计算信息
//     */
//    @PostMapping("/duration/detail")
//    @NoLoginAuthRequired
//    public Result<DurationDetailVO> durationDetail(@RequestBody @Validated DurationDetailParam param) {
//        DurationDetailVO resultVO = hrmsAttendanceApprovalService.durationDetail(param);
//        return Result.ok(resultVO);
//    }
//
//    /**
//     * 取消
//     */
//    @PostMapping("/cancel")
//    @NoLoginAuthRequired
//    @NoHrmsLoginAuthRequired
//    public Result<Boolean> cancel(@RequestBody @Validated ApplicationFormCancelParam param) {
//        hrmsAttendanceApprovalService.cancel(param.getFormId());
//        return Result.ok(Boolean.TRUE);
//    }
//
//    /**
//     * 详情
//     */
//    @PostMapping("/detail")
//    @NoLoginAuthRequired
//    @NoHrmsLoginAuthRequired
//    public Result<AttendanceApplicationFromDetailVO> detail(@RequestBody ApplicationFormDetailParam param) {
//        AttendanceApplicationFromDetailVO detail = hrmsAttendanceApprovalService.getFromDetail(param.getFormId());
//        return Result.ok(detail);
//    }
//
//    /**
//     * 删除
//     */
//    @PostMapping("/delete")
//    @NoLoginAuthRequired
//    @NoHrmsLoginAuthRequired
//    public Result<Boolean> delete(@RequestBody @Validated ApplicationFormDeleteParam param) {
//        hrmsAttendanceApprovalService.delete(param.getFormId());
//        return Result.ok(Boolean.TRUE);
//    }
}
