package com.imile.attendance.controller.archive;

import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.archive.command.AttendanceArchiveUpdateCommand;
import com.imile.attendance.archive.query.AttendanceArchiveListQuery;
import com.imile.attendance.archive.query.RuleModifyRecordQuery;
import com.imile.attendance.archive.service.AttendanceArchiveService;
import com.imile.attendance.archive.vo.AttendanceArchiveDetailVO;
import com.imile.attendance.archive.vo.AttendanceArchiveUpdateConfirmVO;
import com.imile.attendance.archive.vo.AttendanceArchiveVO;
import com.imile.attendance.archive.vo.ModifyRecordModuleVO;
import com.imile.attendance.archive.vo.RuleModifyRecordVO;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.rule.RuleConfigQueryService;
import com.imile.attendance.rule.dto.RuleConfigSelectDTO;
import com.imile.attendance.rule.query.RuleConfigSelectQuery;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工考勤档案
 *
 * <AUTHOR>
 * @since 2025/5/6
 */
@RestController
@RequestMapping("/attendance/archive")
public class EmployeeAttendanceArchiveController {

    @Resource
    private AttendanceArchiveService attendanceArchiveService;
    @Resource
    private ConverterService converterService;
    @Resource
    private RuleConfigQueryService ruleConfigQueryService;


    /**
     * 考勤档案规则下拉
     * 附加未配置下拉选项
     */
    @PostMapping("/rule/config/select")
    public Result<List<RuleConfigSelectDTO>> queryRuleConfigSelect(@RequestBody @Validated RuleConfigSelectQuery query) {
        return Result.ok(ruleConfigQueryService.queryRuleConfigSelect(query, true));
    }

    /**
     * 考勤档案规则下拉
     * 不包含未配置下拉选项
     */
    @PostMapping("/latest/rule/config/select")
    public Result<List<RuleConfigSelectDTO>> queryLatestRuleConfigSelect(@RequestBody @Validated RuleConfigSelectQuery query) {
        return Result.ok(ruleConfigQueryService.queryRuleConfigSelect(query, false));
    }

    /**
     * 考勤档案列表
     */
    @PostMapping("/page")
    public Result<PaginationResult<AttendanceArchiveVO>> list(@RequestBody @Validated AttendanceArchiveListQuery query) {
        PaginationResult<AttendanceArchiveVO> result = attendanceArchiveService.page(query);
        converterService.withAnnotation(result.getResults());
        return Result.ok(result);
    }


    /**
     * 考勤档案导出
     */
    @PostMapping("/export")
    @ExportParamFill
    public Result<PaginationResult<AttendanceArchiveVO>> export(@RequestBody @Validated AttendanceArchiveListQuery query) {
        query.setArePageExport(Boolean.TRUE);
        PaginationResult<AttendanceArchiveVO> result = attendanceArchiveService.page(query);
        converterService.withAnnotation(result.getResults());
        return Result.ok(result);
    }

    /**
     * 考勤档案详情
     */
    @GetMapping("/detail")
    public Result<AttendanceArchiveDetailVO> detail(String userCode) {
        AttendanceArchiveDetailVO result = attendanceArchiveService.detail(userCode);
        converterService.withAnnotationForSingle(result.getEmployeeBaseInfo());
        converterService.withAnnotationForSingle(result.getAttendanceRuleConfig());
        return Result.ok(result);
    }

    /**
     * 考勤档案编辑前置处理
     */
    @PostMapping("/update/pre/processor")
    public Result<AttendanceArchiveUpdateConfirmVO> updatePreProcessor(@RequestBody @Validated AttendanceArchiveUpdateCommand command) {
        return Result.ok(attendanceArchiveService.updatePreProcessor(command));
    }

    /**
     * 考勤档案编辑
     */
    @PostMapping("/update")
    public Result<Void> update(@RequestBody @Validated AttendanceArchiveUpdateCommand command) {
        attendanceArchiveService.update(command);
        return Result.ok();
    }

    /**
     * 变更记录模块下拉
     */
    @GetMapping("/module/select")
    public Result<List<ModifyRecordModuleVO>> moduleSelect() {
        return Result.ok(attendanceArchiveService.moduleSelect());
    }

    /**
     * 规则变更记录分页
     */
    @PostMapping("/ruleModifyList")
    public Result<List<RuleModifyRecordVO>> ruleModifyList(@RequestBody @Validated RuleModifyRecordQuery query) {
        List<RuleModifyRecordVO> result = attendanceArchiveService.ruleModifyList(query);
        return Result.ok(result);
    }

}
