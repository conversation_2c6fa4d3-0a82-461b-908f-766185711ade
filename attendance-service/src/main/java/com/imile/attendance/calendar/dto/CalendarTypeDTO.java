package com.imile.attendance.calendar.dto;

import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description 日历类型
 */
@Data
public class CalendarTypeDTO {

    /**
     * 是否存在默认日历（0 无  1有）
     */
    private Integer isHaveDefaultCalendar;



    public static CalendarTypeDTO create(Integer count) {
        CalendarTypeDTO calendarTypeDTO = new CalendarTypeDTO();
        calendarTypeDTO.setIsHaveDefaultCalendar(count > 0 ? BusinessConstant.Y : BusinessConstant.N);
        return calendarTypeDTO;
    }


}
