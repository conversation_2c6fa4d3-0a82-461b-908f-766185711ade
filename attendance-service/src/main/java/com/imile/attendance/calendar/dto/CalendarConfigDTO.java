package com.imile.attendance.calendar.dto;

import com.imile.attendance.enums.AttendanceTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class CalendarConfigDTO {

    /**
     * 配置ID
     */
    private Long id;
    /**
     * 方案名称
     */
    private String attendanceConfigName;
    /**
     * 出勤方案类型 缺省方案、自定义方案
     */
    private String type;
    /**
     * 启用状态
     */
    private String status;

    /**
     * 使用范围记录
     */
    private List<CalendarConfigRangeDTO> rangeRecords;
    /**
     * 已绑定员工数
     */
    private Integer employeeCount;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;
    /**
     * 最近修改日期
     */
    private Date lastUpdDate;

    /**
     * 适用国家
     */
    private String country;


    public boolean areCustom() {
        return AttendanceTypeEnum.DEFAULT.name().equals(this.type);
    }
}
