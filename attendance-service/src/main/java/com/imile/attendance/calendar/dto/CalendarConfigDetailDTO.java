package com.imile.attendance.calendar.dto;

import lombok.Data;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class CalendarConfigDetailDTO {

    /**
     * 日历配置ID
     */
    private Long id;
    /**
     * 日历配置方案名称
     */
    private String attendanceConfigName;
    /**
     * 日历出勤方案类型 缺省方案、自定义方案
     */
    private String type;

    /**
     * 适用范围之部门记录
     */
    private List<CalendarConfigRangeDTO> deptRecords;
    /**
     * 适用范围之员工记录
     */
    private List<CalendarConfigRangeDTO> userRecords;


    /**
     * 日期属性设置
     */
    private List<MonthDaysConfigDTO> monthDaysConfigs;

    /**
     * 适用部门
     */
    private String deptIds;


    public List<Long> listDeptIds() {
        if (StringUtils.isNotBlank(this.deptIds)) {
            return Arrays.asList((Long[]) ConvertUtils.convert(this.deptIds.split(","), Long.class));
        }
        return Collections.emptyList();
    }
}
