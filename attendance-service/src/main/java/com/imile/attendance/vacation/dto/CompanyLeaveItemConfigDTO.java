package com.imile.attendance.vacation.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-15
 * @version: 1.0
 */
@Data
public class CompanyLeaveItemConfigDTO implements Serializable {
    private static final long serialVersionUID = -9042502066740373014L;

    /**
     * 公司假期配置id
     */
    private Long leaveId;
    /**
     * 阶段
     */
    private Integer stage;
    /**
     * 假期长度
     */
    private BigDecimal leaveDay;
    /**
     * 百分比日薪
     */
    private BigDecimal percentSalary;
}
