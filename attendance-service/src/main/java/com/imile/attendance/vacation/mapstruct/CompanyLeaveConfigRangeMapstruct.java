package com.imile.attendance.vacation.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.vacation.command.CompanyLeaveConfigRangeCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface CompanyLeaveConfigRangeMapstruct {

    CompanyLeaveConfigRangeMapstruct INSTANCE = Mappers.getMapper(CompanyLeaveConfigRangeMapstruct.class);


    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    CompanyLeaveConfigRangDO mapToCompanyLeaveRangeConfigDO(CompanyLeaveConfigRangeCommand addCommand);

    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    List<CompanyLeaveConfigRangDO> mapToCompanyLeaveRangeConfigDO(List<CompanyLeaveConfigRangeCommand> addCommand);

    default List<CompanyLeaveConfigRangDO> toAddRangeConfigList(List<CompanyLeaveConfigRangeCommand> addCommand) {
        List<CompanyLeaveConfigRangDO> companyLeaveConfigRangList = mapToCompanyLeaveRangeConfigDO(addCommand);
        for (CompanyLeaveConfigRangDO companyLeaveConfigRangDO : companyLeaveConfigRangList) {
            BaseDOUtil.fillDOInsert(companyLeaveConfigRangDO);
        }
        return companyLeaveConfigRangList;
    }


}
