package com.imile.attendance.user.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/4/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserOptionVO {

    /**
     * 人员ID
     */
    private Long id;
    /**
     * 人员编码
     */
    private String userCode;
    /**
     * 人员姓名
     */
    private String userName;
}
