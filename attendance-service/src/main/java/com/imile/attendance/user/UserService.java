package com.imile.attendance.user;

import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.user.vo.UserOptionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/19
 */
public interface UserService {

    List<UserOptionVO> getUserAssociateList(UserDaoQuery userDaoQuery);

    void setUserClassNature(String userCode, AttendanceUserEntryRecord attendanceUserEntryRecord);

    boolean checkValidUserAttendanceRange(Long userId);

    boolean checkValidDimissionUserAttendanceRange(Long userId);

}
