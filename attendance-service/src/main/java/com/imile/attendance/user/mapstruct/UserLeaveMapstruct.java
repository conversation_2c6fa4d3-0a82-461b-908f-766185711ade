package com.imile.attendance.user.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveQuery;
import com.imile.attendance.user.dto.UserLeaveDTO;
import com.imile.attendance.user.dto.UserLeaveRecordDTO;
import com.imile.attendance.user.query.UserLeavePageQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/8
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface UserLeaveMapstruct {

    UserLeaveMapstruct INSTANCE = Mappers.getMapper(UserLeaveMapstruct.class);

    UserLeaveQuery pageToQuery(UserLeavePageQuery pageQuery);

    @Mapping(target = "country", source = "locationCountry")
    List<UserLeaveDTO> toUserLeaveDTO(List<AttendanceUser> userInfoList);

    @Mapping(target = "leaveDate", source = "date")
    @Mapping(target = "minutesNum", source = "leaveMinutes")
    @Mapping(target = "operationCode", source = "lastUpdUserCode")
    @Mapping(target = "operationName", source = "lastUpdUserName")
    @Mapping(target = "createDate", source = "lastUpdDate")
    List<UserLeaveRecordDTO> toUserLeaveRecordDTO(List<UserLeaveRecordDO> userLeaveRecordList);

}
