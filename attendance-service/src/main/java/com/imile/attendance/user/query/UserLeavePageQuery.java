package com.imile.attendance.user.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;

import java.util.List;


@Data
public class UserLeavePageQuery extends ResourceQuery {

    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 用户姓名或邮箱
     */
    private String userNameOrEmail;
    /**
     * 部门id列表
     */
    private List<Long> deptIdList;

    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;

    private String country;

    /**
     * 工作状态
     */
    private List<String> workStatusList;

    /**
     * 用工类型:假期涉及用工类型：员工、挂靠、实习、兼容、顾问
     */
    private List<String> employmentTypeList;
}
