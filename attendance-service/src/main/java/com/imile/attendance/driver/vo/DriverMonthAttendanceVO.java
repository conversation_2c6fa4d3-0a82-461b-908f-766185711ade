package com.imile.attendance.driver.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverMonthAttendanceVO
 * {@code @since:} 2024-01-24 10:51
 * {@code @description:}
 */
@Data
public class DriverMonthAttendanceVO implements Serializable {

    private static final long serialVersionUID = 3854565886004116964L;
    /**
     * 当前月份
     */
    private Long month;

    /**
     * 考勤详情
     */
    private List<DriverAttendanceVO> driverAttendanceDetail;
}
