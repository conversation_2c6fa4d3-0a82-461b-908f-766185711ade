package com.imile.attendance.driver.mapstruct;

import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.driver.dto.DriverAttendanceDetailMonthParam;
import com.imile.attendance.driver.dto.DriverAttendanceDetailParam;
import com.imile.attendance.driver.dto.DriverAttendanceOperateRecordParam;
import com.imile.attendance.driver.dto.DriverPunchRecordParam;
import com.imile.attendance.driver.vo.DriverAttendanceDetailVO;
import com.imile.attendance.driver.vo.DriverAttendanceOperateRecordVO;
import com.imile.attendance.driver.vo.DriverPunchRecordVO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailMonthQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceOperateRecordQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import com.imile.attendance.util.DateConvertUtils;
import com.imile.attendance.util.DateHelper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/7
 * @Description
 */
@Mapper(config = MapperConfiguration.class, imports = {RequestInfoHolder.class, DateConvertUtils.class})
public interface DriverPunchMapstruct {

    DriverPunchMapstruct INSTANCE = Mappers.getMapper(DriverPunchMapstruct.class);


    @Mapping(target = "operationType", ignore = true)
    @Mapping(target = "dayIdList", ignore = true)
    DriverPunchRecordDetailQuery toPunchRecordDetailQuery(DriverPunchRecordParam driverPunchRecordParam);


    default Date dealWithLocalCreateDate(String timeZone, Date punchRecordCreateDate) {
        return DateHelper.convertDateByTimeZonePlus(timeZone, punchRecordCreateDate);
    }

    @Mapping(target = "operationContentView", expression = "java(RequestInfoHolder.isChinese() ? driverPunchRecordDO.getOperationContent():driverPunchRecordDO.getOperationContentEn())")
    @Mapping(target = "localCreateDate", expression = "java(dealWithLocalCreateDate(timeZone, driverPunchRecordDO.getCreateDate()))")
    @Mapping(target = "operatingTime", expression = "java(DateConvertUtils.toLocalDateTime(driverPunchRecordDO.getOperatingTime()))")
    DriverPunchRecordVO toDriverPunchRecordVO(DriverPunchRecordDO driverPunchRecordDO, String timeZone);

    default List<DriverPunchRecordVO> toDriverPunchRecordVO(List<DriverPunchRecordDO> driverPunchRecordDOList, String timeZone) {
        return driverPunchRecordDOList.stream()
                .map(item -> toDriverPunchRecordVO(item, timeZone))
                .collect(Collectors.toList());
    }

    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum",  ignore = true)
    @Mapping(target = "isDriver",  ignore = true)
    @Mapping(target = "deptList",  ignore = true)
    @Mapping(target = "deptId",  ignore = true)
    @Mapping(target = "countryList",  ignore = true)
    DriverAttendanceDetailQuery toDriverAttendanceDetailQuery(DriverAttendanceDetailParam detailParam);


    @Mapping(target = "userCodeAndName",  ignore = true)
    @Mapping(target = "pageSize",  ignore = true)
    @Mapping(target = "pageNum",  ignore = true)
    @Mapping(target = "isDriver",  ignore = true)
    @Mapping(target = "deptList",  ignore = true)
    @Mapping(target = "deptId",  ignore = true)
    @Mapping(target = "countryList",  ignore = true)
    DriverAttendanceDetailMonthQuery toDriverAttendanceDetailMonthQuery(DriverAttendanceDetailMonthParam monthParam);


    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum",  ignore = true)
    DriverAttendanceOperateRecordQuery toDriverAttendanceOperateRecordQuery(DriverAttendanceOperateRecordParam operateRecordParam);



    @Mapping(target = "operationTypeString", ignore = true)
    @Mapping(target = "operationContentView", ignore = true)
    DriverAttendanceOperateRecordVO toBaseOperateRecordVO(DriverAttendanceOperateRecordDO driverAttendanceOperateRecordDO);


    default DriverAttendanceOperateRecordVO toOperateRecordVO(DriverAttendanceOperateRecordDO driverAttendanceOperateRecordDO){
        DriverAttendanceOperateRecordVO driverAttendanceOperateRecordVO = toBaseOperateRecordVO(driverAttendanceOperateRecordDO);
        driverAttendanceOperateRecordVO.setOperationContentView(driverAttendanceOperateRecordVO.queryOperationContentView());
        driverAttendanceOperateRecordVO.setOperationTypeString(driverAttendanceOperateRecordVO.queryOperationTypeString());
        return driverAttendanceOperateRecordVO;
    }

    default List<DriverAttendanceOperateRecordVO> toOperateRecordVO(List<DriverAttendanceOperateRecordDO> driverAttendanceOperateRecordDOList){
        return driverAttendanceOperateRecordDOList.stream()
                .map(this::toOperateRecordVO)
                .collect(Collectors.toList());
    }


    @Mapping(target = "workStatusDesc", ignore = true)
    @Mapping(target = "statusDesc", ignore = true)
    @Mapping(target = "ocTypeNames", ignore = true)
    DriverAttendanceDetailVO toDriverAttendanceDetailVO(DriverAttendanceDetailDTO driverAttendanceDetailDTO);

    List<DriverAttendanceDetailVO> toDriverAttendanceDetailVO(List<DriverAttendanceDetailDTO> driverAttendanceDetailDTOList);
}
