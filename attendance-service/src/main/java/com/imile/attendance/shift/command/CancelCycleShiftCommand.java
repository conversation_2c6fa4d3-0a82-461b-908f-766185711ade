package com.imile.attendance.shift.command;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description
 */
@Data
public class CancelCycleShiftCommand {

    /**
     * 批量排班用户
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<Long> userIdList;

    /**
     * 班次ID
     */
    private Long classId;
}
