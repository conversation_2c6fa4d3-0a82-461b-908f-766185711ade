package com.imile.attendance.shift.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description
 */
@Data
public class UserShiftConfigAddCommand {


    /**
     * 用户
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * 每日班次
     */
    List<DaysConfigCommand> daysConfigParamList;

    /**
     * 来自页面排班
     */
    private Boolean fromPage = Boolean.FALSE;
}
