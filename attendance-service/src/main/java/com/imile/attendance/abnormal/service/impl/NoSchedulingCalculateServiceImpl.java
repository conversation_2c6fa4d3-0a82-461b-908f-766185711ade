package com.imile.attendance.abnormal.service.impl;

import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 无排班计划考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "NoSchedulingCalculateServiceImpl")
public class NoSchedulingCalculateServiceImpl implements AttendanceCalculateService {
    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return CollectionUtils.isEmpty(userAttendancePunchConfigList);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {

    }
}
