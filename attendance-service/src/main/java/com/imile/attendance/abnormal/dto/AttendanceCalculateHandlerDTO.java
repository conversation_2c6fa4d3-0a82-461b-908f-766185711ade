package com.imile.attendance.abnormal.dto;

import com.imile.attendance.constants.BusinessConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 考勤计算参数
 *
 * <AUTHOR>
 * @since 2025/05/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceCalculateHandlerDTO {
    /**
     * 入参:公司(定时任务执行的时候用到)
     */
    private String countryList;

    /**
     * 入参:用户编码   考勤审批通过后会通过这个字段来 定时任务也可以，手动执行某个用户
     * 优先看userCodeList有没有值，有就无视公司
     */
    private String userCodes;

    /**
     * 入参:需要计算的考勤天
     */
    private Long attendanceDayId;

    /**
     * 界面无法输入，考勤时间，当天的结束时间  yyyy-MM-dd HH:mm:ss
     */
    private Date attendanceTime;

    /**
     * 界面无法输入，开始时间  yyyy-MM-dd HH:mm:ss  周期的开始时间(3天的开始时间)
     */
    private Date startTime;

    /**
     * 界面无法输入，结束时间  yyyy-MM-dd HH:mm:ss  周期的结束时间(3天的结束时间)
     */
    private Date endTime;

    /**
     * 界面无法输入，开始时间  yyyyMMdd  周期的开始时间的long
     */
    private Long startDayId;

    /**
     * 界面无法输入，结束时间  yyyyMMdd  周期的结束时间的long
     */
    private Long endDayId;

    /**
     * 界面无法输入，改天考勤真正的起始时间 yyyy-MM-dd HH:mm:ss(排班计算出来的)
     */
    private Date actualAttendanceStartTime;

    /**
     * 界面无法输入，改天考勤真正的结束时间  yyyy-MM-dd HH:mm:ss(排班计算出来的)
     */
    private Date actualAttendanceEndTime;

    /**
     * 请假时长
     */
    private BigDecimal leaveHours;

    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 工作网点ID
     */
    private Long ocId;

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 仓内考勤配置ID
     */
    private Long warehouseAttendanceConfigId;

    /**
     * 实际出勤时长 单位:小时 包含休息时间
     */
    private BigDecimal actualAttendanceTime = BigDecimal.ZERO;

    /**
     * 实际工作总时长 单位:小时 不包含休息时间
     */
    private BigDecimal actualWorkingHours = BigDecimal.ZERO;

    public boolean check() {
        if (Objects.isNull(attendanceDayId)) {
            return false;
        }
        return !StringUtils.isEmpty(countryList) || !StringUtils.isEmpty(userCodes);
    }

    /**
     * String 转 List<String>
     */
    public List<String> strConvertList(String field) {
        if (StringUtils.isBlank(field)) {
            return Collections.emptyList();
        }
        return Arrays.stream(field.split(BusinessConstant.DEFAULT_DELIMITER))
                .map(String::trim)
                .collect(Collectors.toList());
    }


}
