package com.imile.attendance.abnormal.service.impl;

import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 非工作(周末、节假日)考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "NoWorkdayCalculateServiceImpl")
public class NoWorkdayCalculateServiceImpl implements AttendanceCalculateService {
    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return CollectionUtils.isNotEmpty(userAttendancePunchConfigList) && Objects.equals(BusinessConstant.N, userAttendancePunchConfigList.get(0).getIsActualPunch());
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {

    }
}
