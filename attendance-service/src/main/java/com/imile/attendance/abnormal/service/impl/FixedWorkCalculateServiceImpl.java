package com.imile.attendance.abnormal.service.impl;

import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 固定班次打卡考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "FixedWorkCalculateServiceImpl")
public class FixedWorkCalculateServiceImpl implements AttendanceCalculateService {

    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.Y, userAttendancePunchConfigList.get(0).getIsActualPunch())
                && Objects.equals(PunchConfigTypeEnum.FIXED_WORK.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {

    }
}
