package com.imile.attendance.abnormal.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.AttendanceDataSourceEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 免打卡考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "NoNeedWorkCalculateServiceImpl")
public class NoNeedWorkCalculateServiceImpl implements AttendanceCalculateService {
    @Resource
    private AttendanceEmployeeDetailManage attendanceEmployeeDetailManage;
    @Resource
    private DefaultIdWorker defaultIdWorker;


    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.Y, userAttendancePunchConfigList.get(0).getIsActualPunch())
                && Objects.equals(PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {

        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList = new ArrayList<>();

        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = calculateContext.getAttendanceEmployeeDetailDOList();
        List<EmployeeAbnormalAttendanceDO> userAbnormalAttendanceDOList = calculateContext.getUserAbnormalAttendanceDOList();

        //需要将当天已经存在的考勤全部删除，会重新计算(请假/外勤审批通过的肯定不能删除,不然会重复计算，浪费假期)
        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = attendanceEmployeeDetailDOList
                .stream().filter(item -> Objects.isNull(item.getFormId())).collect(Collectors.toList());
        updateEmployeeDetailDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });

        //没有被审批单关联的单据，可以删除的异常
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = userAbnormalAttendanceDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode())
                        || StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.REJECT.getCode())).collect(Collectors.toList());

        updateAbnormalAttendanceDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });

        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();
        //当天的起始截止时间（截止时间用第二天的开始时间，如果用当天的结束时间23.59.59，会有1分钟的误差）
        calculateHandlerDTO.setActualAttendanceStartTime(DateHelper.beginOfDay(calculateHandlerDTO.getAttendanceTime()));
        calculateHandlerDTO.setActualAttendanceEndTime(DateHelper.beginOfDay(DateHelper.pushDate(calculateHandlerDTO.getActualAttendanceStartTime(), 1)));

        BigDecimal legalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
        BigDecimal totalMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES);

        //查询当天生成的正常考勤的所有分钟(请假/外勤的 正常考勤会被删除，然后根据本次运行结果，看是否生成正常还是异常考勤)
        BigDecimal usedMinutes = dayExistMinutes(attendanceEmployeeDetailDOList);

        //把当天正常考勤中的已经关联过审批通过的单据的正常考勤排除掉,防止重复计算
        List<Long> usedFormIdList = attendanceEmployeeDetailDOList.stream().map(AttendanceEmployeeDetailDO::getFormId).filter(Objects::nonNull).collect(Collectors.toList());
        for (AttendanceFormDetailBO attendanceFormDetailBO : calculateContext.getUserPassFormBOList()) {
            AttendanceFormDO formDO = attendanceFormDetailBO.getFormDO();
            if (usedFormIdList.contains(formDO.getId())) {
                continue;
            }
            //外勤
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                usedMinutes = outOffOfficeHandler(usedMinutes, totalMinutes, calculateContext.getAttendanceType(), calculateContext.getUser(), formDO,
                        attendanceFormDetailBO.getAttrDOList(), calculateHandlerDTO, addEmployeeDetailDOList);
                continue;
            }

            //请假(未排班，当天请假，全部消耗假期)
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                usedMinutes = leaveHandler(calculateContext.getUser(), usedMinutes, totalMinutes, calculateContext.getAttendanceType(), calculateHandlerDTO, formDO,
                        attendanceFormDetailBO.getAttrDOList(), calculateContext.getUserLeaveDetailDOList(), calculateContext.getUserLeaveStageDetailDOList(),
                        calculateContext.getUserCompanyLeaveConfigDOList(), addEmployeeDetailDOList);
            }
        }

        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
    }


    /**
     * 外勤处理
     */
    private BigDecimal outOffOfficeHandler(BigDecimal usedMinutes,
                                           BigDecimal totalMinutes,
                                           String attendanceType,
                                           UserInfoDO user,
                                           AttendanceFormDO formDO,
                                           List<AttendanceFormAttrDO> userPassFormAttrDOList,
                                           AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                           List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        List<AttendanceFormAttrDO> outOfOfficeStartDateDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode()))
                .collect(Collectors.toList());
        List<AttendanceFormAttrDO> outOfOfficeEndDateDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outOfOfficeStartDateDO) || CollectionUtils.isEmpty(outOfOfficeEndDateDO)) {
            return usedMinutes;
        }
        Date outOfOfficeStartDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateDO.get(0).getAttrValue());
        Date outOfOfficeEndDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateDO.get(0).getAttrValue());

        Long outOfOfficeStartDayId = DateHelper.getDayId(outOfOfficeStartDate);
        Long outOfOfficeEndDayId = DateHelper.getDayId(outOfOfficeEndDate);
        if (calculateHandlerDTO.getAttendanceDayId().compareTo(outOfOfficeStartDayId) < 0 || calculateHandlerDTO.getAttendanceDayId().compareTo(outOfOfficeEndDayId) > 0) {
            return usedMinutes;
        }
        //没有交集，直接返回
        if (outOfOfficeEndDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                || outOfOfficeStartDate.compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
            return usedMinutes;
        }
        //外勤直接包含改天排班时间
        if (outOfOfficeStartDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                && outOfOfficeEndDate.compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
            usedMinutes = outOffOfficeTimeHandler(calculateHandlerDTO.getActualAttendanceStartTime(), calculateHandlerDTO.getActualAttendanceEndTime(),
                    totalMinutes, usedMinutes, user, calculateHandlerDTO, attendanceType, formDO, addEmployeeDetailDOList);
            return usedMinutes;
        }
        //改天排班时间包含外勤时间
        if (calculateHandlerDTO.getActualAttendanceStartTime().compareTo(outOfOfficeStartDate) < 1
                && calculateHandlerDTO.getActualAttendanceEndTime().compareTo(outOfOfficeEndDate) > -1) {
            usedMinutes = outOffOfficeTimeHandler(outOfOfficeStartDate, outOfOfficeEndDate, totalMinutes, usedMinutes, user, calculateHandlerDTO,
                    attendanceType, formDO, addEmployeeDetailDOList);
            return usedMinutes;
        }

        //左交集
        if (outOfOfficeStartDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1) {
            usedMinutes = outOffOfficeTimeHandler(calculateHandlerDTO.getActualAttendanceStartTime(), outOfOfficeEndDate, totalMinutes, usedMinutes,
                    user, calculateHandlerDTO, attendanceType, formDO, addEmployeeDetailDOList);
            return usedMinutes;
        }
        //右交集
        usedMinutes = outOffOfficeTimeHandler(outOfOfficeStartDate, calculateHandlerDTO.getActualAttendanceEndTime(), totalMinutes, usedMinutes, user,
                calculateHandlerDTO, attendanceType, formDO, addEmployeeDetailDOList);
        return usedMinutes;
    }

    private BigDecimal outOffOfficeTimeHandler(Date actualAttendanceStartTime,
                                               Date actualAttendanceEndTime,
                                               BigDecimal totalMinutes,
                                               BigDecimal usedMinutes,
                                               UserInfoDO user,
                                               AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                               String attendanceType,
                                               AttendanceFormDO formDO,
                                               List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        BigDecimal outOfOfficeMinutes = BigDecimal.valueOf(DateUtil.between(actualAttendanceStartTime, actualAttendanceEndTime, DateUnit.MINUTE));
        //已经外勤的加上本次外勤的，已经够了当天法定工作时间，就不会在请假了
        if (outOfOfficeMinutes.add(usedMinutes).compareTo(totalMinutes) > -1) {
            outOfOfficeMinutes = totalMinutes.subtract(usedMinutes);
        }
        if (outOfOfficeMinutes.compareTo(BigDecimal.ZERO) < 1) {
            return usedMinutes;
        }
        AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.OOO.getCode(),
                BusinessConstant.Y, null, null, null, BigDecimal.ZERO, outOfOfficeMinutes, BigDecimal.ZERO, formDO.getId());
        addEmployeeDetailDOList.add(userAttendance);
        usedMinutes = usedMinutes.add(outOfOfficeMinutes);
        return usedMinutes;
    }

    /**
     * 请假处理
     */
    private BigDecimal leaveHandler(UserInfoDO user,
                                    BigDecimal usedMinutes,
                                    BigDecimal totalMinutes,
                                    String attendanceType,
                                    AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                    AttendanceFormDO formDO,
                                    List<AttendanceFormAttrDO> userPassFormAttrDOList,
                                    List<UserLeaveDetailDO> userLeaveDetailDOList,
                                    List<UserLeaveStageDetailDO> userLeaveStageDetailDOList,
                                    List<CompanyLeaveConfigDO> userCompanyLeaveConfigDOList,
                                    List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {

        List<AttendanceFormAttrDO> leaveStartDateDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode()))
                .collect(Collectors.toList());

        List<AttendanceFormAttrDO> leaveEndDateDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode()))
                .collect(Collectors.toList());

        List<AttendanceFormAttrDO> leaveTypeDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode()))
                .collect(Collectors.toList());

        Date leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateDO.get(0).getAttrValue());
        Date leaveEndDate = DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateDO.get(0).getAttrValue());
        Long leaveStartDayId = DateHelper.getDayId(leaveStartDate);
        Long leaveEndDayId = DateHelper.getDayId(leaveEndDate);
        if (calculateHandlerDTO.getAttendanceDayId().compareTo(leaveStartDayId) < 0 || calculateHandlerDTO.getAttendanceDayId().compareTo(leaveEndDayId) > 0) {
            return usedMinutes;
        }
        List<CompanyLeaveConfigDO> companyLeaveList = userCompanyLeaveConfigDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveType(), leaveTypeDO.get(0).getAttrValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(companyLeaveList)) {
            return usedMinutes;
        }
        List<UserLeaveDetailDO> leaveDetailList = userLeaveDetailDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveType(), leaveTypeDO.get(0).getAttrValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveDetailList)) {
            return usedMinutes;
        }
        List<Long> leaveIdList = leaveDetailList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
        //根据百分比降序，优先使用百分比最高的
        List<UserLeaveStageDetailDO> leaveStageDetailDOList = userLeaveStageDetailDOList.stream()
                .filter(item -> leaveIdList.contains(item.getLeaveId())).sorted(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveStageDetailDOList)) {
            return usedMinutes;
        }
        //没有交集，直接返回
        if (leaveEndDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                || leaveStartDate.compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
            return usedMinutes;
        }
        //请假直接包含改天排班时间
        if (leaveStartDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                && leaveEndDate.compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
            usedMinutes = leaveTimeHandler(calculateHandlerDTO.getActualAttendanceStartTime(), calculateHandlerDTO.getActualAttendanceEndTime(), totalMinutes, usedMinutes, user, calculateHandlerDTO, attendanceType, formDO, companyLeaveList, leaveStageDetailDOList, addEmployeeDetailDOList);
            return usedMinutes;
        }
        //改天排班时间包含请假时间
        if (calculateHandlerDTO.getActualAttendanceStartTime().compareTo(leaveStartDate) < 1
                && calculateHandlerDTO.getActualAttendanceEndTime().compareTo(leaveEndDate) > -1) {
            usedMinutes = leaveTimeHandler(leaveStartDate, leaveEndDate, totalMinutes, usedMinutes, user, calculateHandlerDTO, attendanceType, formDO, companyLeaveList, leaveStageDetailDOList, addEmployeeDetailDOList);
            return usedMinutes;
        }

        //左交集
        if (leaveStartDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1) {
            usedMinutes = leaveTimeHandler(calculateHandlerDTO.getActualAttendanceStartTime(), leaveEndDate, totalMinutes, usedMinutes, user, calculateHandlerDTO, attendanceType, formDO, companyLeaveList, leaveStageDetailDOList, addEmployeeDetailDOList);
            return usedMinutes;
        }
        //右交集
        usedMinutes = leaveTimeHandler(leaveStartDate, calculateHandlerDTO.getActualAttendanceEndTime(), totalMinutes, usedMinutes, user, calculateHandlerDTO, attendanceType, formDO, companyLeaveList, leaveStageDetailDOList, addEmployeeDetailDOList);
        return usedMinutes;

    }

    private BigDecimal leaveTimeHandler(Date actualAttendanceStartTime,
                                        Date actualAttendanceEndTime,
                                        BigDecimal totalMinutes,
                                        BigDecimal usedMinutes,
                                        UserInfoDO user,
                                        AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                        String attendanceType,
                                        AttendanceFormDO formDO,
                                        List<CompanyLeaveConfigDO> companyLeaveList,
                                        List<UserLeaveStageDetailDO> leaveStageDetailDOList,
                                        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        BigDecimal leaveMinutes = BigDecimal.valueOf(DateUtil.between(actualAttendanceStartTime, actualAttendanceEndTime, DateUnit.MINUTE));
        //已经请假的加上本次外勤的，已经够了当天法定工作时间，就不会在请假了
        if (leaveMinutes.add(usedMinutes).compareTo(totalMinutes) > -1) {
            leaveMinutes = totalMinutes.subtract(usedMinutes);
        }
        if (leaveMinutes.compareTo(BigDecimal.ZERO) < 1) {
            return usedMinutes;
        }
        leaveInfoBuild(user, calculateHandlerDTO, formDO.getId(), companyLeaveList.get(0), leaveStageDetailDOList, leaveMinutes, attendanceType, addEmployeeDetailDOList);
        usedMinutes = usedMinutes.add(leaveMinutes);
        return usedMinutes;
    }

    private void leaveInfoBuild(UserInfoDO user,
                                AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                Long formId,
                                CompanyLeaveConfigDO companyLeaveConfigDO,
                                List<UserLeaveStageDetailDO> leaveStageDetailDOList,
                                BigDecimal leaveMinutes,
                                String attendanceType,
                                List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        //阶梯假
        for (int i = 0; i < leaveStageDetailDOList.size(); i++) {
            UserLeaveStageDetailDO detailDO = leaveStageDetailDOList.get(i);
            //不是最后一个阶梯，并且有假期<=0
            if (i != leaveStageDetailDOList.size() - 1 && detailDO.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) < 1) {
                continue;
            }
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType,
                    companyLeaveConfigDO.getLeaveShortName(), BusinessConstant.Y, companyLeaveConfigDO.getLeaveName()
                    , detailDO.getPercentSalary(), detailDO.getStage(), leaveMinutes, BigDecimal.ZERO, BigDecimal.ZERO, formId);
            addEmployeeDetailDOList.add(userAttendance);
            break;
        }
    }

    private AttendanceEmployeeDetailDO buildUserAttendance(UserInfoDO user,
                                                           AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                                           String attendanceType,
                                                           String concreteType,
                                                           int isAttendance,
                                                           String leaveType,
                                                           BigDecimal leavePercentSalary,
                                                           Integer stage,
                                                           BigDecimal leaveMinutes,
                                                           BigDecimal attendanceMinutes,
                                                           BigDecimal overtimeMinutes,
                                                           Long formId) {
        AttendanceEmployeeDetailDO userAttendance = new AttendanceEmployeeDetailDO();
        Date date = DateHelper.transferDayIdToDate(calculateHandlerDTO.getAttendanceDayId());
        userAttendance.setId(defaultIdWorker.nextId());
        userAttendance.setUserId(user.getId());
        userAttendance.setLocationCountry(user.getLocationCountry());
        userAttendance.setYear((long) DateUtil.year(date));
        userAttendance.setMonth((long) (DateUtil.month(date) + 1));
        userAttendance.setDay(DateUtil.dayOfMonth(date));
        userAttendance.setDayId(calculateHandlerDTO.getAttendanceDayId());
        userAttendance.setDate(DateUtil.beginOfDay(date));
        userAttendance.setDataSource(AttendanceDataSourceEnum.SYSTEM.getCode());
        userAttendance.setAttendanceType(attendanceType);
        userAttendance.setConcreteType(concreteType);
        userAttendance.setIsAttendance(isAttendance);
        userAttendance.setDeptId(user.getDeptId());
        userAttendance.setPostId(user.getPostId());
        userAttendance.setLeaveType(leaveType);
        userAttendance.setLeavePercentSalary(leavePercentSalary);
        userAttendance.setStage(stage);
        userAttendance.setLeaveMinutes(leaveMinutes);
        userAttendance.setAttendanceMinutes(attendanceMinutes);
        userAttendance.setOvertimeMinutes(overtimeMinutes);
        userAttendance.setFormId(formId);
        userAttendance.setClassId(calculateHandlerDTO.getClassId());
        BaseDOUtil.fillDOInsertByUsrOrSystem(userAttendance);
        return userAttendance;
    }

    private BigDecimal dayExistMinutes(List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList) {
        BigDecimal usedMinutes = BigDecimal.ZERO;
        for (AttendanceEmployeeDetailDO detailDO : attendanceEmployeeDetailDOList) {
            if (detailDO.getFormId() == null) {
                continue;
            }
            if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                usedMinutes = usedMinutes.add(detailDO.getAttendanceMinutes());
            }
            if (detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                usedMinutes = usedMinutes.add(detailDO.getLeaveMinutes());
            }
        }
        return usedMinutes;
    }
}
