package com.imile.attendance.abnormal;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserDimissionRecordManage;
import com.imile.attendance.employee.UserEntryRecordManage;
import com.imile.attendance.enums.AttendanceDayTypeEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.DimissionStatusEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.loader.StrategyLoader;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.vacation.CompanyLeaveConfigManage;
import com.imile.attendance.vacation.UserLeaveDetailManage;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 每日考勤结果计算服务
 *
 * <AUTHOR>
 * @since 2025/05/19
 */
@Slf4j
@Service
public class AttendanceGenerateService {
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserEntryRecordManage userEntryRecordManage;
    @Resource
    private UserDimissionRecordManage userDimissionRecordManage;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;
    @Resource
    private AttendanceEmployeeDetailManage employeeDetailManage;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private UserLeaveDetailManage userLeaveDetailManage;
    @Resource
    private CompanyLeaveConfigManage companyLeaveConfigManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;
    @Resource
    private AttendanceFormManage attendanceFormManage;


    /**
     * 每日考勤结果计算处理
     */
    public void attendanceCalculateHandler(AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        log.info("考勤计算入参：{}", calculateHandlerDTO);
        if (!calculateHandlerDTO.check()) {
            log.info("calculateHandlerDTO | 入参校验不通过");
            return;
        }

        //设置考勤计算相关时间参数
        buildAttendanceTime(calculateHandlerDTO);

        //查询有效的考勤用户
        List<UserInfoDO> userInfoDOList = selectEffectiveAttendanceUser(calculateHandlerDTO);

        if (CollectionUtils.isEmpty(userInfoDOList)) {
            log.info("calculateHandlerDTO | 员工不符合条件");
            return;
        }

        List<Long> userIdList = userInfoDOList
                .stream()
                .map(UserInfoDO::getId).collect(Collectors.toList());
        List<String> userCodeList = userInfoDOList
                .stream()
                .map(UserInfoDO::getUserCode).collect(Collectors.toList());

        Date finalDateNow = DateUtil.endOfDay(calculateHandlerDTO.getAttendanceTime());

        //查询所有员工的入职日期
        Map<Long, UserEntryRecordDO> userEntryMap = userEntryRecordManage.mapByUserIds(userIdList);
        //查询所有员工的离职信息
        Map<Long, UserDimissionRecordDO> userDimissionMap = userDimissionRecordManage.mapByUserIds(userIdList);
        //查询用户打卡记录
        Map<String, List<UserPunchRecordBO>> userPunchCardGroup = employeePunchRecordManage.mapByUserCodesAndTimeRange(calculateHandlerDTO.getStartTime(), calculateHandlerDTO.getEndTime(), userCodeList);
        //查询考勤正常出勤明细
        Map<Long, List<AttendanceEmployeeDetailDO>> userAttendanceEmployeeMap = employeeDetailManage.mapByUserIdsAndDayId(userIdList, calculateHandlerDTO.getAttendanceDayId());
        //查询考勤当天未处理的异常数据表(有些可能已经被发起审批了，在审批中)
        Map<Long, List<EmployeeAbnormalAttendanceDO>> userAbnormalMap = abnormalAttendanceManage.mapByUserIdsAndDayIds(userIdList, Collections.singletonList(calculateHandlerDTO.getAttendanceDayId()));
        //查询用户所在日历
        Map<Long, List<CalendarConfigDO>> userCalendarConfigMap = calendarManage.mapByUserIds(userIdList, finalDateNow);
        //查询用户考勤日历配置明细
        List<Long> calendarConfigIds = userCalendarConfigMap.values().stream()
                .flatMap(List::stream)
                .map(CalendarConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<CalendarConfigDetailDO>> attendanceConfigDetailMap = calendarManage.selectCalendarDetailsByConfigIds(calendarConfigIds)
                .stream().collect(Collectors.groupingBy(CalendarConfigDetailDO::getAttendanceConfigId));
        //查询用户打卡规则
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigManage.mapByUserIds(userIdList, finalDateNow);
        //查询用户班次规则
        Map<Long, List<PunchClassConfigDTO>> punchClassConfigMap = punchClassConfigManage.mapByUserIds(userIdList, finalDateNow);
        //查询用户假期详情
        List<UserLeaveDetailDO> userLeaveDetailList = userLeaveDetailManage.listByUserId(userIdList);
        Map<Long, List<UserLeaveDetailDO>> leaveDetailMap = userLeaveDetailList.stream().collect(Collectors.groupingBy(UserLeaveDetailDO::getUserId));
        //查询用户假期余额
        List<UserLeaveStageDetailDO> leaveStageDetailList = userLeaveStageDetailDao.selectByLeaveId(userLeaveDetailList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList()));
        // 获取所有假期
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigManage.selectAllActive();
        //查询员工周期内的排班数据(3天)
        List<UserShiftConfigDO> userShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(userIdList, calculateHandlerDTO.getStartDayId(), calculateHandlerDTO.getEndDayId());
        Map<Long, List<UserShiftConfigDO>> userShiftConfigMap = userShiftConfigList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
        //查询用户单据
        //查询用户审批通过的请假/外勤单据
        List<AttendanceFormDetailBO> attendanceFormDetailBOList = attendanceFormManage.listByUserIds(userIdList, Collections.singletonList(FormStatusEnum.PASS.getCode()), FormTypeEnum.getLeaveAndOutOfOfficeCodeList());
        //获取有效的审批单据ID
        List<Long> effectFormIdList = getEffectFormIdList(calculateHandlerDTO, attendanceFormDetailBOList);

        //遍历用户计算考勤结果
        for (UserInfoDO user : userInfoDOList) {
            //构建用户考勤计算上下文参数
            AttendanceCalculateContext calculateContext = buildAttendanceCalculateContext(calculateHandlerDTO.getAttendanceDayId(), user, effectFormIdList, userEntryMap, userDimissionMap,
                    userPunchCardGroup, userAttendanceEmployeeMap, userAbnormalMap, userCalendarConfigMap, attendanceConfigDetailMap, punchConfigMap, punchClassConfigMap, leaveDetailMap,
                    leaveStageDetailList, companyLeaveConfigList, userShiftConfigMap, attendanceFormDetailBOList);
            if (Objects.isNull(calculateContext)) {
                continue;
            }

            //查询用户当天的是否有排班
            List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList = calculateContext.getUserAttendancePunchConfigDTOList()
                    .stream().filter(item -> item.getDayId().equals(calculateHandlerDTO.getAttendanceDayId())).collect(Collectors.toList());

            AttendanceCalculateService calculateService = StrategyLoader.load(AttendanceCalculateService.class, t -> t.isMatch(userAttendancePunchConfigList, calculateContext.getPunchConfigDO().getConfigType()));
            calculateService.execute(calculateContext);
        }
    }

    private AttendanceCalculateContext buildAttendanceCalculateContext(Long attendanceDayId,
                                                                       UserInfoDO user,
                                                                       List<Long> effectFormIdList,
                                                                       Map<Long, UserEntryRecordDO> userEntryMap,
                                                                       Map<Long, UserDimissionRecordDO> userDimissionMap,
                                                                       Map<String, List<UserPunchRecordBO>> userPunchCardGroup,
                                                                       Map<Long, List<AttendanceEmployeeDetailDO>> userAttendanceEmployeeMap,
                                                                       Map<Long, List<EmployeeAbnormalAttendanceDO>> userAbnormalMap,
                                                                       Map<Long, List<CalendarConfigDO>> userCalendarConfigMap,
                                                                       Map<Long, List<CalendarConfigDetailDO>> attendanceConfigDetailMap,
                                                                       Map<Long, PunchConfigDO> punchConfigMap,
                                                                       Map<Long, List<PunchClassConfigDTO>> punchClassConfigMap,
                                                                       Map<Long, List<UserLeaveDetailDO>> leaveDetailMap,
                                                                       List<UserLeaveStageDetailDO> leaveStageDetailList,
                                                                       List<CompanyLeaveConfigDO> companyLeaveConfigList,
                                                                       Map<Long, List<UserShiftConfigDO>> userShiftConfigMap,
                                                                       List<AttendanceFormDetailBO> attendanceFormDetailBOList) {
        AttendanceCalculateContext calculateContext = new AttendanceCalculateContext();
        calculateContext.setUser(user);
        //个人入职信息
        UserEntryRecordDO entryRecordDO = userEntryMap.get(user.getId());
        if (ObjectUtil.isNull(entryRecordDO)) {
            log.info("error userCode:{}, date:{}, 该用户没有入职信息", user.getUserCode(), attendanceDayId);
        }
        if (ObjectUtil.isNotNull(entryRecordDO) && entryRecordDO.getConfirmDate() != null) {
            Long entryDayId = DateHelper.getDayId(entryRecordDO.getConfirmDate());
            if (entryDayId.compareTo(attendanceDayId) > 0) {
                log.info("error userCode:{}, date:{}, 该用户当天还没入职", user.getUserCode(), attendanceDayId);
                return null;
            }
        }
        //个人离职信息
        UserDimissionRecordDO dimissionRecordDO = userDimissionMap.get(user.getId());
        if (user.getDisabledDate() == null && StringUtils.equalsIgnoreCase(StatusEnum.DISABLED.getCode(), user.getStatus())) {
            log.info("error userCode:{}, date:{}, 用户停用，无停用时间", user.getUserCode(), attendanceDayId);
            return null;
        }
        if (user.getDisabledDate() != null && StringUtils.equalsIgnoreCase(StatusEnum.DISABLED.getCode(), user.getStatus())) {
            Long disabledDayId = DateHelper.getDayId(user.getDisabledDate());
            if (disabledDayId.compareTo(attendanceDayId) < 1) {
                log.info("error userCode:{}, date:{},用户停用，并且停用时间在入参时间之前", user.getUserCode(), attendanceDayId);
                return null;
            }
        }
        if (Objects.nonNull(dimissionRecordDO)
                && StringUtils.equalsIgnoreCase(DimissionStatusEnum.DIMISSION.getCode(), dimissionRecordDO.getDimissionStatus())
                && Objects.nonNull(dimissionRecordDO.getActualDimissionDate())) {
            Long actualDimissionDayId = DateHelper.getDayId(dimissionRecordDO.getActualDimissionDate());
            if (actualDimissionDayId.compareTo(attendanceDayId) < 0) {
                log.info("error userCode:{}, date:{},用户离职", user.getUserCode(), attendanceDayId);
                return null;
            }
        }

        //个人考勤日历配置数据
        List<CalendarConfigDO> calendarConfigDOList = userCalendarConfigMap.get(user.getId());
        if (CollectionUtils.isEmpty(calendarConfigDOList)) {
            log.info("error userCode:{}, date:{}, 这一天用户没有配置日历规则", user.getUserCode(), attendanceDayId);
            return null;
        }
        if (calendarConfigDOList.size() > 1) {
            log.info("error userCode:{}, date:{}, 这一天用户有多个生效的日历规则", user.getUserCode(), attendanceDayId);
            return null;
        }
        calculateContext.setCalendarConfigDO(calendarConfigDOList.get(0));

        //查询当天对应的打卡规则
        PunchConfigDO punchConfigDO = punchConfigMap.get(user.getId());
        if (Objects.isNull(punchConfigDO)) {
            log.info("error userCode:{}, date:{}, 这个用户当天没有配置打卡规则", user.getUserCode(), attendanceDayId);
            return null;
        }
        calculateContext.setPunchConfigDO(punchConfigDO);

        List<PunchClassConfigDTO> punchClassConfigDTOList = punchClassConfigMap.get(user.getId());
        if (CollectionUtils.isEmpty(punchClassConfigDTOList)) {
            log.info("error userCode:{}, date:{}, 这一天用户没有配置班次", user.getUserCode(), attendanceDayId);
            return null;
        }

        //获取用户这3天的排班（可以为空，3天都未排班）
        List<UserShiftConfigDO> userShiftConfigs = userShiftConfigMap.getOrDefault(user.getId(), Collections.emptyList());
        calculateContext.setUserShiftConfigList(userShiftConfigs);

        List<UserAttendancePunchConfigDTO> userAttendancePunchConfigDTOList = new ArrayList<>();
        boolean tag = false;
        for (UserShiftConfigDO userShiftConfigDO : userShiftConfigs) {
            UserAttendancePunchConfigDTO userAttendancePunchConfigDTO = new UserAttendancePunchConfigDTO();
            userAttendancePunchConfigDTOList.add(userAttendancePunchConfigDTO);
            userAttendancePunchConfigDTO.setUserId(userShiftConfigDO.getUserId());
            userAttendancePunchConfigDTO.setDayId(userShiftConfigDO.getDayId());
            userAttendancePunchConfigDTO.setIsActualPunch(BusinessConstant.Y);
            userAttendancePunchConfigDTO.setDayPunchType(userShiftConfigDO.getDayShiftRule());
            //当天排班OFF/PH
            if (userShiftConfigDO.getPunchClassConfigId() == null) {
                userAttendancePunchConfigDTO.setIsActualPunch(BusinessConstant.N);
                continue;
            }
            List<PunchClassConfigDTO> existPunchClassConfigList = punchClassConfigDTOList.stream().filter(item -> item.getId().equals(userShiftConfigDO.getPunchClassConfigId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existPunchClassConfigList)) {
                log.info("error userCode:{}, date:{}, 这个用户当天有排班，排班的打卡规则也存在，但班次记录不存在", user.getUserCode(), attendanceDayId);
                tag = true;
                break;
            }
            if (existPunchClassConfigList.size() > 1) {
                log.info("error userCode:{}, date:{}, 这个用户当天有排班，排班的打卡规则也存在，班次记录存在但大于1", user.getUserCode(), attendanceDayId);
                tag = true;
                break;
            }
            PunchClassConfigDTO punchClassConfigDTO = existPunchClassConfigList.get(0);
            if (punchClassConfigDTO.getAttendanceHours() == null || punchClassConfigDTO.getLegalWorkingHours() == null) {
                log.info("error userCode:{}, date:{}, 这个用户当天有排班，排班的打卡规则也存在，班次记录也存在，但班次中没有出勤/法定时间", user.getUserCode(), attendanceDayId);
                tag = true;
                break;
            }
            userAttendancePunchConfigDTO.setClassConfigDO(punchClassConfigDTO);
        }
        if (tag) {
            return null;
        }
        calculateContext.setUserAttendancePunchConfigDTOList(userAttendancePunchConfigDTOList);

        //日历对应的具体信息,可以为空，表示改天是出勤日
        List<CalendarConfigDetailDO> calendarConfigDetailDOList = attendanceConfigDetailMap.get(calendarConfigDOList.get(0).getId());
        String attendanceType = AttendanceDayTypeEnum.PRESENT.name();
        if (CollectionUtils.isNotEmpty(calendarConfigDetailDOList)) {
            Optional<CalendarConfigDetailDO> attendanceConfigDetailDOOptional = calendarConfigDetailDOList
                    .stream()
                    .filter(attendanceConfig -> Objects.equals(attendanceConfig.getDayId(), attendanceDayId))
                    .findFirst();
            if (attendanceConfigDetailDOOptional.isPresent()) {
                attendanceType = attendanceConfigDetailDOOptional.get().getDayType();
            }
        }
        calculateContext.setAttendanceType(attendanceType);

        //个人当天正常出勤数据
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = userAttendanceEmployeeMap.getOrDefault(user.getId(), Collections.emptyList());
        calculateContext.setAttendanceEmployeeDetailDOList(attendanceEmployeeDetailDOList);

        //用户当天未处理的异常考勤数据(待处理/审核中)
        List<EmployeeAbnormalAttendanceDO> userAbnormalAttendanceDOList = userAbnormalMap.getOrDefault(user.getId(), Collections.emptyList());
        calculateContext.setUserAbnormalAttendanceDOList(userAbnormalAttendanceDOList);

        //查询用户3天的打卡数据
        List<UserPunchRecordBO> punchRecordDOList = userPunchCardGroup.getOrDefault(user.getUserCode(), Collections.emptyList());
        calculateContext.setPunchRecordDOList(punchRecordDOList);

        //获取员工的假期信息
        List<UserLeaveDetailDO> userLeaveDetailDOList = leaveDetailMap.getOrDefault(user.getId(), Collections.emptyList());
        calculateContext.setUserLeaveDetailDOList(userLeaveDetailDOList);

        //获取员工的假期余额
        List<Long> userLeaveIdList = userLeaveDetailDOList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
        List<UserLeaveStageDetailDO> userLeaveStageDetailDOList = leaveStageDetailList.stream().filter(item -> userLeaveIdList.contains(item.getLeaveId())).collect(Collectors.toList());
        calculateContext.setUserLeaveStageDetailDOList(userLeaveStageDetailDOList);

        // 筛选用户假期
        List<Long> userLeaveConfigIds = userLeaveDetailDOList.stream().map(UserLeaveDetailDO::getConfigId).collect(Collectors.toList());
        List<CompanyLeaveConfigDO> userCompanyLeaveConfigDOList = companyLeaveConfigList.stream().filter(item -> userLeaveConfigIds.contains(item.getId())).collect(Collectors.toList());
        calculateContext.setUserCompanyLeaveConfigDOList(userCompanyLeaveConfigDOList);

        //用户周期审批通过的请假/外勤单据
        List<AttendanceFormDetailBO> userPassFormBOList = attendanceFormDetailBOList.stream()
                .filter(formDetail -> Objects.nonNull(formDetail.getFormDO())
                        && effectFormIdList.contains(formDetail.getFormDO().getId())
                        && Objects.equals(formDetail.getFormDO().getUserId(), user.getId()))
                .collect(Collectors.toList());
        calculateContext.setUserPassFormBOList(userPassFormBOList);
        return calculateContext;
    }


    private List<Long> getEffectFormIdList(AttendanceCalculateHandlerDTO calculateHandlerDTO, List<AttendanceFormDetailBO> attendanceFormDetailBOList) {
        List<Long> effectFormIdList = new ArrayList<>();
        //查询请假周期包含当天的审批通过单据，并且没有被销假(这里不能用当天，要用周期，因为如果班次跨天，如果请了第二天的假，也是算前一天的)
        for (AttendanceFormDetailBO formDO : attendanceFormDetailBOList) {
            List<AttendanceFormAttrDO> formAttrDOList = formDO.getAttrDOList();
            if (CollectionUtils.isEmpty(formAttrDOList)) {
                continue;
            }
            List<AttendanceFormAttrDO> isRevokeDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(isRevokeDO) && StringUtils.isNotBlank(isRevokeDO.get(0).getAttrValue()) && isRevokeDO.get(0).getAttrValue().equals(BusinessConstant.Y.toString())) {
                continue;
            }
            List<AttendanceFormAttrDO> leaveStartDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
            List<AttendanceFormAttrDO> leaveEndDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
            List<AttendanceFormAttrDO> outOfOfficeStartDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
            List<AttendanceFormAttrDO> outOfOfficeEndDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());

            AttendanceFormDO attendanceFormDO = formDO.getFormDO();
            //查询请假时间是不是包含本次周期
            //有交集就可以
            if (CollectionUtils.isNotEmpty(leaveStartDateDO) && CollectionUtils.isNotEmpty(leaveEndDateDO)) {
                Long leaveStartDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateDO.get(0).getAttrValue()));
                Long leaveEndDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateDO.get(0).getAttrValue()));
                if (leaveEndDayId.compareTo(calculateHandlerDTO.getStartDayId()) < 0 || leaveStartDayId.compareTo(calculateHandlerDTO.getEndDayId()) > 0) {
                    continue;
                }
                effectFormIdList.add(attendanceFormDO.getId());
            }
            if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDO) && CollectionUtils.isNotEmpty(outOfOfficeEndDateDO)) {
                Long outOfOfficeStartDate = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateDO.get(0).getAttrValue()));
                Long outOfOfficeEndDate = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateDO.get(0).getAttrValue()));
                if (outOfOfficeEndDate.compareTo(calculateHandlerDTO.getStartDayId()) < 0 || outOfOfficeStartDate.compareTo(calculateHandlerDTO.getEndDayId()) > 0) {
                    continue;
                }
                effectFormIdList.add(attendanceFormDO.getId());
            }
        }
        return effectFormIdList;
    }


    private void buildAttendanceTime(AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        //开始时间 考勤日期 -1
        Long startDayId = DateHelper.getPreviousDayId(calculateHandlerDTO.getAttendanceDayId());
        calculateHandlerDTO.setStartDayId(startDayId);
        calculateHandlerDTO.setStartTime(DateHelper.beginOfDay(DateHelper.transferDayIdToDate(startDayId)));
        //结束时间 考勤日期 +1
        Long endDayId = DateHelper.getNextDayId(calculateHandlerDTO.getAttendanceDayId());
        calculateHandlerDTO.setEndDayId(endDayId);
        calculateHandlerDTO.setEndTime(DateHelper.endOfDay(DateHelper.transferDayIdToDate(endDayId)));

        calculateHandlerDTO.setAttendanceTime(DateHelper.beginOfDay(calculateHandlerDTO.getAttendanceTime()));
    }

    private List<UserInfoDO> selectEffectiveAttendanceUser(AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        List<UserInfoDO> userInfoDOList;
        if (StringUtils.isNotBlank(calculateHandlerDTO.getUserCodes())) {
            userInfoDOList = userInfoDao.listByUserCodes(calculateHandlerDTO.strConvertList(calculateHandlerDTO.getUserCodes()));
        } else {
            userInfoDOList = userInfoDao.listByLocationCountrys(calculateHandlerDTO.strConvertList(calculateHandlerDTO.getCountryList()));
        }
        //过滤司机和仓内劳务员工 仓内考勤和司机走另外的计算方法
        userInfoDOList = userInfoDOList
                .stream()
                .filter(item -> Objects.equals(BusinessConstant.N, item.getIsDriver()) && !isWarehouseSupportUser(item))
                .collect(Collectors.toList());
        return userInfoDOList;
    }

    public Boolean isWarehouseSupportUser(UserInfoDO userInfoDO) {
        return ObjectUtil.equal(userInfoDO.getIsWarehouseStaff(), BusinessConstant.Y)
                && EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE.contains(userInfoDO.getEmployeeType())
                && Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(userInfoDO.getLocationCountry())
                && ObjectUtil.notEqual(userInfoDO.getIsDriver(), BusinessConstant.Y);
    }
}
