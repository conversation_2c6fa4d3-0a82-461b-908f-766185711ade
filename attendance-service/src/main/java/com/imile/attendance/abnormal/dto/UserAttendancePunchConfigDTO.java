package com.imile.attendance.abnormal.dto;

import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import lombok.Data;


/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-23
 * @version: 1.0
 */
@Data
public class UserAttendancePunchConfigDTO {

    private Long userId;

    private Long dayId;

    /**
     * 当天是真正的排班还是OFF/PH?
     */
    private Integer isActualPunch;

    /**
     * 当天的排班
     */
    private String dayPunchType;

    /**
     * 当天对应规则的班次
     */
    private PunchClassConfigDTO classConfigDO;
}
