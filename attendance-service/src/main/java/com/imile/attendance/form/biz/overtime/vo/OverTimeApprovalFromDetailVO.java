package com.imile.attendance.form.biz.overtime.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeApprovalFromDetailVO
 * {@code @since:} 2024-06-13 20:57
 * {@code @description:}
 */
@Data
public class OverTimeApprovalFromDetailVO {

    /**
     * 日期：年月日
     */
    private Long dayId;

    /**
     * 关联表业务code（被申请人user_code）
     */
    private String userCode;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * 预计加班时长(页面显示小时数)
     */
    private BigDecimal estimateDuration;

    // 冗余字段

    /**
     * 被申请人id
     */
    private Long userId;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人部门名称
     */
    private String deptName;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 被申请人岗位名称
     */
    private String postName;

    /**
     * 工作加班开始时间
     */
    private BigDecimal workingOutStartTime;

    /**
     * 工作日最长有效加班时间
     */
    private BigDecimal workingEffectiveTime;

    /**
     * 工作日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String workingSubsidyType;

    /**
     * 休息日最长有效加班时间
     *
     */
    private BigDecimal restEffectiveTime;

    /**
     * 休息日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String restSubsidyType;

    /**
     * 节假日最长有效加班时间
     *
     */
    private BigDecimal holidayEffectiveTime;

    /**
     * 节假日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String holidaySubsidyType;

    /**
     * 最早打卡记录
     */
    private String earliestPunchTime;

    /**
     * 最晚打卡记录
     */
    private String latestPunchTime;

}
