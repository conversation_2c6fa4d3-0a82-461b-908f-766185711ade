package com.imile.attendance.form.biz.reissueCard.param;

import com.imile.attendance.form.param.BaseFormParam;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Data
public class AddDurationParam extends BaseFormParam {

    /**
     * 异常考勤ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long abnormalId;

    /**
     * 实际出勤时长
     */
    private BigDecimal actualAttendanceTime;

    /**
     * 实际工作时长
     */
    private BigDecimal actualWorkingHours;

    /**
     * 更新后实际出勤时长
     */
    private BigDecimal newActualAttendanceTime;

    /**
     * 更新后实际工作时长
     */
    private BigDecimal newActualWorkingHours;
}
