package com.imile.attendance.form.vo;


import com.imile.attendance.form.dto.ClashApplicationInfoDTO;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Data
public class DurationDetailVO {
    /**
     * 冲突单据信息
     */
    private List<ClashApplicationInfoDTO> clashApplicationInfoDTOList;

    /**
     * 每天时长计算明细
     */
    private List<DayDurationInfoDTO> dayDurationInfoDTOList;


    /**
     * 用户预计请假/外勤时长
     */
    private String expectedTime;
}
