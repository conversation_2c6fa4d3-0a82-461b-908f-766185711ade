package com.imile.attendance.form.biz.reissueCard.vo;

import com.imile.attendance.form.biz.reissueCard.service.UserDayReissueAbnormalDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-19
 * @version: 1.0
 */
@Data
public class UserDayReissueInfoVO {

    /**
     * 班次主键
     */
    private Long punchClassId;

    /**
     * 规则主键
     */
    private Long punchConfigId;

    /**
     * 打卡规则类型
     */
    private String punchConfigType;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 当前补卡日期对应的考勤周期起始时间
     */
    private Date attendanceStartDate;

    /**
     * 当前补卡日期对应的考勤周期截止时间
     */
    private Date attendanceEndDate;

    /**
     * 打卡规则对应的班次的所有的时刻信息
     */
    private String punchConfigClassItemInfo;

    /**
     * 班次最早打卡时间
     */
    private Date earliestPunchTime;


    /**
     * 班次最晚打卡时间
     */
    private Date latestPunchTime;

    /**
     * 剩余可用补卡次数
     */
    private Integer residueReissueCardCount;

    /**
     * 实际最早打卡时间
     */
    private Date actualEarliestPunchTime;


    /**
     * 实际最晚打卡时间
     */
    private Date actualLatestPunchTime;

    /**
     * 异常考勤信息
     */
    private List<UserDayReissueAbnormalDTO> userDayReissueAbnormalDTOList;
}
