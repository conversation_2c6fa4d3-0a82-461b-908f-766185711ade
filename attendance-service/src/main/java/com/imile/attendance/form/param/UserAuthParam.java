package com.imile.attendance.form.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-19
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAuthParam {

    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 姓名和账号模糊查询
     */
    private String keyword;


    //业务逻辑，前端无需传递
    private Boolean isFindAll;
}
