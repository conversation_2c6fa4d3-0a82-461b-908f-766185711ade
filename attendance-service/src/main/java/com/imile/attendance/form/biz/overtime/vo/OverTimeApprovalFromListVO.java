package com.imile.attendance.form.biz.overtime.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeApprovalFromListVO
 * {@code @since:} 2024-06-14 16:21
 * {@code @description:}
 */
@Data
public class OverTimeApprovalFromListVO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申请人编码
     */
    private String applyUserCode;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 申请单号
     */
    private String applicationCode;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * bpm审批单ID
     */
    private Long approvalId;

    /**
     * 审批节点信息
     */
    private String approvalProcessInfo;

    /**
     * 单据来源: 0:手动创建 1:导入
     */
    private Integer dataSource;

    /**
     * 提交人
     */
    private String createUserCode;

    /**
     * 提交人姓名
     */
    private String createUserName;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 最近修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;

}
