package com.imile.attendance.form.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-19
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAuthVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户部门
     */
    private Long deptId;

    /**
     * 用户部门
     */
    private String deptName;

    /**
     * 用户岗位
     */
    private Long postId;

    /**
     * 用户岗位
     */
    private String postName;

    /**
     * 用户所在国
     */
    private String country;


}
