package com.imile.attendance.form.biz.reissueCard;

import com.imile.attendance.infrastructure.repository.form.dao.AttendanceUserCardConfigDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceUserCardConfigDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/16
 * @Description 补卡次数配置服务
 */
@Slf4j
@Service
public class UserCardConfigService {
    @Resource
    AttendanceUserCardConfigDao userCardConfigDao;

    public List<AttendanceUserCardConfigDO> selectByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return userCardConfigDao.selectByUserIdList(userIdList);
    }
}
