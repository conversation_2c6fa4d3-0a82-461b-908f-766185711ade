package com.imile.attendance.form.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-30
 * @version: 1.0
 */
@Data
public class ApplicationFormDeleteParam {
    /**
     * 申请单据ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long formId;
}
