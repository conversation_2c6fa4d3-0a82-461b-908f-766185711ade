package com.imile.attendance.form.biz.leave.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.form.param.BaseFormParam;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Data
public class LeaveAddParam extends BaseFormParam {

    /**
     * 异常考勤ID
     */
    private Long abnormalId;

    /**
     * 假期规则主键
     */
    private Long configId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 请假开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaveStartDate;

    /**
     * 请假结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaveEndDate;

    /**
     * 用户假期可用余额(分钟)
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 请假单位
     */
    private String leaveUnit;

    /**
     * 最小请假时长
     */
    private Integer miniLeaveDuration;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 用户偶默认每天的出勤小时线
     */
    private BigDecimal dayAttendanceHours;

    /**
     * 是否上传附件
     */
    private Integer isUploadAttachment;

    /**
     * 上传附件条件：
     */
    private Long uploadAttachmentCondition;

    /**
     * 上传附件单位：DAYS-天 HOURS-小时 MINUTES-分钟
     */
    private String attachmentUnit;
}
