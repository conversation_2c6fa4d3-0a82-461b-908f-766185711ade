package com.imile.attendance.form.param;

import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/16
 * @Description 审批单通用参数
 */
@Data
public class BaseFormParam {

    /**
     * 申请单据ID(新增无需传)
     */
    private Long applicationFormId;

    /**
     * 申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long applyUserId;

    /**
     * 被申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 被申请人编码
     */
    private String userCode;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 被申请人所在国
     */
    private String country;

    /**
     * 被申请人所属结算国
     */
    private String originCountry;

    /**
     * 是否仓内员工
     */
    private Integer isWarehouseStaff;

    /**
     * 每天时长计算明细
     */
    private List<DayDurationInfoDTO> dayDurationInfoDTOList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 操作方式 0 暂存  1保存  2预览
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;

}
