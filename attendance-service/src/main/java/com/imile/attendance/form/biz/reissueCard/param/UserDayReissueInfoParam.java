package com.imile.attendance.form.biz.reissueCard.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-19
 * @version: 1.0
 */
@Data
public class UserDayReissueInfoParam {

    /**
     * 用户ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 指定天
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long dayId;

    /**
     * 当前日期(时间戳)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateTime;
}
