package com.imile.attendance.form.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-26
 * @version: 1.0
 */
@Data
public class ApplicationFormDetailParam {
    /**
     * 申请单据ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long formId;
}
