package com.imile.attendance.form.biz.reissueCard.service;

import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-19
 * @version: 1.0
 */
@Data
public class UserDayReissueAbnormalDTO {
    /**
     * 异常ID
     */
    private Long abnormalId;

    /**
     * 补卡类型
     */
    private String reissueCardType;

    /**
     * 补卡类型描述
     */
    private String reissueCardTypeDesc;

    /**
     * 补卡类型详细描述
     */
    private String reissueCardTypeDetail;

    /**
     * 实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    private Date actualPunchTime;

    /**
     * 补卡后的时间(根据时刻时间来补)
     */
    private Date correctPunchTime;
}
