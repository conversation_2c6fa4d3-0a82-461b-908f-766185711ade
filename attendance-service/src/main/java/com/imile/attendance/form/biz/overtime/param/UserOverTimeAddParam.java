package com.imile.attendance.form.biz.overtime.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * {@code @author:} allen
 * {@code @className:} UserOverTimeAddParam
 * {@code @since:} 2024-06-12 17:05
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserOverTimeAddParam extends OverTimeAddParam {

    /**
     * 申请单id(新增不用传)
     */
    private Long formId;

    /**
     * 被申请人主键id
     */
    private Long userId;

    /**
     * 被申请人编码
     */
    private String userCode;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 被申请人所在国
     */
    private String userCountry;

    /**
     * 被申请人结算国
     */
    private String userOriginCountry;


    /**
     * 申请人所在国
     */
    private String applyUserCountry;
}
