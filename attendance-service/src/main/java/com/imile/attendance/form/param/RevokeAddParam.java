package com.imile.attendance.form.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-30
 * @version: 1.0
 */
@Data
public class RevokeAddParam {

    /**
     * 需要撤销的申请单据ID
     */
    private Long applicationFormId;

    /**
     * 需要撤销的申请单据编码
     */
    private String applicationFormCode;

    /**
     * 申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long applyUserId;

    /**
     * 撤销理由
     */
    private String revokeReason;

    /**
     * 操作方式   1保存  2预览
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;

    //后端逻辑处理需要的字段
    /**
     * 异常考勤ID
     */
    private Long abnormalId;
}
