package com.imile.attendance.clock.dto;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Data
public class MobileFormDTO {

    private Long id;

    /**
     * 审批单id
     */
    private Long approvalId;

    /**
     * 申请单号
     */
    private String applicationCode;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 请假开始时间
     */
    private String leaveStartDate;

    /**
     * 请假结束时间
     */
    private String leaveEndDate;

    /**
     * 外勤开始时间
     */
    private String outOfOfficeStartDate;

    /**
     * 外勤结束时间
     */
    private String outOfOfficeEndDate;

    /**
     * 补卡日期
     */
    private Long reissueCardDayId;
}
