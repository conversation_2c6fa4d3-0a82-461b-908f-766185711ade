package com.imile.attendance.clock.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MobileAbnormalDTO {

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 打卡班次id
     */
    private Long punchClassConfigId;

    /**
     * 打卡班次时段id
     */
    private Long punchClassItemConfigId;
}
