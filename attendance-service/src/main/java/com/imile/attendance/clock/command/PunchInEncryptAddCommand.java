package com.imile.attendance.clock.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> chen
 * @Date 2025/5/21 
 * @Description
 */
@Data
public class PunchInEncryptAddCommand {

    /**
     * AES的iv值，前端每次动态生成
     */
    @NotBlank(message = "iv不能为空")
    private String ivStr;

    /**
     * 加密后的报文
     */
    @NotBlank(message = "content不能为空")
    private String content;
}
