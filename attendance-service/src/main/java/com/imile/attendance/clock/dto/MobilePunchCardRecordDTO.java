package com.imile.attendance.clock.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Data
public class MobilePunchCardRecordDTO {

    private Long id;

    /**
     * 日期
     */
    private String dayId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 数据来源
     */
    private String sourceType;

    /**
     * 打卡区域
     */
    private String punchArea;

    /**
     * 打卡方式
     */
    private String punchCardType;

    /**
     * 打卡时间
     */
    private Date punchTime;

    /**
     * 班次id
     */
    private Long classId;

    /**
     * 班次详情id
     */
    private Long classItemId;

    /**
     * wifi配置ID
     */
    private Long wifiConfigId;

    /**
     * gps配置ID
     */
    private Long gpsConfigId;

    /**
     * wifi配置名称
     */
    private String wifiConfigName;

    /**
     * gps配置名称
     */
    private String gpsConfigName;
}
