package com.imile.attendance.clock.query;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/5/23 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MobileDayPunchDetailQuery extends MobilePunchDetailQuery {

    /**
     * 考勤日
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long dayId;
}
