package com.imile.attendance.clock.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chen
 * @Date 2025/5/23 
 * @Description
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MobileAbnormalVO {

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 打卡班次id
     */
    private Long punchClassConfigId;

    /**
     * 打卡班次时段id
     */
    private Long punchClassItemConfigId;
}
