package com.imile.attendance.clock.mapstruct;

import com.imile.attendance.clock.dto.MobileAbnormalDTO;
import com.imile.attendance.clock.dto.MobileFormDTO;
import com.imile.attendance.clock.dto.MobilePunchCardRecordDTO;
import com.imile.attendance.clock.dto.PunchClassConfigDTO;
import com.imile.attendance.clock.dto.UserDayMobilePunchDetailDTO;
import com.imile.attendance.clock.dto.UserMobileRuleConfigDTO;
import com.imile.attendance.clock.vo.MobileAbnormalVO;
import com.imile.attendance.clock.vo.MobileFormVO;
import com.imile.attendance.clock.vo.MobilePunchCardRecordVO;
import com.imile.attendance.clock.vo.PunchClassConfigVO;
import com.imile.attendance.clock.vo.PunchClassItemConfigVO;
import com.imile.attendance.clock.vo.UserDayMobilePunchDetailVO;
import com.imile.attendance.clock.vo.UserMobileRuleConfigVO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.punch.vo.PunchCardRecordVO;
import com.imile.attendance.util.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface MobileConfigMapstruct {

    MobileConfigMapstruct INSTANCE = Mappers.getMapper(MobileConfigMapstruct.class);


    MobilePunchCardRecordDTO toMobilePunchCardRecordDTO(EmployeePunchRecordDO punchRecordDO);

    List<MobilePunchCardRecordDTO> toMobilePunchCardRecordDTO(List<EmployeePunchRecordDO> punchRecordDOList);


    default MobilePunchCardRecordDTO toMobilePunchCardRecordDTO(EmployeePunchRecordDO punchRecord,
                                                                PunchClassItemConfigDTO classItemConfigDTO) {
        MobilePunchCardRecordDTO dto = toMobilePunchCardRecordDTO(punchRecord);
        dto.setClassId(classItemConfigDTO.getPunchClassId());
        dto.setClassItemId(classItemConfigDTO.getId());
        return dto;
    }


    PunchClassConfigDTO toPunchClassConfigDTO(PunchClassConfigDO punchClassConfigDO);

    List<PunchClassConfigDTO> toPunchClassConfigDTO(List<PunchClassConfigDO> punchClassConfigDOList);


    default UserMobileRuleConfigDTO toRuleConfigDTO(PunchConfigDO userPunchConfigDO, ReissueCardConfigDO userReissueCardConfigDO) {
        UserMobileRuleConfigDTO userMobileRuleConfigDTO = new UserMobileRuleConfigDTO();
        //打卡规则一般不为空
        if (null != userPunchConfigDO) {
            userMobileRuleConfigDTO.setPunchConfigNo(userPunchConfigDO.getConfigNo());
            userMobileRuleConfigDTO.setPunchConfigName(userPunchConfigDO.getConfigName());
            userMobileRuleConfigDTO.setPunchConfigType(userPunchConfigDO.getConfigType());
        }
        if (null != userReissueCardConfigDO) {
            userMobileRuleConfigDTO.setMaxRepunchNumber(userReissueCardConfigDO.getMaxRepunchNumber());
        }
        return userMobileRuleConfigDTO;
    }

    // DTO 到 VO 的映射方法
    UserMobileRuleConfigVO toUserMobileRuleConfigVO(UserMobileRuleConfigDTO dto);

    PunchClassConfigVO toPunchClassConfigVO(PunchClassConfigDTO dto);

    PunchClassItemConfigVO toPunchClassItemConfigVO(PunchClassItemConfigDTO dto);

    List<PunchClassItemConfigVO> toPunchClassItemConfigVO(List<PunchClassItemConfigDTO> dtoList);

    MobilePunchCardRecordVO toMobilePunchCardRecordVO(MobilePunchCardRecordDTO dto);

    List<MobilePunchCardRecordVO> toMobilePunchCardRecordVO(List<MobilePunchCardRecordDTO> dtoList);

    MobileFormVO toMobileFormVO(MobileFormDTO dto);

    List<MobileFormVO> toMobileFormVO(List<MobileFormDTO> dtoList);

    MobileAbnormalVO toMobileAbnormalVO(MobileAbnormalDTO dto);

    List<MobileAbnormalVO> toMobileAbnormalVO(List<MobileAbnormalDTO> dtoList);

    default UserDayMobilePunchDetailVO toUserDayMobilePunchDetailVO(UserDayMobilePunchDetailDTO dto){
        UserDayMobilePunchDetailVO detailVO = new UserDayMobilePunchDetailVO();
        detailVO.setUserId(dto.getUserId());
        detailVO.setLocationCountry(dto.getLocationCountry());
        detailVO.setDayId(dto.getDayId());
        detailVO.setRuleConfigVO(toUserMobileRuleConfigVO(dto.getRuleConfigDTO()));
        detailVO.setPunchClassConfigVO(toPunchClassConfigVO(dto.getPunchClassConfigDTO()));
        detailVO.setPunchClassItemConfigVO(CollectionUtils.convert(dto.getPunchClassItemConfigDTO(), PunchClassItemConfigVO.class));
        detailVO.setPunchCardRecordVO(CollectionUtils.convert(dto.getPunchCardRecordDTO(), MobilePunchCardRecordVO.class));
        detailVO.setMobileFormVOList(CollectionUtils.convert(dto.getMobileFormDTOList(), MobileFormVO.class));
        detailVO.setAbnormalPunchList(CollectionUtils.convert(dto.getPunchCardRecordDTO(), MobileAbnormalVO.class));
        return detailVO;
    }
}
