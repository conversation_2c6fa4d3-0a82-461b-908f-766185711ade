package com.imile.attendance.cycleConfig.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 考勤周期检查结果
 */
@Data
public class AttendanceCycleCheckResult {

    /** 是否在有效周期内 */
    private boolean inValidCycle;

    /** 考勤周期开始日期 */
    private Date cycleStartDate;

    /** 考勤周期结束日期 */
    private Date cycleEndDate;

    /** 最晚有效申报日期 */
    private Date latestValidDate;
}
