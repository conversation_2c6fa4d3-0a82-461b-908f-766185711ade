package com.imile.attendance.cycleConfig.application;

import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigAddCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigStatusSwitchCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigUpdateCommand;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleConfigDetailDTO;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleConfigPageDTO;
import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.cycleConfig.mapstruct.AttendanceCycleConfigMapstruct;
import com.imile.attendance.cycleConfig.query.AttendanceCycleConfigDetailQuery;
import com.imile.attendance.cycleConfig.query.AttendanceCycleConfigListQuery;
import com.imile.attendance.cycleConfig.vo.CycleDetailSelectVO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.common.page.PaginationResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@Service
public class AttendanceCycleConfigApplicationService {

    @Resource
    private AttendanceCycleConfigService cycleConfigService;

    /**
     * 获取考勤周期详情列表
     *
     * @param cycleType 考勤周期类型
     * @return 考勤周期详情列表
     */
    public List<CycleDetailSelectVO> getCycleDetailList(String cycleType) {
        List<CycleTypeEnum.CycleDetail> cycleDetailList = cycleConfigService.getCycleDetailList(cycleType);
        return AttendanceCycleConfigMapstruct.INSTANCE.toCycleDetailSelectVO(cycleDetailList);
    }


    public void add(AttendanceCycleConfigAddCommand addCommand) {
        cycleConfigService.add(addCommand);
    }

    public void update(AttendanceCycleConfigUpdateCommand updateCommand) {
        cycleConfigService.update(updateCommand);
    }

    public void statusSwitch(AttendanceCycleConfigStatusSwitchCommand switchCommand) {
        cycleConfigService.statusSwitch(switchCommand);
    }

    public AttendanceCycleConfigDetailDTO detail(AttendanceCycleConfigDetailQuery detailQuery) {
        return cycleConfigService.detail(detailQuery);
    }

    public PaginationResult<AttendanceCycleConfigPageDTO> list(AttendanceCycleConfigListQuery listQuery) {
        return cycleConfigService.list(listQuery);
    }

    public AttendanceCycleConfigDO getByCountryAndCycleType(String country, Integer cycleType) {
        return cycleConfigService.getByCountryAndCycleType(country, cycleType);
    }

    /**
     * 获取考勤周期配置
     *
     * @param userId 用户ID
     * @return HrmsAttendanceCycleConfigDO
     */
    public AttendanceCycleConfigDO getUserAttendanceCycleConfig(Long userId) {
        return cycleConfigService.getUserAttendanceCycleConfig(userId);
    }

    /**
     * 发放补卡次数逻辑：获取考勤周期
     * 获取考勤周期配置：补卡定时任务专属，这里不分国家，都是月维度
     *
     * @param userId 用户id
     * @return HrmsAttendanceCycleConfigDO
     */
    public AttendanceCycleConfigDO getUserAttendanceCycleConfigUserCard(Long userId) {
        return cycleConfigService.getUserAttendanceCycleConfigUserCard(userId);
    }

    /**
     * 获取当前时间所属考勤周期的时间点
     *
     * @param nowDayId      当前时间
     * @param cycleConfigDO 考勤周期配置
     * @return AttendanceDayCycleDTO
     */
    public AttendanceDayCycleDTO getUserAttendanceCycleConfigDay(Long nowDayId, AttendanceCycleConfigDO cycleConfigDO) {
        return cycleConfigService.getUserAttendanceCycleConfigDay(nowDayId, cycleConfigDO);
    }


}
