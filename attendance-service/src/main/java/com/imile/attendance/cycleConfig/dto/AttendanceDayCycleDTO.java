package com.imile.attendance.cycleConfig.dto;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class AttendanceDayCycleDTO {

    /**
     * 日期对应的考勤周期起始时间
     */
    private Date attendanceStartDate;

    /**
     * 日期对应的考勤周期截止时间
     */
    private Date attendanceEndDate;


    public static AttendanceDayCycleDTO buildWithDayId(Long nowDayId,
                                                       Integer cycleType,
                                                       String cycleStart,
                                                       String cycleEnd) {
        AttendanceDayCycleDTO attendanceDayCycleDTO = new AttendanceDayCycleDTO();
        AttendanceCycleTypeEnum attendanceCycleTypeEnum = AttendanceCycleTypeEnum.getByType(cycleType);
        if (null == attendanceCycleTypeEnum) {
            return null;
        }
        Date dayDate = DateUtil.parse(nowDayId.toString(), "yyyyMMdd");
        switch (attendanceCycleTypeEnum) {
            case WEEK:
                return buildWeekCycle(dayDate, cycleStart, cycleEnd, attendanceDayCycleDTO);
            case MONTH:
                return buildMonthCycle(dayDate, cycleStart, cycleEnd, attendanceDayCycleDTO);
            default:
                return null;
        }
    }


    private static AttendanceDayCycleDTO buildWeekCycle(Date dayDate,
                                                        String cycleStart,
                                                        String cycleEnd,
                                                        AttendanceDayCycleDTO attendanceDayCycleDTO) {
        // 获取周维度的开始和结束时间
        CycleTypeEnum cycleTypeEnum = CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.WEEK.name());
        if (cycleTypeEnum == null) {
            return null;
        }
        CycleTypeEnum.CycleRangDetail currentWeekRange = cycleTypeEnum.getCurrentRange(dayDate, cycleStart, cycleEnd);
        if (currentWeekRange == null) {
            return null;
        }
        attendanceDayCycleDTO.setAttendanceStartDate(currentWeekRange.getCycleStartDate());
        attendanceDayCycleDTO.setAttendanceEndDate(currentWeekRange.getCycleEndDate());
        return attendanceDayCycleDTO;
    }

    private static AttendanceDayCycleDTO buildMonthCycle(Date dayDate,
                                                         String cycleStart,
                                                         String cycleEnd,
                                                         AttendanceDayCycleDTO attendanceDayCycleDTO) {
        CycleTypeEnum.CycleRangDetail currentMonthRange = CycleTypeEnum.MONTH.getCurrentRange(
                dayDate,
                cycleStart,
                cycleEnd
        );
        if (null == currentMonthRange){
            return null;
        }
        attendanceDayCycleDTO.setAttendanceStartDate(currentMonthRange.getCycleStartDate());
        attendanceDayCycleDTO.setAttendanceEndDate(currentMonthRange.getCycleEndDate());
        return attendanceDayCycleDTO;
    }
}
