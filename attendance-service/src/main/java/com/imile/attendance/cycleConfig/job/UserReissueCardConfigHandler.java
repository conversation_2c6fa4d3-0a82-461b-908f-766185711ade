package com.imile.attendance.cycleConfig.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.UserCycleReissueCardCountManage;
import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.UserCycleReissueCardCountDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description 用户考勤周期补卡次数配置
 */
@Slf4j
@Component
public class UserReissueCardConfigHandler {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserCycleReissueCardCountManage userCycleReissueCardCountManage;
    @Resource
    private AttendanceCycleConfigService cycleConfigService;
    @Resource
    private DefaultIdWorker defaultIdWorker;


    @XxlJob(BusinessConstant.JobHandler.USER_REISSUE_CARD_CONFIG_HANDLER)
    public ReturnT<String> userReissueCardConfigHandler(String content) {
        var param = StringUtils.isNotBlank(content) ?
                JSON.parseObject(content, UserReissueCardConfigHandler.UserCardConfigHandlerParam.class) :
                new UserReissueCardConfigHandler.UserCardConfigHandlerParam();

        if (param.getInitDayId() == null) {
            param.setInitDayId(DateHelper.getDayId(new Date()));
        }
        List<AttendanceUser> userList;
        if (StringUtils.isNotBlank(param.getUserCodeList())) {
            List<String> userCodeList = Arrays.asList(param.getUserCodeList().split(","));
            userList = userService.listOnJobNonDriverUserByCodes(userCodeList);
        } else {
            if (StringUtils.isEmpty(param.getCountryList())) {
                XxlJobLogger.log("公司不能为空,content:{}", content);
                return ReturnT.SUCCESS;
            }

            List<String> countryList = Arrays.asList(param.getCountryList().split(","));
            userList = userService.listOnJobNonDriverUserByCountries(countryList);
        }

        if (CollectionUtils.isEmpty(userList)) {
            XxlJobLogger.log("用户列表为空,不处理");
            return ReturnT.SUCCESS;
        }

        List<Long> userIdList = userList.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());

        //查询员工所有的周期补卡记录
        Map<Long, List<UserCycleReissueCardCountDO>> existUserCardConfigMap = userCycleReissueCardCountManage.selectByUserIdList(userIdList)
                .stream()
                .sorted(Comparator.comparing(UserCycleReissueCardCountDO::getCycleStartDate))
                .collect(Collectors.groupingBy(UserCycleReissueCardCountDO::getUserId));

        List<UserCycleReissueCardCountDO> addUserCardConfigDOList = new ArrayList<>();
        for (AttendanceUser attendanceUser : userList) {
            Long userId = attendanceUser.getId();
            AttendanceCycleConfigDO userCycleConfigDO = cycleConfigService.getUserAttendanceCycleConfigUserCard(userId);
            AttendanceDayCycleDTO userDayCycleDTO = cycleConfigService.getUserAttendanceCycleConfigDay(
                    param.getInitDayId(), userCycleConfigDO);
            if (userDayCycleDTO == null) {
                log.info("用户：{} 未配置考勤周期,不处理", attendanceUser.getUserCode());
                continue;
            }
            //新入职的用户，之前可能没有
            List<UserCycleReissueCardCountDO> userCardList = existUserCardConfigMap.get(userId);
            if (CollectionUtils.isEmpty(userCardList)) {
                addCardConfigBuild(userId, userDayCycleDTO, addUserCardConfigDOList);
                continue;
            }
            //存在历史数据，看有没有相同周期的
            //看本周期是否以及存在了，存在了不做任务操作
            List<UserCycleReissueCardCountDO> existUserCardConfigList = userCardList.stream()
                    .filter(item -> DateHelper.formatPureDate(item.getCycleStartDate())
                            .equals(DateHelper.formatPureDate(userDayCycleDTO.getAttendanceStartDate())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existUserCardConfigList)) {
                continue;
            }
            //不存在本周期的，新增一条
            addCardConfigBuild(userId, userDayCycleDTO, addUserCardConfigDOList);
        }
        userCycleReissueCardCountManage.batchSave();
        return ReturnT.SUCCESS;
    }

    private void addCardConfigBuild(Long userId,
                                    AttendanceDayCycleDTO userDayCycleDTO,
                                    List<UserCycleReissueCardCountDO> addUserCardConfigDOList) {
        UserCycleReissueCardCountDO reissueCardCountDO = new UserCycleReissueCardCountDO();
        reissueCardCountDO.setId(defaultIdWorker.nextId());
        reissueCardCountDO.setUserId(userId);
        reissueCardCountDO.setUsedReissueCardCount(BusinessConstant.ZERO);
        reissueCardCountDO.setCycleStartDate(userDayCycleDTO.getAttendanceStartDate());
        reissueCardCountDO.setCycleEndDate(DateUtil.offset(userDayCycleDTO.getAttendanceEndDate(), DateField.MILLISECOND, -999));
        BaseDOUtil.fillDOUpdateByUserOrSystem(reissueCardCountDO);
        addUserCardConfigDOList.add(reissueCardCountDO);
    }


    @Data
    private static class UserCardConfigHandlerParam {

        /**
         * 初始化哪个月份的考勤周期  默认今天所属的周期
         */
        private Long initDayId;

        /**
         * 国家
         */
        private String countryList;

        /**
         * 定时任务每天执行：哪些用户
         */
        private String userCodeList;
    }
}
