package com.imile.attendance.rule.dto;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/5/6 
 * @Description 规则配置下拉选择DTO
 */
@Data
public class RuleConfigSelectDTO {

    /**
     * 未配置规则名称
     */
    public static final String NOT_CONFIG_RULE_NAME = "NOT_CONFIG";

    /**
     * 规则类型  RuleConfigTypeEnum
     */
    private String ruleType;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则编码
     */
    private String ruleNo;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 构建未配置规则配置下拉选择DTO
     * 
     * @param ruleType 规则类型
     * @return 未配置规则配置下拉选择DTO
     */
    public static RuleConfigSelectDTO buildNotConfigRuleConfigSelectDTO(String ruleType) {
        RuleConfigSelectDTO ruleConfigSelectDTO = new RuleConfigSelectDTO();
        ruleConfigSelectDTO.setRuleType(ruleType);
        ruleConfigSelectDTO.setRuleId(0L);
        ruleConfigSelectDTO.setRuleNo(null);
        ruleConfigSelectDTO.setRuleName(NOT_CONFIG_RULE_NAME);
        return ruleConfigSelectDTO;
    }

    /**
     * 构建规则配置下拉选择DTO
     * 
     * @param ruleType 规则类型
     * @param ruleId 规则ID
     * @param ruleNo 规则编码
     * @param ruleName 规则名称
     * @return 规则配置下拉选择DTO
     */
    public static RuleConfigSelectDTO buildRuleConfigSelectDTO(String ruleType, Long ruleId, String ruleNo, String ruleName) {
        RuleConfigSelectDTO ruleConfigSelectDTO = new RuleConfigSelectDTO();
        ruleConfigSelectDTO.setRuleType(ruleType);
        ruleConfigSelectDTO.setRuleId(ruleId);
        ruleConfigSelectDTO.setRuleNo(ruleNo);
        ruleConfigSelectDTO.setRuleName(ruleName);
        return ruleConfigSelectDTO;
    }
    
    /**
     * 判断是否为未配置规则
     * 
     * @param ruleName 规则名称
     * @return 是否为未配置规则
     */
    public static Boolean isNotConfigRule(String ruleName) {
        return NOT_CONFIG_RULE_NAME.equals(ruleName);
    }
}
