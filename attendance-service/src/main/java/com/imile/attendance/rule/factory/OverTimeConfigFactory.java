package com.imile.attendance.rule.factory;

import cn.hutool.core.text.StrPool;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.rule.OverTimeConfigManage;
import com.imile.attendance.rule.OverTimeConfigQueryService;
import com.imile.attendance.rule.RuleChangeLogService;
import com.imile.attendance.rule.bo.CountryOverTimeConfig;
import com.imile.attendance.rule.bo.OverTime;
import com.imile.attendance.rule.bo.OverTimeConfig;
import com.imile.attendance.rule.command.OverTimeConfigAddCommand;
import com.imile.attendance.rule.command.OverTimeConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.OverTimeConfigUpdateCommand;
import com.imile.attendance.rule.dto.AttendanceArchiveRuleConfigUpdateDTO;
import com.imile.attendance.rule.dto.ConfigUpdateType;
import com.imile.attendance.rule.dto.RuleConfigApplyUserCountDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigRangeChangeDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.handler.OverTimeConfigRangeHandler;
import com.imile.attendance.rule.mapstruct.OverTimeConfigMapstruct;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description
 */
@Slf4j
@Component
public class OverTimeConfigFactory {

    @Resource
    private OverTimeConfigDao overTimeConfigDao;
    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private CountryService countryService;
    @Resource
    private OverTimeConfigRangeHandler overTimeConfigRangeHandler;
    @Resource
    private OverTimeConfigQueryService overTimeConfigQueryService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendanceUserService userService;

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private RuleChangeLogService ruleChangeLogService;

    /**
     * 加班规则日志字段列表
     */
    public static final List<String> ruleLogFieldList = Arrays.asList(
            OverTimeConfigDO.Fields.configName,
            OverTimeConfigDO.Fields.status,
            OverTimeConfigDO.Fields.workingOutStartTime,
            OverTimeConfigDO.Fields.workingEffectiveTime,
            OverTimeConfigDO.Fields.workingSubsidyType,
            OverTimeConfigDO.Fields.restEffectiveTime,
            OverTimeConfigDO.Fields.restSubsidyType,
            OverTimeConfigDO.Fields.holidayEffectiveTime,
            OverTimeConfigDO.Fields.holidaySubsidyType);

    @Transactional
    public RuleConfigChangeCheckDTO add(OverTimeConfigAddCommand addCommand) {
        // 1.判断名称是否重复
        String configName = addCommand.getConfigName();
        OverTimeConfigDO overTimeConfigDO = overTimeConfigDao.getByName(configName);
        if (null != overTimeConfigDO) {
            throw BusinessLogicException.getException(ErrorCodeEnum.OVERTIME_CONFIG_NAME_EXIST);
        }

        // 获取该国家的加班配置
        CountryOverTimeConfig countryConfig = overTimeConfigManage.getCountryConfig(addCommand.getCountry());

        // 获取国家级别的配置
        OverTimeConfigDO countryLevelConfig = countryConfig.queryCountryLevelConfig();

        // 2.国家部门人员检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = checkRangeIsValid(
                addCommand,
                countryConfig.getCountryConfigs(),
                countryLevelConfig
        );
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return ruleConfigChangeCheckDTO;
        }

        // 3.构造OverTimeConfig
        CountryDTO countryDTO = countryService.queryCountry(addCommand.getCountry());
        Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

        OverTimeConfig overTimeConfig = OverTimeConfig.builder()
                .id(defaultIdWorker.nextId())
                .country(addCommand.getCountry())
                .configNo(defaultIdWorker.nextOverTimeConfigNo())
                .configName(addCommand.getConfigName())
                .overTime(buildOverTime(addCommand))
                .status(StatusEnum.ACTIVE.getCode())
                .deptIds(StringUtils.join(addCommand.getDeptIds(), StrPool.COMMA))
                .isLatest(BusinessConstant.Y)
                .effectTime(currentDate)
                .expireTime(BusinessConstant.DEFAULT_END_TIME)
                .workingOutStartTime(addCommand.getWorkingOutStartTime())
                .workingEffectiveTime(addCommand.getWorkingEffectiveTime())
                .workingSubsidyType(addCommand.getWorkingSubsidyType())
                .restEffectiveTime(addCommand.getRestEffectiveTime())
                .restSubsidyType(addCommand.getRestSubsidyType())
                .holidayEffectiveTime(addCommand.getHolidayEffectiveTime())
                .holidaySubsidyType(addCommand.getHolidaySubsidyType())
                .build();
        if (countryLevelConfig == null &&
                CollectionUtils.isEmpty(addCommand.getDeptIds()) &&
                CollectionUtils.isEmpty(addCommand.getUserIds())) {
            overTimeConfig.setIsCountryLevel(BusinessConstant.Y);
        }

        OverTimeConfigDO model = OverTimeConfigMapstruct.INSTANCE.toModel(overTimeConfig);
        BaseDOUtil.fillDOInsert(model);
        overTimeConfigDao.save(model);

        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = overTimeConfigRangeHandler.addConfigRangeHandler(
                currentDate,
                model,
                addCommand.getCountry(),
                addCommand.getDeptIds(),
                addCommand.getUserIds()
        );

        log.info("新增加班规则:{}，规则影响人员为:{}", addCommand.getConfigName(), ruleConfigRangeChangeDTO);

        logRecordService.recordOperation(model,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.OVERTIME_CONFIG_ADD.getCode())
                        .country(model.getCountry())
                        .bizName(model.getConfigName())
                        .fieldNameList(ruleLogFieldList)
                        .build());
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 检查更新加班规则影响的人员
     *
     * @param updateCommand 更新命令
     * @return 更新结果
     */
    public UpdateRuleReflectResult checkUpdateRule(OverTimeConfigUpdateCommand updateCommand) {
        // 获取当前配置
        OverTimeConfigDO currentConfig = overTimeConfigDao.getLatestByConfigNo(updateCommand.getConfigNo());
        if (null == currentConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }

        // 获取该国家的加班配置
        CountryOverTimeConfig countryConfig = overTimeConfigManage.getCountryConfig(updateCommand.getCountry());

        // 获取国家级别的配置，可以为空
        OverTimeConfigDO countryLevelConfig = countryConfig.queryCountryLevelConfig();

        // 范围检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = checkRangeIsValid(
                updateCommand,
                countryConfig.getCountryConfigs(),
                countryLevelConfig
        );
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return UpdateRuleReflectResult.rangeCheckFail(ruleConfigChangeCheckDTO);
        }

        // 确定加班配置的更新类型
        ConfigUpdateType updateType = determineUpdateType(updateCommand, currentConfig);
        // 配置和适用范围都未发生变动,直接返回
        if (updateType == ConfigUpdateType.NO_CHANGES) {
            return UpdateRuleReflectResult.noReflect();
        }

        // 当前更新的为国家级别配置，特殊处理（部门和人员可以不为空）
        if (countryLevelConfig != null && Objects.equals(currentConfig.getId(), countryLevelConfig.getId())) {
            // 部门和人员都为空, 则范围没有变动，说明只修改了配置，国家级规则版本升级
            if (CollectionUtils.isEmpty(updateCommand.getDeptIds()) &&
                    CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(false)
                        // 国家级规则版本升级，影响人数为当前配置的适用人数
                        .updateConfigReflectUserSize(overTimeConfigQueryService
                                .queryApplyUserCount(countryLevelConfig.getConfigNo()).getApplyUserSize())
                        .updateRangeReflectAddUserSize(0)
                        .updateRangeReflectRemoveUserSize(0)
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            }
            // 部门或人员变动，从国家级别配置升级

            // 查询部门和人员级别配置的变动影响的人
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = overTimeConfigRangeHandler.checkConfigRangeHandler(
                    currentConfig,
                    countryConfig,
                    updateCommand.getCountry(),
                    updateCommand.getDeptIds(),
                    updateCommand.getUserIds()
            );
            // 当前updateType只可能为RANGE_ONLY和BOTH
            if (updateType == ConfigUpdateType.RANGE_ONLY) {
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(false)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(0)
                        // 部门和人员级别当前配置的影响人员
                        .updateRangeReflectAddUserSize(ruleConfigRangeChangeDTO.getAddUserIdList().size())
                        // 查询国家级别配置的变动影响的人作为移除的人数
                        .updateRangeReflectRemoveUserSize(overTimeConfigQueryService.
                                queryApplyUserCount(updateCommand.getConfigNo()).getCountryLevelUserSize())
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            } else if (updateType == ConfigUpdateType.BOTH) {
                Integer countryLevelUserSize = overTimeConfigQueryService
                        .queryApplyUserCount(updateCommand.getConfigNo()).getCountryLevelUserSize();
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(countryLevelUserSize)
                        // 部门和人员级别当前配置的影响人员
                        .updateRangeReflectAddUserSize(ruleConfigRangeChangeDTO.getAddUserIdList().size())
                        // 查询国家级别配置的变动影响的人作为移除的人数
                        .updateRangeReflectRemoveUserSize(countryLevelUserSize)
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            }
        }

        switch (updateType) {
            case RANGE_ONLY:
                // 配置无变动，人员范围变动
                RuleConfigRangeChangeDTO onlyRuleRangeChangeDTO = overTimeConfigRangeHandler.checkConfigRangeHandler(
                        currentConfig,
                        countryConfig,
                        updateCommand.getCountry(),
                        updateCommand.getDeptIds(),
                        updateCommand.getUserIds());
                // 回退到国家级配置的处理
                if (countryLevelConfig == null) {
                    // 如果国家级别的配置不存在，添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置
                    if (CollectionUtils.isEmpty(updateCommand.getDeptIds())
                            && CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                        // 需要看国家添加的人员
                        List<UserInfoDO> countryUserList = overTimeConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                                RuleRangeUserQuery.builder()
                                        .country(updateCommand.getCountry()).build()
                        );
                        List<Long> countryLevelAddUserIds = countryUserList.stream()
                                .map(UserInfoDO::getId)
                                .collect(Collectors.toList());
                        // 当前规则移除人员进入到回退的国家级规则的用户
                        List<Long> intoCountryLevelOrNoRuleUserIdList = onlyRuleRangeChangeDTO.getIntoCountryLevelOrNoRuleUserIdList();
                        // 两者相加为真正的回退到国家级的人数
                        if (CollectionUtils.isNotEmpty(intoCountryLevelOrNoRuleUserIdList)) {
                            countryLevelAddUserIds.addAll(intoCountryLevelOrNoRuleUserIdList);
                        }
                        onlyRuleRangeChangeDTO.getAddUserIdList().addAll(countryLevelAddUserIds);
                    }
                }
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(false)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(0)
                        .updateRangeReflectAddUserSize(onlyRuleRangeChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(onlyRuleRangeChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            case CONFIG_ONLY:
                // 配置改动，人员范围无变动
                RuleConfigApplyUserCountDTO ruleConfigApplyUserCountDTO = overTimeConfigQueryService
                        .queryApplyUserCount(updateCommand.getConfigNo());
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(false)
                        .updateConfigReflectUserSize(ruleConfigApplyUserCountDTO.getApplyUserSize())
                        .updateRangeReflectAddUserSize(0)
                        .updateRangeReflectRemoveUserSize(0)
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            case BOTH:
                // 配置改动.人员范围都改动
                RuleConfigRangeChangeDTO ruleConfigAndRangeChangeDTO = overTimeConfigRangeHandler.checkConfigRangeHandler(
                        currentConfig,
                        countryConfig,
                        updateCommand.getCountry(),
                        updateCommand.getDeptIds(),
                        updateCommand.getUserIds());
                // 回退到国家级配置的处理
                if (countryLevelConfig == null) {
                    // 如果国家级别的配置不存在，添加的部门和用户id都为空,则说明本次从非国家级降级为国家级
                    if (CollectionUtils.isEmpty(updateCommand.getDeptIds())
                            && CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                        // 需要看国家添加的人员
                        List<UserInfoDO> countryUserList = overTimeConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                                RuleRangeUserQuery.builder()
                                        .country(updateCommand.getCountry()).build()
                        );
                        List<Long> countryLevelAddUserIds = countryUserList.stream()
                                .map(UserInfoDO::getId)
                                .collect(Collectors.toList());
                        // 当前规则移除人员进入到回退的国家级规则的用户
                        List<Long> intoCountryLevelOrNoRuleUserIdList = ruleConfigAndRangeChangeDTO.getIntoCountryLevelOrNoRuleUserIdList();
                        // 两者相加为回退到国家级的人数
                        if (CollectionUtils.isNotEmpty(intoCountryLevelOrNoRuleUserIdList)) {
                            countryLevelAddUserIds.addAll(intoCountryLevelOrNoRuleUserIdList);
                        }
                        ruleConfigAndRangeChangeDTO.getAddUserIdList().addAll(countryLevelAddUserIds);
                    }
                }
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        // 当前规则应用的用户总数
                        .updateConfigReflectUserSize(overTimeConfigQueryService
                                .queryApplyUserCount(updateCommand.getConfigNo()).getApplyUserSize())
                        .updateRangeReflectAddUserSize(ruleConfigAndRangeChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(ruleConfigAndRangeChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            default:
                return UpdateRuleReflectResult.noReflect();
        }
    }

    @Transactional
    public RuleConfigChangeCheckDTO update(OverTimeConfigUpdateCommand updateCommand) {
        // 获取当前配置
        OverTimeConfigDO currentConfig = overTimeConfigDao.getLatestByConfigNo(updateCommand.getConfigNo());
        if (null == currentConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        // 国家不能修改
        if (!StringUtils.equals(currentConfig.getCountry(), updateCommand.getCountry())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.OVERTIME_CONFIG_COUNTRY_NOT_CHANGE);
        }
        // 停用的规则不可编辑
        if (StringUtils.equals(currentConfig.getStatus(), StatusEnum.DISABLED.getCode())) {
            throw BusinessLogicException
                    .getException(ErrorCodeEnum.OVERTIME_CONFIG_COUNTRY_NOT_CHANGE_CONFIG_DISABLE_NOT_UPDATE);
        }
        // 判断修改后的名称是否和其他存在的配置重复
        OverTimeConfigDO configDaoByName = overTimeConfigDao.getByName(updateCommand.getConfigName());
        if (null != configDaoByName &&
                !StringUtils.equals(configDaoByName.getConfigNo(), updateCommand.getConfigNo())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.OVERTIME_CONFIG_NAME_EXIST);
        }

        // 获取该国家的加班配置
        CountryOverTimeConfig countryOverTimeConfig = overTimeConfigManage.getCountryConfig(updateCommand.getCountry());

        // 获取国家级别的配置，可以为空
        OverTimeConfigDO countryLevelConfig = countryOverTimeConfig.queryCountryLevelConfig();

        // 范围检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = checkRangeIsValid(
                updateCommand,
                countryOverTimeConfig.getCountryConfigs(),
                countryLevelConfig
        );
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return ruleConfigChangeCheckDTO;
        }

        // 确定加班配置的更新类型
        ConfigUpdateType configUpdateType = determineUpdateType(updateCommand, currentConfig);

        // 配置和适用范围都未发生变动,直接返回
        if (configUpdateType == ConfigUpdateType.NO_CHANGES) {
            log.info("更新加班规则:{}，配置和适用范围都未发生变动", updateCommand.getConfigName());
            return RuleConfigChangeCheckDTO.buildSuccess();
        }

        // 获取当前国家的时间
        CountryDTO countryDTO = countryService.queryCountry(updateCommand.getCountry());
        Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

        // 当前更新的为国家级别配置，特殊处理
        if (countryLevelConfig != null && Objects.equals(currentConfig.getId(), countryLevelConfig.getId())) {
            return handleCountryLevelConfigUpdate(updateCommand, currentConfig, currentDate, countryOverTimeConfig);
        }

        // 根据更新类型选择处理逻辑
        switch (configUpdateType) {
            case RANGE_ONLY:
                return handleRangeOnlyUpdate(updateCommand, currentConfig, currentDate);
            case CONFIG_ONLY:
                return handleConfigOnlyUpdate(updateCommand, currentConfig, currentDate);
            case BOTH:
                return handleBothUpdate(updateCommand, currentConfig, currentDate);
            default:
                return RuleConfigChangeCheckDTO.buildSuccess();
        }
    }
    
    /**
     * 检查状态切换的影响
     * @param statusSwitchCommand 状态切换命令
     * @return 更新结果
     */
    public UpdateRuleReflectResult checkStatusSwitch(OverTimeConfigStatusSwitchCommand statusSwitchCommand) {
        List<OverTimeConfigDO> overTimeConfigDOList = overTimeConfigDao
                .listByConfigNo(statusSwitchCommand.getConfigNo());
        if (CollectionUtils.isEmpty(overTimeConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchCommand.getStatus());
        if (null == statusEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "status=" + statusSwitchCommand.getStatus() + " not support");
        }
        OverTimeConfigDO currentConfig = overTimeConfigDOList.get(0);
        switch (statusEnum) {
            case ACTIVE:
                // 启用前范围检查
                RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = validateRangeAndGetResult(currentConfig);
                if (!ruleConfigChangeCheckDTO.getSuccess()) {
                    return UpdateRuleReflectResult.rangeCheckFail(ruleConfigChangeCheckDTO);
                }
                RuleConfigRangeChangeDTO enableConfigChangeDTO = overTimeConfigRangeHandler.checkEnableConfig(currentConfig);
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(enableConfigChangeDTO.getUpdateUserIdList().size())
                        .updateRangeReflectAddUserSize(enableConfigChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(enableConfigChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            case DISABLED:
                RuleConfigRangeChangeDTO disableConfigChangeDTO = overTimeConfigRangeHandler.checkDisableConfig(currentConfig);
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(disableConfigChangeDTO.getUpdateUserIdList().size())
                        .updateRangeReflectAddUserSize(disableConfigChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(disableConfigChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            default:
                return UpdateRuleReflectResult.noReflect();
        }
    }

    @Transactional
    public RuleConfigChangeCheckDTO statusSwitch(OverTimeConfigStatusSwitchCommand statusSwitchCommand) {
        // 获取当前配置
        List<OverTimeConfigDO> overTimeConfigDOList = overTimeConfigDao.listByConfigNo(statusSwitchCommand.getConfigNo());
        if (CollectionUtils.isEmpty(overTimeConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchCommand.getStatus());
        if (null == statusEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "status=" + statusSwitchCommand.getStatus() + " not support");
        }
        OverTimeConfigDO currentConfig = overTimeConfigDOList.get(0);
        switch (statusEnum) {
            case ACTIVE:
                // 启用前范围检查
                RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = validateRangeAndGetResult(currentConfig);
                if (!ruleConfigChangeCheckDTO.getSuccess()) {
                    return ruleConfigChangeCheckDTO;
                }
                activeConfig(currentConfig);
                break;
            case DISABLED:
                disableConfig(currentConfig);
                break;
            default:
                break;
        }
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 启用加班规则
     * @param currentConfig 当前配置
     */
    public void userOverTimeConfigRangeUpdate(AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO) {
        if (Objects.isNull(ruleConfigUpdateDTO.getOldConfigId())
                || Objects.isNull(ruleConfigUpdateDTO.getNewConfigId())
                || Objects.isNull(ruleConfigUpdateDTO.getUserId())
                || Objects.isNull(ruleConfigUpdateDTO.getStartDate())) {
            return;
        }

        List<OverTimeConfigRangeDO> overTimeConfigRangeDOList = overTimeConfigRangeDao.listConfigRanges(Collections.singletonList(ruleConfigUpdateDTO.getUserId()));
        //存在非国家级
        if (CollectionUtils.isNotEmpty(overTimeConfigRangeDOList)) {
            overTimeConfigRangeDOList.forEach(range -> {
                if (Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType())) {
                    range.setIsLatest(BusinessConstant.N);
                    range.setIsDelete(IsDeleteEnum.YES.getCode());
                } else {
                    range.setIsLatest(BusinessConstant.N);
                    range.setExpireTime(ruleConfigUpdateDTO.getStartDate());
                }
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
        }

        // 查找新打卡规则记录
        List<OverTimeConfigDO> overTimeConfigDOList = overTimeConfigDao.listLatestByConfigIds(Collections.singletonList(ruleConfigUpdateDTO.getNewConfigId()));
        if (CollectionUtils.isEmpty(overTimeConfigDOList)) {
            log.error("员工档案变更加班规则,新的加班规则配置不存在");
            return;
        }

        OverTimeConfigRangeDO rangeDO = new OverTimeConfigRangeDO();
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setBizId(ruleConfigUpdateDTO.getUserId());
        rangeDO.setRuleConfigId(overTimeConfigDOList.get(0).getId());
        rangeDO.setRuleConfigNo(overTimeConfigDOList.get(0).getConfigNo());
        rangeDO.setRangeType(RangeTypeEnum.USER.getCode());
        rangeDO.setEffectTime(ruleConfigUpdateDTO.getStartDate());
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        rangeDO.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(rangeDO);

        overTimeConfigManage.configRangeUpdateOrAdd(overTimeConfigRangeDOList, Collections.singletonList(rangeDO));
    }

    private void activeConfig(OverTimeConfigDO currentConfig) {
        String country = currentConfig.getCountry();

        CountryDTO countryDTO = countryService.queryCountry(country);
        Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

        boolean isCountryLevel = currentConfig.areCountryLevel();
        if (isCountryLevel) {
            log.info("启用国家级加班规则:{}", currentConfig.getConfigName());
            List<UserInfoDO> userInfoDOList = overTimeConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                    RuleRangeUserQuery.builder().country(country).build()
            );
            if (CollectionUtils.isEmpty(userInfoDOList)) {
                return;
            }
            List<Long> userIdList = userInfoDOList.stream()
                    .map(UserInfoDO::getId)
                    .collect(Collectors.toList());
            List<OverTimeConfigRangeDO> addConfigRangeList = new ArrayList<>();
            List<OverTimeConfigRangeDO> updateConfigRangeList = new ArrayList<>();
            //新增的国家级范围
            for (Long userId : userIdList) {
                OverTimeConfigRangeDO configRangeDO = new OverTimeConfigRangeDO();
                configRangeDO.setId(defaultIdWorker.nextId());
                configRangeDO.setRuleConfigId(currentConfig.getId());
                configRangeDO.setRuleConfigNo(currentConfig.getConfigNo());
                configRangeDO.setBizId(userId);
                configRangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
                configRangeDO.setEffectTime(currentDate);
                configRangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
                configRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
                configRangeDO.setIsLatest(BusinessConstant.Y);
                BaseDOUtil.fillDOInsert(configRangeDO);
                addConfigRangeList.add(configRangeDO);
            }
            //处理停用的国家级范围，修改为非最新
            List<OverTimeConfigRangeDO> oldCountryLevelRangeList = overTimeConfigRangeDao.listDisabledByConfigId(currentConfig.getId());
            if (CollectionUtils.isNotEmpty(oldCountryLevelRangeList)) {
                for (OverTimeConfigRangeDO item : oldCountryLevelRangeList) {
                    item.setIsLatest(BusinessConstant.N);
                    BaseDOUtil.fillDOUpdate(item);
                    updateConfigRangeList.add(item);
                }
            }
            overTimeConfigManage.configRangeUpdateOrAdd(updateConfigRangeList, addConfigRangeList);
            log.info("启用国家级加班规则:{}，新增加班规则的影响人数为:{}", currentConfig.getConfigName(), addConfigRangeList.size());
        } else {
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = overTimeConfigRangeHandler.enableConfigRangeHandler(
                    currentDate,
                    currentConfig,
                    currentConfig);
            log.info("启用加班规则:{},规则影响人员为：{}", currentConfig.getConfigName(), ruleConfigRangeChangeDTO);
        }

        currentConfig.setStatus(StatusEnum.ACTIVE.getCode());
        currentConfig.setEffectTime(currentDate);
        currentConfig.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        BaseDOUtil.fillDOUpdate(currentConfig);
        overTimeConfigManage.configUpdateAndAdd(currentConfig, null);

        logRecordService.recordOperation(
                currentConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ACTIVE)
                        .operationType(OperationTypeEnum.OVERTIME_CONFIG_ACTIVE.getCode())
                        .country(currentConfig.getCountry())
                        .bizName(currentConfig.getConfigName())
                        .fieldNameList(ruleLogFieldList)
                        .build());
    }

    /**
     * 停用加班规则
     * @param currentConfig 当前配置
     */
    private void disableConfig(OverTimeConfigDO currentConfig) {
        String country = currentConfig.getCountry();

        CountryDTO countryDTO = countryService.queryCountry(country);
        Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

        boolean isCountryLevel = currentConfig.areCountryLevel();
        if (isCountryLevel) {
            log.info("停用国家级加班规则:{}", currentConfig.getConfigName());
            List<OverTimeConfigRangeDO> countryLevelRangeList = overTimeConfigRangeDao.listByConfigId(currentConfig.getId());
            if (CollectionUtils.isEmpty(countryLevelRangeList)) {
                return;
            }
            for (OverTimeConfigRangeDO item : countryLevelRangeList) {
                item.setStatus(StatusEnum.DISABLED.getCode());
                item.setExpireTime(currentDate);
                item.setRemark("页面停用作导致旧规则停用");
                BaseDOUtil.fillDOUpdate(item);
            }
            overTimeConfigManage.configRangeUpdateOrAdd(countryLevelRangeList, null);
            log.info("停用国家级加班规则:{}，规则影响人员为：{}", currentConfig.getConfigName(), countryLevelRangeList.size());
        } else {
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = overTimeConfigRangeHandler.disableConfigRangeHandler(
                    currentDate,
                    currentConfig,
                    currentConfig,
                    overTimeConfigManage.getCountryConfig(country),
                    country);
            log.info("停用加班规则:{},规则影响人员为：{}", currentConfig.getConfigName(), ruleConfigRangeChangeDTO);
        }

        currentConfig.setStatus(StatusEnum.DISABLED.getCode());
        currentConfig.setExpireTime(currentDate);
        BaseDOUtil.fillDOUpdate(currentConfig);
        overTimeConfigManage.configUpdateAndAdd(currentConfig, null);

        logRecordService.recordOperation(
                currentConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.DISABLE)
                        .operationType(OperationTypeEnum.OVERTIME_CONFIG_DISABLED.getCode())
                        .country(currentConfig.getCountry())
                        .bizName(currentConfig.getConfigName())
                        .fieldNameList(ruleLogFieldList)
                        .build());
    }

    /**
     * 验证范围并获取结果
     * @param currentConfig 当前规则
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO validateRangeAndGetResult(OverTimeConfigDO currentConfig) {
        // 获取国家级别的配置，可以为空
        CountryOverTimeConfig countryOverTimeConfig = overTimeConfigManage.getCountryConfig(currentConfig.getCountry());
        OverTimeConfigDO countryLevelConfig = countryOverTimeConfig.queryCountryLevelConfig();

        // 范围检查
        return checkRangeIsValid(
                currentConfig,
                countryOverTimeConfig.getCountryConfigs(),
                countryLevelConfig
        );
    }

    /**
     * 检查规则范围是否重复(添加)
     */
    private RuleConfigChangeCheckDTO checkRangeIsValid(OverTimeConfigAddCommand addCommand,
                                                       List<OverTimeConfigDO> countryConfigs,
                                                       OverTimeConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();

        // 如果国家级别的配置存在,添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置，需要校验是否冲突
        if (countryLevelConfig != null &&
                CollectionUtils.isEmpty(addCommand.getDeptIds()) &&
                CollectionUtils.isEmpty(addCommand.getUserIds())) {
            rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                    addCommand.getCountry(), countryLevelConfig.getConfigName()));
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryConfigs, addCommand.getDeptIds(), null, rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        validateUserUsage(addCommand.getUserIds(), null, rangeDuplicateInfos);

        return CollectionUtils.isEmpty(rangeDuplicateInfos) ? RuleConfigChangeCheckDTO.buildSuccess()
                : RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    /**
     * 检查规则范围是否重复(修改)
     */
    private RuleConfigChangeCheckDTO checkRangeIsValid(OverTimeConfigUpdateCommand updateCommand,
                                                       List<OverTimeConfigDO> countryConfigs,
                                                       OverTimeConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();
        // 如果国家级别的配置存在,添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置，需要校验是否冲突
        if (countryLevelConfig != null) {
            if (CollectionUtils.isEmpty(updateCommand.getDeptIds()) &&
                    CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                // 如果编辑后满足国家级别，且前端传的规则编码和国家级编码不一致，则冲突（不是更新国家级规则）
                if (!StringUtils.equals(updateCommand.getConfigNo(), countryLevelConfig.getConfigNo())) {
                    rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                            updateCommand.getCountry(), countryLevelConfig.getConfigName()));
                }
            }
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryConfigs, updateCommand.getDeptIds(), updateCommand.getConfigNo(), rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        validateUserUsage(updateCommand.getUserIds(), updateCommand.getConfigNo(), rangeDuplicateInfos);

        return CollectionUtils.isEmpty(rangeDuplicateInfos) ? RuleConfigChangeCheckDTO.buildSuccess()
                : RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    /**
     * 检查规则范围是否重复(启用前)
     */
    private RuleConfigChangeCheckDTO checkRangeIsValid(OverTimeConfigDO currentConfig,
                                                       List<OverTimeConfigDO> countryConfigs,
                                                       OverTimeConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();
        // 如果国家级别的配置存在,则说明本次添加或更新的是国家级别的配置，需要校验是否冲突
        if (countryLevelConfig != null && currentConfig.areCountryLevel() &&
                !Objects.equals(currentConfig.getId(), countryLevelConfig.getId())) {
            rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                    currentConfig.getCountry(), countryLevelConfig.getConfigName()));
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryConfigs, currentConfig.listDeptIds(), currentConfig.getConfigNo(), rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        List<Long> currentConfigUserIds = overTimeConfigRangeDao.listDisabledByConfigId(currentConfig.getId())
                .stream()
                .map(OverTimeConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
        validateUserUsage(currentConfigUserIds, currentConfig.getConfigNo(), rangeDuplicateInfos);

        return CollectionUtils.isEmpty(rangeDuplicateInfos) ?
                RuleConfigChangeCheckDTO.buildSuccess() :
                RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    /**
     * 部门校验逻辑
     */
    private void validateDeptUsage(List<OverTimeConfigDO> configDOList, List<Long> deptIds, String configNo,
                                   List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos) {
        if (CollectionUtils.isEmpty(configDOList)) {
            return;
        }
        configDOList.forEach(configDO -> {
            if (!StringUtils.equals(configNo, configDO.getConfigNo())) {
                List<Long> deptIdList = configDO.listDeptIds();
                List<Long> duplicateDeptIds = deptIdList.stream()
                        .filter(deptIds::contains)
                        .collect(Collectors.toList());

                duplicateDeptIds.forEach(deptId -> {
                    AttendanceDept attendanceDept = deptService.getByDeptId(deptId);
                    if (attendanceDept != null) {
                        rangeDuplicateInfos.add(
                                RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                                        attendanceDept.getLocalizeName(), configDO.getConfigName()));
                    }
                });
            }
        });
    }

    /**
     * 用户校验逻辑
     */
    private void validateUserUsage(List<Long> userIds,
                                   String configNo,
                                   List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos) {
        List<OverTimeConfigRangeDO> existedConfigRecords = overTimeConfigRangeDao.listConfigRanges(userIds)
                .stream()
                .filter(OverTimeConfigRangeDO::areUserRange)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existedConfigRecords)) {
            return;
        }
        // 收集所有需要查询的用户ID
        Set<Long> allUserIds = existedConfigRecords.stream()
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        // 一次性批量查询所有用户信息
        Map<Long, AttendanceUser> userMap = userService.listUsersByIds(new ArrayList<>(allUserIds))
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getId, user -> user, (a, b) -> a));

        Map<Long, List<Long>> configIdToUserIdsMap = existedConfigRecords.stream()
                .collect(Collectors.groupingBy(
                        OverTimeConfigRangeDO::getRuleConfigId,
                        Collectors.mapping(OverTimeConfigRangeDO::getBizId, Collectors.toList())));

        List<Long> configIdList = new ArrayList<>(configIdToUserIdsMap.keySet());
        List<OverTimeConfigDO> configDOS = overTimeConfigDao.listLatestByConfigIds(configIdList);

        configDOS.stream()
                .filter(config -> !StringUtils.equals(config.getConfigNo(), configNo))
                .forEach(config -> {
                    List<Long> repeatUserIds = configIdToUserIdsMap.getOrDefault(config.getId(),
                            Collections.emptyList());
                    for (Long repeatUserId : repeatUserIds) {
                        AttendanceUser attendanceUser = userMap.get(repeatUserId);
                        if (null != attendanceUser) {
                            rangeDuplicateInfos.add(
                                    RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                                            attendanceUser.getLocaleName(), config.getConfigName()));
                        }
                    }
                });
    }

    /**
     * 构建加班配置
     */
    public OverTime buildOverTime(OverTimeConfigAddCommand addCommand) {
        return OverTime.builder()
                .workingOutStartTime(addCommand.getWorkingOutStartTime())
                .workingEffectiveTime(addCommand.getWorkingEffectiveTime())
                .workingSubsidyType(addCommand.getWorkingSubsidyType())
                .restEffectiveTime(addCommand.getRestEffectiveTime())
                .restSubsidyType(addCommand.getRestSubsidyType())
                .holidayEffectiveTime(addCommand.getHolidayEffectiveTime())
                .holidaySubsidyType(addCommand.getHolidaySubsidyType())
                .build();
    }

    /**
     * 确定加班配置的更新类型
     */
    private ConfigUpdateType determineUpdateType(OverTimeConfigUpdateCommand updateCommand,
                                                 OverTimeConfigDO currentConfig) {
        boolean configChanged = checkConfigRuleNeedUpdate(updateCommand, currentConfig);
        boolean rangeChanged = checkConfigRangeNeedUpdate(updateCommand, currentConfig);

        if (configChanged && rangeChanged) {
            return ConfigUpdateType.BOTH;
        }
        if (configChanged) {
            return ConfigUpdateType.CONFIG_ONLY;
        }
        if (rangeChanged) {
            return ConfigUpdateType.RANGE_ONLY;
        }
        return ConfigUpdateType.NO_CHANGES;
    }

    /**
     * 检查加班配置规则是否需要更新
     */
    public boolean checkConfigRuleNeedUpdate(OverTimeConfigUpdateCommand updateCommand, OverTimeConfigDO configDO) {
        if (!StringUtils.equals(updateCommand.getConfigName(), configDO.getConfigName())) {
            return true;
        }
        // 检查加班配置规则是否需要更新
        if (configDO.getWorkingOutStartTime().compareTo(updateCommand.getWorkingOutStartTime()) != 0) {
            return true;
        }
        if (configDO.getWorkingEffectiveTime().compareTo(updateCommand.getWorkingEffectiveTime()) != 0) {
            return true;
        }
        if (ObjectUtils.notEqual(configDO.getWorkingSubsidyType(), updateCommand.getWorkingSubsidyType())) {
            return true;
        }
        if (configDO.getRestEffectiveTime().compareTo(updateCommand.getRestEffectiveTime()) != 0) {
            return true;
        }
        if (ObjectUtils.notEqual(configDO.getRestSubsidyType(), updateCommand.getRestSubsidyType())) {
            return true;
        }
        if (configDO.getHolidayEffectiveTime().compareTo(updateCommand.getHolidayEffectiveTime()) != 0) {
            return true;
        }
        if (ObjectUtils.notEqual(configDO.getHolidaySubsidyType(), updateCommand.getHolidaySubsidyType())) {
            return true;
        }
        return false;
    }

    /**
     * 检查加班配置适用范围是否需要更新
     */
    public boolean checkConfigRangeNeedUpdate(OverTimeConfigUpdateCommand updateCommand, OverTimeConfigDO configDO) {
        // 获取更新后的部门ID和用户ID列表
        List<Long> updatedDeptIds = CollectionUtils.isEmpty(updateCommand.getDeptIds()) ? new ArrayList<>()
                : updateCommand.getDeptIds();
        List<Long> updatedUserIds = CollectionUtils.isEmpty(updateCommand.getUserIds()) ? new ArrayList<>()
                : updateCommand.getUserIds();

        // 获取已有的部门ID列表
        List<Long> existingDeptIds = configDO.listDeptIds();

        // 检查部门ID差异
        if (hasDifference(updatedDeptIds, existingDeptIds)) {
            return true;
        }

        // 获取已有的用户ID列表
        List<Long> existingUserIds = getExistingUserIds(configDO.getId());

        // 检查用户ID差异
        return hasDifference(updatedUserIds, existingUserIds);
    }

    private boolean hasDifference(List<Long> firstList, List<Long> secondList) {
        // 检查firstList中是否有secondList没有的元素
        if (firstList.stream().anyMatch(id -> !secondList.contains(id))) {
            return true;
        }
        // 检查secondList中是否有firstList没有的元素
        return secondList.stream().anyMatch(id -> !firstList.contains(id));
    }

    /**
     * 获取已有的用户ID列表
     */
    private List<Long> getExistingUserIds(Long configId) {
        List<OverTimeConfigRangeDO> rangeList = overTimeConfigRangeDao.listByConfigId(configId);
        return rangeList.stream()
                .filter(i -> StringUtils.equals(i.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(OverTimeConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 构建新的加班配置(非国家级)
     */
    private OverTimeConfigDO buildNewConfig(OverTimeConfigUpdateCommand updateCommand, Date currentDate) {
        OverTimeConfig overTimeConfig = OverTimeConfig.builder()
                .id(defaultIdWorker.nextId())
                .country(updateCommand.getCountry())
                .configNo(defaultIdWorker.nextOverTimeConfigNo())
                .configName(updateCommand.getConfigName())
                .overTime(buildOverTime(updateCommand))
                .status(StatusEnum.ACTIVE.getCode())
                .deptIds(StringUtils.join(updateCommand.getDeptIds(), StrPool.COMMA))
                .isLatest(BusinessConstant.Y)
                .effectTime(currentDate)
                .expireTime(BusinessConstant.DEFAULT_END_TIME)
                .workingOutStartTime(updateCommand.getWorkingOutStartTime())
                .workingEffectiveTime(updateCommand.getWorkingEffectiveTime())
                .workingSubsidyType(updateCommand.getWorkingSubsidyType())
                .restEffectiveTime(updateCommand.getRestEffectiveTime())
                .restSubsidyType(updateCommand.getRestSubsidyType())
                .holidayEffectiveTime(updateCommand.getHolidayEffectiveTime())
                .holidaySubsidyType(updateCommand.getHolidaySubsidyType())
                .isCountryLevel(BusinessConstant.N)
                .build();
        OverTimeConfigDO model = OverTimeConfigMapstruct.INSTANCE.toModel(overTimeConfig);
        BaseDOUtil.fillDOInsert(model);
        return model;
    }

    /**
     * 构建新的加班配置适用范围
     */
    private List<OverTimeConfigRangeDO> buildNewRangeForConfig(List<OverTimeConfigRangeDO> rangeDOList,
                                                               OverTimeConfigDO newConfig) {
        return rangeDOList.stream()
                .map(rangeDO -> {
                    OverTimeConfigRangeDO configRangeDO = new OverTimeConfigRangeDO();
                    configRangeDO.setId(defaultIdWorker.nextId());
                    configRangeDO.setRuleConfigId(newConfig.getId());
                    configRangeDO.setRuleConfigNo(newConfig.getConfigNo());
                    configRangeDO.setBizId(rangeDO.getBizId());
                    configRangeDO.setRangeType(rangeDO.getRangeType());
                    configRangeDO.setEffectTime(newConfig.getEffectTime());
                    configRangeDO.setExpireTime(newConfig.getExpireTime());
                    configRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
                    configRangeDO.setIsLatest(BusinessConstant.Y);
                    BaseDOUtil.fillDOInsert(configRangeDO);
                    return configRangeDO;
                }).collect(Collectors.toList());
    }

    /**
     * 标记旧的加班配置为过期
     */
    private void markOldConfigAsExpired(OverTimeConfigDO currentConfig, Date currentTime) {
        currentConfig.setIsLatest(BusinessConstant.N);
        currentConfig.setExpireTime(currentTime);
        BaseDOUtil.fillDOUpdate(currentConfig);
    }

    /**
     * 标记旧的加班配置适用范围为过期
     */
    private void markOldConfigRangeAsExpired(List<OverTimeConfigRangeDO> rangeDOList, Date currentDate) {
        for (OverTimeConfigRangeDO configRangeDO : rangeDOList) {
            configRangeDO.setIsLatest(BusinessConstant.N);
            configRangeDO.setExpireTime(currentDate);
            configRangeDO.setRemark("页面操作导致旧规则过期");
            BaseDOUtil.fillDOUpdate(configRangeDO);
        }
    }

    /**
     * 处理国家级别用户
     * @param country 国家
     * @param ruleConfigRangeChangeDTO 规则配置范围变更DTO
     * @return 国家级别添加的用户ID列表
     */
    private List<Long> processCountryLevelUsers(String country, RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        List<UserInfoDO> countryUserList = overTimeConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                RuleRangeUserQuery.builder().country(country).build()
        );
        List<Long> countryLevelAddUserIds = countryUserList.stream()
                .map(UserInfoDO::getId)
                .collect(Collectors.toList());

        //移除人员进入到回退的国家级规则的用户已经在上面的流程添加了，无需处理

//        // 当前规则移除人员进入到回退的国家级规则的用户
//        List<Long> intoCountryLevelOrNoRuleUserIdList = ruleConfigRangeChangeDTO.getIntoCountryLevelOrNoRuleUserIdList();
//        // 两者相加为真正的回退到国家级的人数
//        if (CollectionUtils.isNotEmpty(intoCountryLevelOrNoRuleUserIdList)) {
//            countryLevelAddUserIds.addAll(intoCountryLevelOrNoRuleUserIdList);
//        }
        return countryLevelAddUserIds;
    }

    /**
     * 处理回退到国家级别的情况
     *
     * @param updateCommand 更新命令
     * @param currentConfig 当前配置
     * @param currentDate   当前日期
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleBackToCountryLevel(OverTimeConfigUpdateCommand updateCommand,
                                                              OverTimeConfigDO currentConfig,
                                                              Date currentDate) {
        // 获取原国家级配置的适用范围
        List<OverTimeConfigRangeDO> currentRangeDOList = overTimeConfigRangeDao.listByConfigId(currentConfig.getId());
        // 标记当前配置的适用范围为过期
        markOldConfigRangeAsExpired(currentRangeDOList, currentDate);

        // 构建新的国家级加班配置数据
        OverTimeConfigDO newConfig = buildNewConfig(updateCommand, currentDate);
        newConfig.setIsCountryLevel(BusinessConstant.Y);

        //更新和新增主配置
        overTimeConfigManage.configUpdateAndAdd(currentConfig, newConfig);

        // 该国家的补卡配置已经修改了，重新获取
        CountryOverTimeConfig countryOverTimeConfig = overTimeConfigManage.getCountryConfig(updateCommand.getCountry());

        // 回退到国家级后，部门和人员的流动情况
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = overTimeConfigRangeHandler.updateConfigRangeHandler(
                currentDate,
                currentConfig,
                newConfig,
                countryOverTimeConfig,
                updateCommand.getCountry(),
                updateCommand.getDeptIds(),
                updateCommand.getUserIds()
        );

        // 处理国家级别用户
        List<Long> countryLevelAddUserIds = processCountryLevelUsers(updateCommand.getCountry(), ruleConfigRangeChangeDTO);

        // 创建国家级别范围记录
        List<OverTimeConfigRangeDO> newRangeDOList = countryLevelAddUserIds.stream()
                .map(addUserId -> {
                    OverTimeConfigRangeDO configRangeDO = new OverTimeConfigRangeDO();
                    configRangeDO.setId(defaultIdWorker.nextId());
                    configRangeDO.setRuleConfigId(newConfig.getId());
                    configRangeDO.setRuleConfigNo(newConfig.getConfigNo());
                    configRangeDO.setBizId(addUserId);
                    configRangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
                    configRangeDO.setEffectTime(newConfig.getEffectTime());
                    configRangeDO.setExpireTime(newConfig.getExpireTime());
                    configRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
                    configRangeDO.setIsLatest(BusinessConstant.Y);
                    configRangeDO.setRemark("非国家级规则回退到国家级规则添加的人员");
                    BaseDOUtil.fillDOInsert(configRangeDO);
                    return configRangeDO;
                }).collect(Collectors.toList());

        overTimeConfigManage.configRangeUpdateOrAdd(currentRangeDOList, newRangeDOList);

        log.info("更新加班规则:{},回退到国家级，规则影响人员为：{}", updateCommand.getConfigName(), ruleConfigRangeChangeDTO);

        // 记录操作日志
        logRecordService.recordOperation(
                newConfig,
                LogRecordOptions.buildWithRemark(OperationTypeEnum.OVERTIME_CONFIG_UPDATE.getCode(),
                        ruleChangeLogService.buildOverTimeConfigChangeLog(newConfig, currentConfig)));
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 处理国家级别配置更新
     * @param updateCommand 更新命令
     * @param currentConfig 当前配置
     * @param currentDate 当前日期
     * @param countryOverTimeConfig 国家加班配置
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleCountryLevelConfigUpdate(OverTimeConfigUpdateCommand updateCommand,
                                                                    OverTimeConfigDO currentConfig,
                                                                    Date currentDate,
                                                                    CountryOverTimeConfig countryOverTimeConfig) {
        // 部门和人员都为空，则范围没有更新，只修改了配置，只修改主配置
        if (CollectionUtils.isEmpty(updateCommand.getDeptIds()) &&
                CollectionUtils.isEmpty(updateCommand.getUserIds())) {
            // 标记当前国家级加班配置为过期
            markOldConfigAsExpired(currentConfig, currentDate);
            // 构建新的国家级加班配置数据
            OverTimeConfigDO newCountryLevelConfig = buildNewConfig(updateCommand, currentDate);
            newCountryLevelConfig.setIsCountryLevel(BusinessConstant.Y);

            // 获取原国家级配置的适用范围
            List<OverTimeConfigRangeDO> oldConfigRanges = overTimeConfigRangeDao.listByConfigId(currentConfig.getId());
            // 标记当前配置的适用范围为过期
            markOldConfigRangeAsExpired(oldConfigRanges, currentDate);
            // 构建新的配置适用范围
            List<OverTimeConfigRangeDO> addConfigRanges = buildNewRangeForConfig(oldConfigRanges, newCountryLevelConfig);

            overTimeConfigManage.configUpdateAndAdd(
                    currentConfig,
                    newCountryLevelConfig,
                    oldConfigRanges,
                    addConfigRanges
            );
            log.info("更新国家级加班规则:{}，配置发生改动,修改为：{}", currentConfig.getConfigName(), newCountryLevelConfig);
            logRecordService.recordOperation(
                    newCountryLevelConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.OVERTIME_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildOverTimeConfigChangeLog(newCountryLevelConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }

        // 部门或人员变动，从国家级别配置升级

        // 将当前的国家级别配置设置为过期
        markOldConfigAsExpired(currentConfig, currentDate);
        // 获取原国家级配置的适用范围
        List<OverTimeConfigRangeDO> oldConfigRanges = overTimeConfigRangeDao.listByConfigId(currentConfig.getId());
        // 标记原国家级配置的适用范围为过期
        markOldConfigRangeAsExpired(oldConfigRanges, currentDate);

        // 构建新的加班配置数据(非国家级)
        OverTimeConfigDO newConfig = buildNewConfig(updateCommand, currentDate);

        overTimeConfigManage.configUpdateAndAdd(currentConfig, newConfig, oldConfigRanges, null);

        // 该国家的补卡配置已经修改了，需要重新查询
        countryOverTimeConfig = overTimeConfigManage.getCountryConfig(updateCommand.getCountry());

        // 处理适用范围变更
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = overTimeConfigRangeHandler.updateConfigRangeHandler(
                currentDate,
                currentConfig,
                newConfig,
                countryOverTimeConfig,
                updateCommand.getCountry(),
                updateCommand.getDeptIds(),
                updateCommand.getUserIds()
        );
        log.info("更新加班规则:{}，规则影响人员为：{}", updateCommand.getConfigName(), ruleConfigRangeChangeDTO);
        logRecordService.recordOperation(
                newConfig,
                LogRecordOptions.buildWithRemark(OperationTypeEnum.OVERTIME_CONFIG_UPDATE.getCode(),
                        ruleChangeLogService.buildOverTimeConfigChangeLog(newConfig, currentConfig)));
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 处理仅范围变更
     *
     * @param updateCommand 更新命令
     * @param currentConfig 当前配置
     * @param currentDate   当前日期
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleRangeOnlyUpdate(OverTimeConfigUpdateCommand updateCommand,
                                                           OverTimeConfigDO currentConfig,
                                                           Date currentDate) {
        log.info("更新加班规则:{}，仅变动了适用范围", updateCommand.getConfigName());
        boolean isBackToCountryLevelFlag = updateCommand.isCountryLevelRangeFlag();
        markOldConfigAsExpired(currentConfig, currentDate);

        if (isBackToCountryLevelFlag) {
            // 回退到国家级别
            return handleBackToCountryLevel(updateCommand, currentConfig, currentDate);
        } else {
            // 规则的适用范围变更，配置也需要变更, 将当前配置设置为过期
            // 构建新的加班配置数据
            OverTimeConfigDO newConfig = buildNewConfig(updateCommand, currentDate);
            //更新和新增主配置
            overTimeConfigManage.configUpdateAndAdd(currentConfig, newConfig);

            // 该国家的加班配置已经修改了，需要重新查
            CountryOverTimeConfig countryOverTimeConfig = overTimeConfigManage.getCountryConfig(updateCommand.getCountry());

            // 处理适用范围变更
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = overTimeConfigRangeHandler.updateConfigRangeHandler(
                    currentDate,
                    currentConfig,
                    newConfig,
                    countryOverTimeConfig,
                    updateCommand.getCountry(),
                    updateCommand.getDeptIds(),
                    updateCommand.getUserIds()
            );
            log.info("更新加班规则:{},仅变动适用范围，规则影响人员为：{}", updateCommand.getConfigName(), ruleConfigRangeChangeDTO);
            logRecordService.recordOperation(
                    newConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.OVERTIME_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildOverTimeConfigChangeLog(newConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
    }

    /**
     * 处理仅配置变更
     * @param updateCommand 更新命令
     * @param currentConfig 当前配置
     * @param currentDate 当前日期
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleConfigOnlyUpdate(OverTimeConfigUpdateCommand updateCommand,
                                                            OverTimeConfigDO currentConfig,
                                                            Date currentDate) {
        log.info("更新加班规则:{}，仅变动配置，适用范围不变", updateCommand.getConfigName());
        // 标记当前配置为旧配置
        markOldConfigAsExpired(currentConfig, currentDate);
        // 获取原配置的适用范围
        List<OverTimeConfigRangeDO> currentRangeDOList = overTimeConfigRangeDao.listByConfigId(currentConfig.getId());
        // 标记当前配置的适用范围为过期
        markOldConfigRangeAsExpired(currentRangeDOList, currentDate);

        // 构建新的加班配置数据
        OverTimeConfigDO newConfig = buildNewConfig(updateCommand, currentDate);
        // 构建新的配置适用范围
        List<OverTimeConfigRangeDO> addConfigRanges = buildNewRangeForConfig(currentRangeDOList, newConfig);
        //更新和新增主配置
        overTimeConfigManage.configUpdateAndAdd(currentConfig, newConfig, currentRangeDOList, addConfigRanges);
        // 添加日志
        logRecordService.recordOperation(
                newConfig,
                LogRecordOptions.buildWithRemark(OperationTypeEnum.OVERTIME_CONFIG_UPDATE.getCode(),
                        ruleChangeLogService.buildOverTimeConfigChangeLog(newConfig, currentConfig)));
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 处理配置和范围都变更
     *
     * @param updateCommand 更新命令
     * @param currentConfig 当前配置
     * @param currentDate   当前日期
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleBothUpdate(OverTimeConfigUpdateCommand updateCommand,
                                                      OverTimeConfigDO currentConfig,
                                                      Date currentDate) {
        log.info("更新加班规则:{}，配置和适用范围都发生变动", updateCommand.getConfigName());
        boolean isBackToCountryLevelFlag = updateCommand.isCountryLevelRangeFlag();
        // 标记当前配置为旧配置
        markOldConfigAsExpired(currentConfig, currentDate);

        if (isBackToCountryLevelFlag) {
            // 回退到国家级别
            return handleBackToCountryLevel(updateCommand, currentConfig, currentDate);
        } else {
            // 规则的适用范围变更，配置也需要变更, 将当前配置设置为过期
            // 构建新的加班配置数据
            OverTimeConfigDO newConfig = buildNewConfig(updateCommand, currentDate);
            //更新和新增主配置
            overTimeConfigManage.configUpdateAndAdd(currentConfig, newConfig);

            // 该国家的加班配置已经修改了，需要重新查
            CountryOverTimeConfig countryOverTimeConfig = overTimeConfigManage.getCountryConfig(updateCommand.getCountry());

            // 处理适用范围变更
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = overTimeConfigRangeHandler.updateConfigRangeHandler(
                    currentDate,
                    currentConfig,
                    newConfig,
                    countryOverTimeConfig,
                    updateCommand.getCountry(),
                    updateCommand.getDeptIds(),
                    updateCommand.getUserIds()
            );
            log.info("更新加班规则:{},配置和适用范围都变更，规则影响人员为：{}", updateCommand.getConfigName(), ruleConfigRangeChangeDTO);
            logRecordService.recordOperation(
                    newConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.OVERTIME_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildOverTimeConfigChangeLog(newConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
    }

}
