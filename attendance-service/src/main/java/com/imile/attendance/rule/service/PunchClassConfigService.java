package com.imile.attendance.rule.service;

import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.rule.dto.PunchClassConfigAddDTO;
import com.imile.attendance.rule.vo.PunchClassConfigAddConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigAddVO;
import com.imile.attendance.rule.vo.PunchClassConfigDisabledCheckConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigUpdateConfirmVO;


/**
 * 班次服务Service
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
public interface PunchClassConfigService {

    boolean isMatch(ClassNatureEnum classNatureEnum);

    PunchClassConfigAddConfirmVO addPreProcessor(PunchClassConfigAddDTO dto);

    PunchClassConfigAddVO add(PunchClassConfigAddDTO dto);

    PunchClassConfigUpdateConfirmVO updatePreProcessor(PunchClassConfigAddDTO dto);

    PunchClassConfigAddVO update(PunchClassConfigAddDTO dto);

    PunchClassConfigDisabledCheckConfirmVO disabledCheck(Long classId);

    void enableCheck(PunchClassConfigDTO punchClassConfigDTO);

    Boolean enableStatus(Long classId);

    Boolean disabledStatus(Long classId);

    void classNatureSwitchHandler(UserInfoDO userInfoDO, String oldClassNature, String newClassNature);

    void userEntryAutoShift(UserInfoDO userInfo, AttendanceUserEntryRecord attendanceUserEntryRecord);

    void classSchedulingHandler(CalendarAndPunchHandlerDTO classHandlerDTO, String classNature);
}
