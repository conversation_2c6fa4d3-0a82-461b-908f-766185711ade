package com.imile.attendance.rule.command;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReissueCardConfigUpdateCommand extends ReissueCardConfigAddCommand {

    /**
     * 补卡规则编码
     */
    @NotBlank(message = "configNo can not be null")
    private String configNo;
}
