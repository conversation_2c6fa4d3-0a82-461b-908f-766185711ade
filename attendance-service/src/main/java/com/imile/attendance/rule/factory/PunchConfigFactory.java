package com.imile.attendance.rule.factory;

import cn.hutool.core.text.StrPool;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.PunchConfigQueryService;
import com.imile.attendance.rule.RuleChangeLogService;
import com.imile.attendance.rule.bo.CountryPunchConfig;
import com.imile.attendance.rule.bo.PunchConfig;
import com.imile.attendance.rule.command.PunchConfigAddCommand;
import com.imile.attendance.rule.command.PunchConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.PunchConfigUpdateCommand;
import com.imile.attendance.rule.dto.AttendanceArchiveRuleConfigUpdateDTO;
import com.imile.attendance.rule.dto.ConfigUpdateType;
import com.imile.attendance.rule.dto.RuleConfigApplyUserCountDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigRangeChangeDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.handler.PunchConfigRangeHandler;
import com.imile.attendance.rule.mapstruct.PunchConfigMapstruct;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Slf4j
@Component
public class PunchConfigFactory {

    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private CountryService countryService;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchConfigRangeHandler punchConfigRangeHandler;
    @Resource
    private PunchConfigQueryService punchConfigQueryService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendanceUserService userService;

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private RuleChangeLogService ruleChangeLogService;

    /**
     * 打卡规则日志字段列表
     */
    public static final List<String> punchRuleLogFieldList = Arrays.asList(
            PunchConfigDO.Fields.configName,
            PunchConfigDO.Fields.configType,
            PunchConfigDO.Fields.punchTimeInterval,
            PunchConfigDO.Fields.status);

    /**
     * 新增打卡规则配置
     *
     * 支持以下配置类型:
     * - 国家级配置: 适用于整个国家，不指定部门和用户
     * - 部门级配置: 适用于指定部门的所有用户
     * - 用户级配置: 适用于指定的具体用户
     *
     * 配置限制:
     * - 配置名称在系统中必须唯一
     * - 每个国家只能有一个国家级配置
     * - 同一部门不能重复配置
     * - 同一用户不能被多个配置同时覆盖
     *
     * @param addCommand 新增命令
     * @throws BusinessLogicException 当出现以下情况时抛出异常:
     *                                - 配置名称已存在
     *                                - 目标国家不存在国家级配置
     *                                - 配置范围与现有配置重复
     *                                - 用户已在其他配置
     */
    @Transactional
    public RuleConfigChangeCheckDTO add(PunchConfigAddCommand addCommand) {
        // 1.判断名称是否重复
        String punchConfigName = addCommand.getPunchConfigName();
        PunchConfigDO punchConfigDO = punchConfigDao.getByName(punchConfigName);
        if (null != punchConfigDO) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CONFIG_NAME_EXIST);
        }
        // 获取该国家的打卡配置
        CountryPunchConfig countryConfig = punchConfigManage.getCountryConfig(addCommand.getCountry());

        // 获取国家级别的配置
        PunchConfigDO countryLevelConfig = countryConfig.queryCountryLevelConfig();

        // 2.国家部门人员检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = checkRangeIsValid(
                addCommand,
                countryConfig.getCountryConfigs(),
                countryLevelConfig);
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return ruleConfigChangeCheckDTO;
        }

        // 3.构造punchConfig
        CountryDTO countryDTO = countryService.queryCountry(addCommand.getCountry());
        Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

        PunchConfig punchConfig = PunchConfig.builder()
                .id(defaultIdWorker.nextId())
                .country(addCommand.getCountry())
                .configNo(defaultIdWorker.nextPunchConfigNo())
                .configName(addCommand.getPunchConfigName())
                .configType(addCommand.getPunchConfigType())
                .punchTimeInterval(addCommand.getPunchTimeInterval())
                .status(StatusEnum.ACTIVE.getCode())
                .deptIds(StringUtils.join(addCommand.getDeptIds(), StrPool.COMMA))
                .isLatest(BusinessConstant.Y)
                .effectTime(currentDate)
                .expireTime(BusinessConstant.DEFAULT_END_TIME)
                .build();
        if (null == countryLevelConfig &&
                CollectionUtils.isEmpty(addCommand.getDeptIds()) &&
                CollectionUtils.isEmpty(addCommand.getUserIds())) {
            punchConfig.setIsCountryLevel(BusinessConstant.Y);
        }

        // 4.保存
        PunchConfigDO model = PunchConfigMapstruct.INSTANCE.toModel(punchConfig);
        BaseDOUtil.fillDOInsert(model);
        punchConfigDao.save(model);

        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = punchConfigRangeHandler.addConfigRangeHandler(currentDate,
                model, addCommand.getCountry(),
                addCommand.getDeptIds(), addCommand.getUserIds());

        log.info("新增打卡规则:{}，规则影响人员为:{}", addCommand.getPunchConfigName(), ruleConfigRangeChangeDTO);

        // 5.日志
        logRecordService.recordOperation(model,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.PUNCH_CONFIG_ADD.getCode())
                        .country(model.getCountry())
                        .bizName(model.getConfigName())
                        .fieldNameList(punchRuleLogFieldList)
                        .build());
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 检查打卡规则更新的影响范围
     * 分析规则变更类型并计算受影响的用户数量
     *
     * 支持四种更新场景：
     * 1. 仅配置变更：规则内容改变，适用范围不变
     * 2. 仅范围变更：规则内容不变，适用范围改变
     * 3. 配置和范围都变更：规则内容和适用范围都改变
     * 4. 无变更：规则内容和适用范围都未改变
     *
     * @param updateCommand 更新命令
     * @return 规则更新影响结果
     * @throws BusinessLogicException 当配置不存在时抛出异常
     */
    public UpdateRuleReflectResult checkUpdateRule(PunchConfigUpdateCommand updateCommand) {
        // 获取当前配置
        PunchConfigDO currentConfig = punchConfigDao.getLatestByConfigNo(updateCommand.getPunchConfigNo());
        if (null == currentConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }

        // 获取该国家的打卡配置
        CountryPunchConfig countryPunchConfig = punchConfigManage.getCountryConfig(updateCommand.getCountry());

        // 获取国家级别的配置，不能为空
        PunchConfigDO countryLevelConfig = countryPunchConfig.queryCountryLevelConfig();
        if (null == countryLevelConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CONFIG_COUNTRY_NOT_EXIST,
                    updateCommand.getCountry());
        }

        // 范围检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = checkRangeIsValid(
                updateCommand,
                countryPunchConfig.getCountryConfigs(),
                countryLevelConfig);
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return UpdateRuleReflectResult.rangeCheckFail(ruleConfigChangeCheckDTO);
        }

        // 确定打卡配置的更新类型
        ConfigUpdateType updateType = determineUpdateType(updateCommand, currentConfig);
        // 配置和适用范围都未发生变动,直接返回
        if (updateType == ConfigUpdateType.NO_CHANGES) {
            return UpdateRuleReflectResult.noReflect();
        }

        switch (updateType) {
            case RANGE_ONLY:
                // 配置无变动，人员范围变动
                RuleConfigRangeChangeDTO onlyRuleRangeChangeDTO = punchConfigRangeHandler.checkConfigRangeHandler(
                        currentConfig,
                        countryPunchConfig,
                        updateCommand.getCountry(),
                        updateCommand.getDeptIds(),
                        updateCommand.getUserIds());
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(false)
                        .isNeedUpdateRange(true)
                        // 仅范围改动，配置不动，该字段为0，不展示
                        .updateConfigReflectUserSize(0)
                        // 新增进规则的用户数
                        .updateRangeReflectAddUserSize(onlyRuleRangeChangeDTO.getAddUserIdList().size())
                        // 从规则移除的用户数
                        .updateRangeReflectRemoveUserSize(onlyRuleRangeChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            case CONFIG_ONLY:
                // 配置改动，人员范围无变动
                RuleConfigApplyUserCountDTO ruleConfigApplyUserCountDTO = punchConfigQueryService
                        .queryApplyUserCount(updateCommand.getPunchConfigNo());
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(false)
                        // 只有配置改动，当前规则应用的用户数
                        .updateConfigReflectUserSize(ruleConfigApplyUserCountDTO.getApplyUserSize())
                        .updateRangeReflectAddUserSize(0)
                        .updateRangeReflectRemoveUserSize(0)
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            case BOTH:
                // 配置改动.人员范围改动
                RuleConfigRangeChangeDTO ruleConfigAndRangeChangeDTO = punchConfigRangeHandler.checkConfigRangeHandler(
                        currentConfig,
                        countryPunchConfig,
                        updateCommand.getCountry(),
                        updateCommand.getDeptIds(),
                        updateCommand.getUserIds());

                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        // 当前规则应用的用户数
                        .updateConfigReflectUserSize(punchConfigQueryService
                                .queryApplyUserCount(updateCommand.getPunchConfigNo()).getApplyUserSize())
                        // 新增进规则的用户数
                        .updateRangeReflectAddUserSize(ruleConfigAndRangeChangeDTO.getAddUserIdList().size())
                        // 从规则移除的用户数
                        .updateRangeReflectRemoveUserSize(ruleConfigAndRangeChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            default:
                return UpdateRuleReflectResult.noReflect();
        }
    }

    /**
     * 确定打卡配置的更新类型
     *
     * @param updateCommand 更新命令
     * @param currentConfig 当前配置
     * @return 更新类型
     */
    private ConfigUpdateType determineUpdateType(PunchConfigUpdateCommand updateCommand, PunchConfigDO currentConfig) {
        boolean configChanged = checkConfigRuleNeedUpdate(updateCommand, currentConfig);
        boolean rangeChanged = checkConfigRangeNeedUpdate(updateCommand, currentConfig);

        if (configChanged && rangeChanged) {
            return ConfigUpdateType.BOTH;
        }
        if (configChanged) {
            return ConfigUpdateType.CONFIG_ONLY;
        }
        if (rangeChanged) {
            return ConfigUpdateType.RANGE_ONLY;
        }
        return ConfigUpdateType.NO_CHANGES;
    }

    /**
     * 更新打卡规则配置
     * 支持以下更新场景:
     * 1. 国家级别配置更新: 仅允许修改配置内容，不允许修改适用范围
     * 2. 普通配置更新:
     * - 仅配置内容更新: 创建新配置记录，原配置标记为过期
     * - 仅适用范围更新: 更新现有配置的适用范围
     * - 配置内容和适用范围同时更新: 创建新配置记录并更新适用范围
     *
     * 更新限制:
     * - 不允许修改配置所属国家
     * - 不允许修改已停用的配置
     * - 配置名称不能与其他配置重复
     *
     * @param updateCommand 更新命令
     * @throws BusinessLogicException 当出现以下情况时抛出异常:
     *                                - 配置不存在
     *                                - 尝试修改配置所属国家
     *                                - 尝试修改已停用的配置
     *                                - 配置名称与现有配置重复
     *                                - 国家级配置尝试修改适用范围
     *                                - 目标国家不存在国家级配置
     */
    @Transactional
    public RuleConfigChangeCheckDTO update(PunchConfigUpdateCommand updateCommand) {
        // 获取当前配置
        PunchConfigDO currentConfig = punchConfigDao.getLatestByConfigNo(updateCommand.getPunchConfigNo());
        if (null == currentConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }

        // 国家不能修改
        if (!StringUtils.equals(currentConfig.getCountry(), updateCommand.getCountry())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CONFIG_COUNTRY_NOT_CHANGE);
        }
        // 停用的规则不可编辑
        if (StringUtils.equals(currentConfig.getStatus(), StatusEnum.DISABLED.getCode())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CONFIG_DISABLE_NOT_UPDATE);
        }

        // 判断修改后的名称是否和其他存在的配置重复
        String punchConfigName = updateCommand.getPunchConfigName();
        PunchConfigDO configDaoByName = punchConfigDao.getByName(punchConfigName);
        if (null != configDaoByName &&
                !StringUtils.equals(configDaoByName.getConfigNo(), updateCommand.getPunchConfigNo())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CONFIG_NAME_EXIST);
        }
        // 获取该国家的打卡配置
        CountryPunchConfig countryPunchConfig = punchConfigManage.getCountryConfig(updateCommand.getCountry());

        // 获取国家级别的配置，不能为空
        PunchConfigDO countryLevelConfig = countryPunchConfig.queryCountryLevelConfig();
        if (null == countryLevelConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CONFIG_COUNTRY_NOT_EXIST,
                    updateCommand.getCountry());
        }

        // 当前更新的为国家级别配置，特殊处理, 只修改主配置
        if (Objects.equals(currentConfig.getId(), countryLevelConfig.getId())) {
            // 部门和人员必须为空
            if (CollectionUtils.isNotEmpty(updateCommand.getDeptIds()) ||
                    CollectionUtils.isNotEmpty(updateCommand.getUserIds())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.COUNTRY_LEVEL_RULE_CAN_NOT_CHANGE_RANGE);
            }

            CountryDTO countryDTO = countryService.queryCountry(updateCommand.getCountry());
            Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

            // 标记当前国家级配置为过期
            markOldConfigAsExpired(currentConfig, currentDate);
            // 构建新的国家级打卡配置数据
            PunchConfigDO newCountryLevelConfig = buildNewPunchConfig(updateCommand, currentDate);
            newCountryLevelConfig.setIsCountryLevel(BusinessConstant.Y);

            // 获取原国家级配置的适用范围
            List<PunchConfigRangeDO> oldConfigRanges = punchConfigRangeDao.listByConfigId(currentConfig.getId());
            // 标记当前配置的适用范围为过期
            markOldConfigRangeAsExpired(oldConfigRanges, currentDate);
            // 构建新的配置适用范围
            List<PunchConfigRangeDO> addConfigRanges = buildNewRangeForConfig(oldConfigRanges, newCountryLevelConfig);

            punchConfigManage.configUpdateAndAdd(
                    currentConfig,
                    newCountryLevelConfig,
                    oldConfigRanges,
                    addConfigRanges);
            log.info("更新国家级打卡规则:{}，配置发生改动,修改为：{}", currentConfig.getConfigName(), newCountryLevelConfig);
            logRecordService.recordOperation(
                    newCountryLevelConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.PUNCH_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildPunchConfigChangeLog(newCountryLevelConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }

        // 2.范围检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = checkRangeIsValid(
                updateCommand,
                countryPunchConfig.getCountryConfigs(),
                countryLevelConfig
        );
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return ruleConfigChangeCheckDTO;
        }

        // 确定打卡更新类型
        ConfigUpdateType configUpdateType = determineUpdateType(updateCommand, currentConfig);
        // 配置和适用范围都未发生变动,直接返回
        if (configUpdateType == ConfigUpdateType.NO_CHANGES) {
            log.info("更新打卡规则:{}，配置和适用范围都未发生变动", updateCommand.getPunchConfigName());
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
        // 获取国家时区时间
        CountryDTO countryDTO = countryService.queryCountry(updateCommand.getCountry());
        Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

        // 适用范围变动，配置不变
        if (configUpdateType == ConfigUpdateType.RANGE_ONLY) {
            log.info("更新打卡规则:{}，仅变动了适用范围", updateCommand.getPunchConfigName());

            // 规则的适用范围变更，配置也需要变更,将当前配置设置为过期
            markOldConfigAsExpired(currentConfig, currentDate);
            // 构建新的打卡配置数据
            PunchConfigDO newPunchConfig = buildNewPunchConfig(updateCommand, currentDate);
            // 更新和新增主配置
            punchConfigManage.configUpdateAndAdd(currentConfig, newPunchConfig);

            // 该国家的打卡配置已经修改了，需要更新
            countryPunchConfig = punchConfigManage.getCountryConfig(updateCommand.getCountry());

            // 处理适用范围变更
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = punchConfigRangeHandler.updateConfigRangeHandler(
                    currentDate,
                    currentConfig,
                    newPunchConfig,
                    countryPunchConfig,
                    updateCommand.getCountry(),
                    updateCommand.getDeptIds(),
                    updateCommand.getUserIds()
            );
            log.info("更新打卡规则:{},仅变动适用范围，规则影响人员为：{}", updateCommand.getPunchConfigName(), ruleConfigRangeChangeDTO);

            logRecordService.recordOperation(
                    newPunchConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.PUNCH_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildPunchConfigChangeLog(newPunchConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
        // 配置变动，适用范围不变
        if (configUpdateType == ConfigUpdateType.CONFIG_ONLY) {
            log.info("更新打卡规则:{}，仅变动配置，适用范围不变", updateCommand.getPunchConfigName());
            // 标记当前配置为旧配置
            markOldConfigAsExpired(currentConfig, currentDate);
            // 获取原配置的适用范围
            List<PunchConfigRangeDO> currentRangeDOList = punchConfigRangeDao.listByConfigId(currentConfig.getId());
            // 标记当前配置的适用范围为过期
            markOldConfigRangeAsExpired(currentRangeDOList, currentDate);

            // 构建新的打卡配置数据
            PunchConfigDO newConfig = buildNewPunchConfig(updateCommand, currentDate);
            // 构建新的配置适用范围
            List<PunchConfigRangeDO> addConfigRanges = buildNewRangeForConfig(currentRangeDOList, newConfig);
            //更新和新增主配置
            punchConfigManage.configUpdateAndAdd(currentConfig, newConfig, currentRangeDOList, addConfigRanges);

            // 添加日志
            logRecordService.recordOperation(
                    newConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.PUNCH_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildPunchConfigChangeLog(newConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
        // 配置和适用范围都发生变动
        if (configUpdateType == ConfigUpdateType.BOTH) {
            log.info("更新打卡规则:{}，配置和适用范围都发生变动", updateCommand.getPunchConfigName());
            // 标记当前配置为旧配置
            markOldConfigAsExpired(currentConfig, currentDate);
            // 构建新的打卡配置数据
            PunchConfigDO newConfig = buildNewPunchConfig(updateCommand, currentDate);
            // 更新和新增主配置
            punchConfigManage.configUpdateAndAdd(currentConfig, newConfig);

            // 该国家的打卡配置已经修改了，需要更新
            countryPunchConfig = punchConfigManage.getCountryConfig(updateCommand.getCountry());

            // 处理适用范围变更
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = punchConfigRangeHandler.updateConfigRangeHandler(
                    currentDate,
                    currentConfig,
                    newConfig,
                    countryPunchConfig,
                    updateCommand.getCountry(),
                    updateCommand.getDeptIds(),
                    updateCommand.getUserIds());
            log.info("更新打卡规则:{},配置和适用范围都发生变动，规则影响人员为：{}", updateCommand.getPunchConfigName(), ruleConfigRangeChangeDTO);

            // 添加日志
            logRecordService.recordOperation(
                    newConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.PUNCH_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildPunchConfigChangeLog(newConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 检查打卡规则状态切换的影响范围
     * 分析状态变更（启用/停用）对用户范围的影响
     *
     * 支持两种状态切换场景：
     * 1. 启用(ACTIVE)：
     * - 检查规则启用后影响的用户范围
     * - 计算需要更新配置的用户数量
     * - 计算新增和移除的用户数量
     * 2. 停用(DISABLED)：
     * - 检查规则停用后影响的用户范围
     * - 计算需要更新配置的用户数量
     * - 计算需要移除的用户数量
     *
     * @param statusSwitchCommand 状态切换命令
     * @return 状态切换影响结果
     * @throws BusinessLogicException 当状态值不合法时抛出异常
     */
    public UpdateRuleReflectResult checkStatusSwitch(PunchConfigStatusSwitchCommand statusSwitchCommand) {
        List<PunchConfigDO> punchConfigDOList = punchConfigDao.listByConfigNo(statusSwitchCommand.getPunchConfigNo());
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchCommand.getStatus());
        if (null == statusEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "status=" + statusSwitchCommand.getStatus() + " not support");
        }
        PunchConfigDO currentConfig = punchConfigDOList.get(0);
        switch (statusEnum) {
            case ACTIVE:
                // 启用前范围检查
                RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = validateRangeAndGetResult(currentConfig);
                if (!ruleConfigChangeCheckDTO.getSuccess()) {
                    return UpdateRuleReflectResult.rangeCheckFail(ruleConfigChangeCheckDTO);
                }
                RuleConfigRangeChangeDTO enableConfigChangeDTO = punchConfigRangeHandler.checkEnableConfig(currentConfig);
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(enableConfigChangeDTO.getUpdateUserIdList().size())
                        .updateRangeReflectAddUserSize(enableConfigChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(enableConfigChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            case DISABLED:
                RuleConfigRangeChangeDTO disableConfigChangeDTO = punchConfigRangeHandler.checkDisableConfig(currentConfig);
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(disableConfigChangeDTO.getUpdateUserIdList().size())
                        .updateRangeReflectAddUserSize(disableConfigChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(disableConfigChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            default:
                return UpdateRuleReflectResult.noReflect();
        }
    }

    /**
     * 打卡规则的状态切换
     *
     * 处理流程：
     * 1. 获取并验证当前配置
     * 2. 根据目标状态执行相应操作：
     * - ACTIVE: 执行规则启用流程
     * * 获取国家时区时间
     * * 更新规则范围
     * * 更新规则状态为启用
     * - DISABLED: 执行规则停用流程
     * * 验证是否为国家级规则（国家级规则不可停用）
     * * 更新规则范围
     * * 更新规则状态为停用
     *
     * @param statusSwitchCommand 状态切换命令
     * @throws BusinessLogicException 当出现以下情况时抛出异常：
     *                                - 配置不存在
     *                                - 状态值不合法
     *                                - 尝试停用国家级规则
     */
    @Transactional
    public RuleConfigChangeCheckDTO statusSwitch(PunchConfigStatusSwitchCommand statusSwitchCommand) {
        // 获取当前配置
        List<PunchConfigDO> punchConfigDOList = punchConfigDao.listByConfigNo(statusSwitchCommand.getPunchConfigNo());
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchCommand.getStatus());
        if (null == statusEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "status=" + statusSwitchCommand.getStatus() + " not support");
        }
        PunchConfigDO currentConfig = punchConfigDOList.get(0);
        switch (statusEnum) {
            case ACTIVE:
                // 启用前范围检查
                RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = validateRangeAndGetResult(currentConfig);
                if (!ruleConfigChangeCheckDTO.getSuccess()) {
                    return ruleConfigChangeCheckDTO;
                }
                activeConfig(currentConfig);
                break;
            case DISABLED:
                disableConfig(currentConfig);
                break;
            default:
                break;
        }
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 停用打卡规则
     */
    public void userPunchConfigRangeUpdate(AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO) {
        if (Objects.isNull(ruleConfigUpdateDTO.getOldConfigId())
                || Objects.isNull(ruleConfigUpdateDTO.getNewConfigId())
                || Objects.isNull(ruleConfigUpdateDTO.getUserId())
                || Objects.isNull(ruleConfigUpdateDTO.getStartDate())) {
            return;
        }

        List<PunchConfigRangeDO> punchConfigRangeDOList = punchConfigRangeDao.listConfigRanges(Collections.singletonList(ruleConfigUpdateDTO.getUserId()));
        //存在非国家级
        if (CollectionUtils.isNotEmpty(punchConfigRangeDOList)) {
            punchConfigRangeDOList.forEach(range -> {
                if (Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType())) {
                    range.setIsLatest(BusinessConstant.N);
                    range.setIsDelete(IsDeleteEnum.YES.getCode());
                } else {
                    range.setIsLatest(BusinessConstant.N);
                    range.setExpireTime(ruleConfigUpdateDTO.getStartDate());
                }
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
        }

        // 查找新打卡规则记录
        List<PunchConfigDO> punchConfigDOList = punchConfigDao.listLatestByConfigIds(Collections.singletonList(ruleConfigUpdateDTO.getNewConfigId()));
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            log.error("员工档案变更打卡规则,新的打卡规则配置不存在");
            return;
        }

        PunchConfigRangeDO rangeDO = new PunchConfigRangeDO();
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setBizId(ruleConfigUpdateDTO.getUserId());
        rangeDO.setRuleConfigId(punchConfigDOList.get(0).getId());
        rangeDO.setRuleConfigNo(punchConfigDOList.get(0).getConfigNo());
        rangeDO.setRangeType(RangeTypeEnum.USER.getCode());
        rangeDO.setEffectTime(ruleConfigUpdateDTO.getStartDate());
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        rangeDO.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(rangeDO);

        punchConfigManage.configRangeUpdateOrAdd(punchConfigRangeDOList, Collections.singletonList(rangeDO));
    }

    private void disableConfig(PunchConfigDO currentConfig) {
        String country = currentConfig.getCountry();

        // 打卡的国家级规则不可停用
        boolean isCountryLevel = currentConfig.areCountryLevel();
        if (isCountryLevel) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CONFIG_COUNTRY_LEVEL_CAN_NOT_DISABLED);
        }

        CountryDTO countryDTO = countryService.queryCountry(country);
        Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = punchConfigRangeHandler.disableConfigRangeHandler(
                currentDate,
                currentConfig,
                currentConfig,
                punchConfigManage.getCountryConfig(country),
                country);
        log.info("停用打卡规则:{},规则影响人员为：{}", currentConfig.getConfigName(), ruleConfigRangeChangeDTO);

        currentConfig.setStatus(StatusEnum.DISABLED.getCode());
        currentConfig.setExpireTime(currentDate);
        BaseDOUtil.fillDOUpdate(currentConfig);
        punchConfigManage.configUpdateAndAdd(currentConfig, null);

        logRecordService.recordOperation(
                currentConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.DISABLE)
                        .operationType(OperationTypeEnum.PUNCH_CONFIG_DISABLED.getCode())
                        .country(currentConfig.getCountry())
                        .bizName(currentConfig.getConfigName())
                        .fieldNameList(punchRuleLogFieldList)
                        .build());
    }

    /**
     * 启用打卡规则
     */
    private void activeConfig(PunchConfigDO currentConfig) {
        String country = currentConfig.getCountry();

        CountryDTO countryDTO = countryService.queryCountry(country);
        Date currentDate = countryDTO.getCountryTimeZoneDate(new Date());

        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = punchConfigRangeHandler.enableConfigRangeHandler(
                currentDate,
                currentConfig,
                currentConfig);
        log.info("启用打卡规则:{},规则影响人员为：{}", currentConfig.getConfigName(), ruleConfigRangeChangeDTO);

        currentConfig.setStatus(StatusEnum.ACTIVE.getCode());
        currentConfig.setEffectTime(currentDate);
        currentConfig.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        BaseDOUtil.fillDOUpdate(currentConfig);
        punchConfigManage.configUpdateAndAdd(currentConfig, null);

        logRecordService.recordOperation(
                currentConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ACTIVE)
                        .operationType(OperationTypeEnum.PUNCH_CONFIG_ACTIVE.getCode())
                        .country(currentConfig.getCountry())
                        .bizName(currentConfig.getConfigName())
                        .fieldNameList(punchRuleLogFieldList)
                        .build());
    }

    /**
     * 构建新的打卡配置对象（非国家级）
     * 基于更新命令创建一个新的打卡配置数据对象，用于替换旧配置
     *
     * @param updateCommand 更新命令
     * @param currentDate   当前日期，用作新配置的生效时间
     * @return 新创建的打卡配置数据对象
     */
    private PunchConfigDO buildNewPunchConfig(PunchConfigUpdateCommand updateCommand, Date currentDate) {
        PunchConfig punchConfig = PunchConfig.builder()
                .id(defaultIdWorker.nextId())
                .country(updateCommand.getCountry())
                .configNo(defaultIdWorker.nextPunchConfigNo())
                .configName(updateCommand.getPunchConfigName())
                .configType(updateCommand.getPunchConfigType())
                .punchTimeInterval(updateCommand.getPunchTimeInterval())
                .status(StatusEnum.ACTIVE.getCode())
                .deptIds(StringUtils.join(updateCommand.getDeptIds(), StrPool.COMMA))
                .isLatest(BusinessConstant.Y)
                .effectTime(currentDate)
                .expireTime(BusinessConstant.DEFAULT_END_TIME)
                .isCountryLevel(BusinessConstant.N)
                .build();
        PunchConfigDO model = PunchConfigMapstruct.INSTANCE.toModel(punchConfig);
        BaseDOUtil.fillDOInsert(model);
        return model;
    }

    /**
     * 为新配置构建适用范围对象列表
     *
     * 基于原有配置的适用范围列表，创建一组新的适用范围对象，并关联到新的配置
     * 保留原有的业务ID和范围类型，更新配置ID和配置编号
     *
     * @param rangeDOList 原有配置的适用范围对象列表
     * @param newConfig   新创建的打卡配置对象
     * @return 新创建的适用范围对象列表
     */
    private List<PunchConfigRangeDO> buildNewRangeForConfig(List<PunchConfigRangeDO> rangeDOList,
                                                            PunchConfigDO newConfig) {
        return rangeDOList.stream()
                .map(rangeDO -> {
                    PunchConfigRangeDO punchConfigRangeDO = new PunchConfigRangeDO();
                    punchConfigRangeDO.setId(defaultIdWorker.nextId());
                    punchConfigRangeDO.setRuleConfigId(newConfig.getId());
                    punchConfigRangeDO.setRuleConfigNo(newConfig.getConfigNo());
                    punchConfigRangeDO.setBizId(rangeDO.getBizId());
                    punchConfigRangeDO.setRangeType(rangeDO.getRangeType());
                    punchConfigRangeDO.setEffectTime(newConfig.getEffectTime());
                    punchConfigRangeDO.setExpireTime(newConfig.getExpireTime());
                    punchConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
                    punchConfigRangeDO.setIsLatest(BusinessConstant.Y);
                    BaseDOUtil.fillDOInsert(punchConfigRangeDO);
                    return punchConfigRangeDO;
                }).collect(Collectors.toList());
    }

    /**
     * 检查配置规则是否需要更新
     */
    public boolean checkConfigRuleNeedUpdate(PunchConfigUpdateCommand updateCommand, PunchConfigDO punchConfigDO) {
        if (!StringUtils.equals(updateCommand.getPunchConfigName(), punchConfigDO.getConfigName())) {
            return true;
        }
        if (!StringUtils.equals(updateCommand.getPunchConfigType(), punchConfigDO.getConfigType())) {
            return true;
        }
        if (!Objects.equals(updateCommand.getPunchTimeInterval(), punchConfigDO.getPunchTimeInterval())) {
            return true;
        }
        return false;
    }

    /**
     * 检查配置适用范围是否需要更新
     */
    public boolean checkConfigRangeNeedUpdate(PunchConfigUpdateCommand updateCommand, PunchConfigDO punchConfigDO) {
        // 获取更新后的部门ID和用户ID列表
        List<Long> updatedDeptIds = CollectionUtils.isEmpty(updateCommand.getDeptIds()) ? new ArrayList<>()
                : updateCommand.getDeptIds();
        List<Long> updatedUserIds = CollectionUtils.isEmpty(updateCommand.getUserIds()) ? new ArrayList<>()
                : updateCommand.getUserIds();

        // 获取已有的部门ID列表
        List<Long> existingDeptIds = punchConfigDO.listDeptIds();

        // 检查部门ID差异
        if (hasDifference(updatedDeptIds, existingDeptIds)) {
            return true;
        }

        // 获取已有的用户ID列表
        List<Long> existingUserIds = getExistingUserIds(punchConfigDO.getId());

        // 检查用户ID差异
        return hasDifference(updatedUserIds, existingUserIds);
    }

    /**
     * 检查两个列表是否存在差异
     */
    private boolean hasDifference(List<Long> firstList, List<Long> secondList) {
        // 检查firstList中是否有secondList没有的元素
        if (firstList.stream().anyMatch(id -> !secondList.contains(id))) {
            return true;
        }
        // 检查secondList中是否有firstList没有的元素
        return secondList.stream().anyMatch(id -> !firstList.contains(id));
    }

    /**
     * 获取已有的用户ID列表
     */
    private List<Long> getExistingUserIds(Long punchConfigId) {
        List<PunchConfigRangeDO> rangeList = punchConfigRangeDao.listByConfigId(punchConfigId);
        return rangeList.stream()
                .filter(i -> StringUtils.equals(i.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(PunchConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 规则范围检查(添加)
     */
    private RuleConfigChangeCheckDTO checkRangeIsValid(PunchConfigAddCommand addCommand,
                                                       List<PunchConfigDO> countryPunchConfigs,
                                                       PunchConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        if (CollectionUtils.isEmpty(countryPunchConfigs)) {
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
        if (countryLevelConfig == null) {
            // 国家级别的配置不存在
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CONFIG_COUNTRY_NOT_EXIST,
                    addCommand.getCountry());
        }
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();

        // 如果国家级别的配置存在,添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置，需要校验是否冲突
        if (CollectionUtils.isEmpty(addCommand.getDeptIds()) &&
                CollectionUtils.isEmpty(addCommand.getUserIds())) {
            rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                    addCommand.getCountry(), countryLevelConfig.getConfigName()));
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryPunchConfigs, addCommand.getDeptIds(), null, rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        validateUserUsage(addCommand.getUserIds(), null, rangeDuplicateInfos);

        return CollectionUtils.isEmpty(rangeDuplicateInfos) ? RuleConfigChangeCheckDTO.buildSuccess()
                : RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    /**
     * 规则范围检查(更新)
     */
    private RuleConfigChangeCheckDTO checkRangeIsValid(PunchConfigUpdateCommand updateCommand,
                                                       List<PunchConfigDO> countryPunchConfigs,
                                                       PunchConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        if (CollectionUtils.isEmpty(countryPunchConfigs)) {
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();
        // 如果国家级别的配置存在,添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置，需要校验是否冲突
        if (countryLevelConfig != null) {
            if (CollectionUtils.isEmpty(updateCommand.getDeptIds()) &&
                    CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                // 如果编辑后满足国家级别，且前端传的规则编码和国家级编码不一致，则冲突（不是更新国家级规则）
                if (!StringUtils.equals(updateCommand.getPunchConfigNo(), countryLevelConfig.getConfigNo())) {
                    rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                            updateCommand.getCountry(), countryLevelConfig.getConfigName()));
                }
            }
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryPunchConfigs, updateCommand.getDeptIds(), updateCommand.getPunchConfigNo(),
                rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        validateUserUsage(updateCommand.getUserIds(), updateCommand.getPunchConfigNo(), rangeDuplicateInfos);
        return CollectionUtils.isEmpty(rangeDuplicateInfos) ? RuleConfigChangeCheckDTO.buildSuccess()
                : RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    /**
     * 规则范围检查(启用前)
     */
    private RuleConfigChangeCheckDTO checkRangeIsValid(PunchConfigDO currentConfig,
                                                       List<PunchConfigDO> countryConfigs,
                                                       PunchConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();
        // 如果国家级别的配置存在,则说明本次添加或更新的是国家级别的配置，需要校验是否冲突
        if (countryLevelConfig != null && currentConfig.areCountryLevel() &&
                !Objects.equals(currentConfig.getId(), countryLevelConfig.getId())) {
            rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                    currentConfig.getCountry(), countryLevelConfig.getConfigName()));
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryConfigs, currentConfig.listDeptIds(), currentConfig.getConfigNo(), rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        List<Long> currentConfigUserIds = punchConfigRangeDao.listDisabledByConfigId(currentConfig.getId())
                .stream()
                .map(PunchConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
        validateUserUsage(currentConfigUserIds, currentConfig.getConfigNo(), rangeDuplicateInfos);

        return CollectionUtils.isEmpty(rangeDuplicateInfos) ?
                RuleConfigChangeCheckDTO.buildSuccess() :
                RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    /**
     * 验证范围并获取结果
     * @param currentConfig 更新命令
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO validateRangeAndGetResult(PunchConfigDO currentConfig) {
        // 获取国家级别的配置，可以为空
        CountryPunchConfig countryPunchConfig = punchConfigManage.getCountryConfig(currentConfig.getCountry());
        PunchConfigDO countryLevelConfig = countryPunchConfig.queryCountryLevelConfig();

        // 范围检查
        return checkRangeIsValid(
                currentConfig,
                countryPunchConfig.getCountryConfigs(),
                countryLevelConfig
        );
    }

    /**
     * 部门校验逻辑
     */
    private void validateDeptUsage(List<PunchConfigDO> configDOList, List<Long> deptIds, String configNo,
                                   List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos) {
        configDOList.forEach(configDO -> {
            if (!StringUtils.equals(configNo, configDO.getConfigNo())) {
                List<Long> deptIdList = configDO.listDeptIds();
                List<Long> duplicateDeptIds = deptIdList.stream()
                        .filter(deptIds::contains)
                        .collect(Collectors.toList());

                duplicateDeptIds.forEach(deptId -> {
                    AttendanceDept attendanceDept = deptService.getByDeptId(deptId);
                    if (attendanceDept != null) {
                        rangeDuplicateInfos.add(
                                RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                                        attendanceDept.getLocalizeName(), configDO.getConfigName()));
                    }
                });
            }
        });
    }

    /**
     * 用户校验逻辑
     */
    private void validateUserUsage(List<Long> userIds, String configNo,
                                   List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos) {
        List<PunchConfigRangeDO> existedConfigRecords = punchConfigRangeDao.listConfigRanges(userIds)
                .stream()
                .filter(item -> StringUtils.equals(item.getRangeType(), RangeTypeEnum.USER.getCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(existedConfigRecords)) {
            return;
        }

        // 收集所有需要查询的用户ID
        Set<Long> allUserIds = existedConfigRecords.stream()
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        // 一次性批量查询所有用户信息
        Map<Long, AttendanceUser> userMap = userService.listUsersByIds(new ArrayList<>(allUserIds))
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getId, user -> user, (a, b) -> a));

        Map<Long, List<Long>> configIdToUserIdsMap = existedConfigRecords.stream()
                .collect(Collectors.groupingBy(
                        PunchConfigRangeDO::getRuleConfigId,
                        Collectors.mapping(PunchConfigRangeDO::getBizId, Collectors.toList())));

        List<Long> configIdList = new ArrayList<>(configIdToUserIdsMap.keySet());
        List<PunchConfigDO> configDOS = punchConfigDao.listLatestByConfigIds(configIdList);

        configDOS.stream()
                .filter(config -> !StringUtils.equals(config.getConfigNo(), configNo))
                .forEach(config -> {
                    List<Long> repeatUserIds = configIdToUserIdsMap.getOrDefault(config.getId(),
                            Collections.emptyList());
                    for (Long repeatUserId : repeatUserIds) {
                        AttendanceUser attendanceUser = userMap.get(repeatUserId);
                        if (null != attendanceUser) {
                            rangeDuplicateInfos.add(
                                    RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                                            attendanceUser.getLocaleName(), config.getConfigName()));
                        }
                    }
                });
    }

    /**
     * 标记旧配置为过期
     */
    private void markOldConfigAsExpired(PunchConfigDO currentConfig, Date currentTime) {
        currentConfig.setIsLatest(BusinessConstant.N);
        currentConfig.setExpireTime(currentTime);
        BaseDOUtil.fillDOUpdate(currentConfig);
    }

    /**
     * 标记旧配置适用范围为过期
     */
    private void markOldConfigRangeAsExpired(List<PunchConfigRangeDO> rangeDOList, Date currentDate) {
        for (PunchConfigRangeDO punchConfigRangeDO : rangeDOList) {
            punchConfigRangeDO.setIsLatest(BusinessConstant.N);
            punchConfigRangeDO.setExpireTime(currentDate);
            punchConfigRangeDO.setRemark("页面操作导致旧规则过期");
            BaseDOUtil.fillDOUpdate(punchConfigRangeDO);
        }
    }

}
