package com.imile.attendance.rule;

import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.UserPermissionService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.rule.bo.ReissueCardConfigBO;
import com.imile.attendance.rule.command.ReissueCardConfigAddCommand;
import com.imile.attendance.rule.command.ReissueCardConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.ReissueCardConfigUpdateCommand;
import com.imile.attendance.rule.dto.AttendanceArchiveRuleConfigUpdateDTO;
import com.imile.attendance.rule.dto.ConfigRangeDTO;
import com.imile.attendance.rule.dto.ReissueCardConfigDetailDTO;
import com.imile.attendance.rule.dto.ReissueCardConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.factory.ReissueCardConfigFactory;
import com.imile.attendance.rule.mapstruct.PunchConfigMapstruct;
import com.imile.attendance.rule.mapstruct.ReissueCardConfigMapstruct;
import com.imile.attendance.rule.permission.RuleConfigPermissionService;
import com.imile.attendance.rule.query.RuleConfigUserQuery;
import com.imile.attendance.util.PageUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.WorkStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description
 */
@Service
public class ReissueCardConfigService {

    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private ReissueCardConfigFactory reissueCardConfigFactory;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private ReissueCardConfigQueryService reissueCardConfigQueryService;

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private ConverterService converterService;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private RuleConfigPermissionService ruleConfigPermissionService;



    public RuleConfigChangeCheckDTO add(ReissueCardConfigAddCommand addCommand) {
        return reissueCardConfigFactory.add(addCommand);
    }

    public UpdateRuleReflectResult checkUpdateRule(ReissueCardConfigUpdateCommand updateCommand) {
        return reissueCardConfigFactory.checkUpdateRule(updateCommand);
    }

    public RuleConfigChangeCheckDTO update(ReissueCardConfigUpdateCommand updateCommand) {
        return reissueCardConfigFactory.update(updateCommand);
    }

    public UpdateRuleReflectResult checkStatusSwitch(ReissueCardConfigStatusSwitchCommand statusSwitchCommand) {
        return reissueCardConfigFactory.checkStatusSwitch(statusSwitchCommand);
    }

    public RuleConfigChangeCheckDTO statusSwitch(ReissueCardConfigStatusSwitchCommand statusSwitchCommand) {
        return reissueCardConfigFactory.statusSwitch(statusSwitchCommand);
    }

    /**
     * 用户补卡规则适用范围变更
     * 若旧补卡规则适用范围为用户级别则移除
     * 若旧补卡规则适用范围为非用户级别更新为历史版本
     * 添加新用户级别的补卡规则适用范围
     */
    public void userReissueCardConfigRangeUpdate(AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO) {
        reissueCardConfigFactory.userReissueCardConfigRangeUpdate(ruleConfigUpdateDTO);
    }

    /**
     * 处理分页权限
     */
    public PaginationResult<ReissueCardConfigPageDTO> dealWithPagePermission(ReissueCardConfigPageQuery query) {
        String country = query.getCountry();
        List<String> paramCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(country)) {
            paramCountryList.add(country);
        }
        Boolean isChooseDept = Boolean.FALSE;
        PermissionCountryDeptVO permissionDept = ruleConfigPermissionService.getPermissionCountryDeptVO(
                query.getDeptIds(), Lists.newArrayList(paramCountryList));

        // 如果传入部门，就按照部门查询
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            // 设置标志
            isChooseDept = Boolean.TRUE;
        }

        // 如果只传入国家，就按照国家查询
        if (StringUtils.isNotBlank(query.getCountry()) && !isChooseDept) {
            permissionDept.setDeptIdList(new ArrayList<>());
            permissionDept.setHasDeptPermission(false);
            if (CollectionUtils.isEmpty(permissionDept.getCountryList())) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
        }

        //权限部门列表（已经取了人的权限部门和前端传入的部门的交集）
        query.setDeptIds(permissionDept.getDeptIdList());
        //权限国家列表 (已经取了人的权限国家和前端传入的国家的交集)
        query.setAuthLocationCountryList(permissionDept.getCountryList());

        // 非系统管理员
        query.setHasDeptPermission(permissionDept.getHasDeptPermission());
        query.setHasCountryPermission(permissionDept.getHasCountryPermission());
        query.setHasOrDeptAndCountryPermission(permissionDept.getHasOrDeptAndCountryPermission());
        query.setHasAndDeptAndCountryPermission(permissionDept.getHasAndDeptAndCountryPermission());
        query.setIsChooseDept(isChooseDept);
        return null;
    }

    /**
     * 打卡规则列表
     */
    public PaginationResult<ReissueCardConfigPageDTO> pageReissueCardConfigList(ReissueCardConfigPageQuery query) {
        PaginationResult<ReissueCardConfigPageDTO> paginationResult = dealWithPagePermission(query);
        if (paginationResult != null) {
            return paginationResult;
        }
        // 如果传入了用户ID，才做用户维度的范围判断
        List<Long> userIds = query.getUserIdList();
        if (CollectionUtils.isNotEmpty(userIds)) {
            Set<Long> configIdSet = new HashSet<>();
            // 查询用户
            List<AttendanceUser> users = userService.listUsersByIds(userIds);
            if (CollectionUtils.isEmpty(users)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            // 根据 range 表判断哪些用户已有规则
            List<ReissueCardConfigRangeDO> rangeList = reissueCardConfigRangeDao.listNotDeletedConfigRanges(userIds);
            // 用户已有范围的规则ID(包含启用和停用的)
            rangeList.stream()
                    .map(ReissueCardConfigRangeDO::getRuleConfigId)
                    .forEach(configIdSet::add);
            if (CollectionUtils.isEmpty(configIdSet)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            query.setConfigIds(new ArrayList<>(configIdSet));
        }
        PageInfo<ReissueCardConfigDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount())
                .doSelectPageInfo(
                        () -> reissueCardConfigDao.pageQuery(query));
        List<ReissueCardConfigDO> configDOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(configDOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 获取所有补卡配置id
        List<Long> configIds = configDOList.stream()
                .map(ReissueCardConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        List<ReissueCardConfigRangeDO> allRangeList = reissueCardConfigRangeDao.listNotDeletedByConfigIds(configIds);

        List<ReissueCardConfigPageDTO> dtoList = new ArrayList<>();
        for (ReissueCardConfigDO reissueCardConfigDO : configDOList) {
            ReissueCardConfigPageDTO dto = ReissueCardConfigMapstruct.INSTANCE
                    .toReissueCardConfigPageDTO(reissueCardConfigDO);
            setRangeAndUserInfo(dto, reissueCardConfigDO, allRangeList);
            dtoList.add(dto);
        }
        return PageUtil.getPageResult(dtoList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 分页查询补卡配置的用户列表
     * 支持两种场景：
     * 1. 国家级别配置：查询该国家下所有在职非司机且未配置规则的用户
     * 2. 普通配置：查询已配置的用户列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public PaginationResult<RuleConfigUserInfoDTO> pageConfigUserList(RuleConfigUserQuery query) {
        String configNo = query.getConfigNo();
        if (Objects.isNull(configNo)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        ReissueCardConfigBO configBO = reissueCardConfigManage.getConfigBO(configNo);
        if (null == configBO) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<ReissueCardConfigRangeDO> configRangeDOAllList = configBO.getRangeDOList();
        Map<Long, List<ReissueCardConfigRangeDO>> userRangeMap = configRangeDOAllList.stream()
                .collect(Collectors.groupingBy(ReissueCardConfigRangeDO::getBizId));
        List<Long> userIds = new ArrayList<>(userRangeMap.keySet());
        if (CollectionUtils.isEmpty(userIds)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 查询员工
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userIds(userIds)
                .deptIds(query.getDeptIds())
                .locationCountry(query.getLocationCountry())
                .codeOrNameLike(query.getUserCodeOrName())
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .isDelete(IsDeleteEnum.NO.getCode())
                .build();
        if (Objects.isNull(query.getDeptId())) {
            userDaoQuery.setDeptId(null);
        }
        PageInfo<UserInfoDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount())
                .doSelectPageInfo(
                        () -> userInfoDao.userList(userDaoQuery));
        List<UserInfoDO> userList = pageInfo.getList();

        List<RuleConfigUserInfoDTO> ruleConfigUserInfoDTOS = transferRuleConfigUserDTO(userList, userRangeMap);
        return PageUtil.getPageResult(ruleConfigUserInfoDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 查询补卡配置详情
     * 
     * @param configNo 配置编码
     * @return 补卡配置详情
     */
    public ReissueCardConfigDetailDTO queryConfigDetail(String configNo) {
        return reissueCardConfigQueryService.queryConfigDetail(configNo);
    }

    /**
     * 设置范围和员工信息
     */
    private void setRangeAndUserInfo(ReissueCardConfigPageDTO dto,
            ReissueCardConfigDO configDO,
            List<ReissueCardConfigRangeDO> configRangeDOAllList) {
        List<ConfigRangeDTO> configRanges = new ArrayList<>();

        // 获取用户IDs
        List<Long> userIds = configDO.areActive() ? getActiveUserIds(configRangeDOAllList, configDO.getId())
                : getInactiveUserIds(configRangeDOAllList, configDO.getId());

        // 处理国家配置范围
        if (configDO.areCountryLevel()) {
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<AttendanceUser> activeUsers = userService.listActiveAndOnJobUsers(userIds);
                // 停用规则的员工数量为0
                dto.setEmployeeCount(configDO.areActive() ? activeUsers.size() : 0);
            } else {
                dto.setEmployeeCount(0);
            }
            configRanges.add(ConfigRangeDTO.buildCountryRangeDTO(configDO.getCountry()));
        } else {
            // 处理用户范围
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<AttendanceUser> activeUsers = userService.listActiveAndOnJobUsers(userIds);
                // 停用规则的员工数量为0
                dto.setEmployeeCount(configDO.areActive() ? activeUsers.size() : 0);
                configRanges.addAll(buildUserRangeRecords(activeUsers, configRangeDOAllList));
            }
            // 处理部门范围
            configRanges.addAll(buildDeptRangeRecords(configDO));
        }
        dto.setRangeRecords(configRanges);
    }

    private List<Long> getActiveUserIds(List<ReissueCardConfigRangeDO> configRangeDOAllList, Long configId) {
        return configRangeDOAllList.stream()
                .filter(ReissueCardConfigRangeDO::areActive)
                .filter(o -> o.getRuleConfigId().equals(configId))
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    private List<Long> getInactiveUserIds(List<ReissueCardConfigRangeDO> configRangeDOAllList, Long configId) {
        return configRangeDOAllList.stream()
                .filter(ReissueCardConfigRangeDO::areDisabled)
                .filter(o -> o.getRuleConfigId().equals(configId))
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 构建用户范围记录
     */
    private List<ConfigRangeDTO> buildUserRangeRecords(List<AttendanceUser> activeUsers,
            List<ReissueCardConfigRangeDO> configRangeDOS) {

        Set<Long> existingUserIds = configRangeDOS.stream()
                .filter(ReissueCardConfigRangeDO::areUserRange)
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        return activeUsers.stream()
                .filter(user -> existingUserIds.contains(user.getId()))
                .map(ConfigRangeDTO::buildUserRangeDTO)
                .collect(Collectors.toList());
    }

    /**
     * 构建部门范围记录
     */
    private List<ConfigRangeDTO> buildDeptRangeRecords(ReissueCardConfigDO configDO) {
        if (configDO == null || StringUtils.isBlank(configDO.getDeptIds())) {
            return Collections.emptyList();
        }

        List<Long> deptIdList = configDO.listDeptIds();
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }

        return deptService.listByDeptIds(deptIdList).stream()
                .map(ConfigRangeDTO::buildDeptRangeDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换规则配置用户信息
     *
     * @param userList     用户列表
     * @param userRangeMap 用户范围映射
     * @return 规则配置用户信息列表
     */
    private List<RuleConfigUserInfoDTO> transferRuleConfigUserDTO(List<UserInfoDO> userList,
            Map<Long, List<ReissueCardConfigRangeDO>> userRangeMap) {
        // 获取部门信息
        List<Long> deptIds = userList.stream()
                .map(UserInfoDO::getDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(deptIds)
                .stream()
                .collect(Collectors.toMap(AttendanceDept::getId, item -> item, (oldVal, newVal) -> oldVal));

        // 转换用户信息
        List<RuleConfigUserInfoDTO> ruleConfigUserInfoList = PunchConfigMapstruct.INSTANCE
                .toRuleConfigUserInfoDTO(userList);

        // 设置部门信息和创建时间
        for (RuleConfigUserInfoDTO ruleConfigUserInfoDTO : ruleConfigUserInfoList) {
            // 设置部门信息
            AttendanceDept attendanceDept = deptMap.get(ruleConfigUserInfoDTO.getDeptId());
            if (Objects.nonNull(attendanceDept)) {
                ruleConfigUserInfoDTO.setCountry(attendanceDept.getCountry());
                ruleConfigUserInfoDTO.setDeptCode(attendanceDept.getDeptCode());
                ruleConfigUserInfoDTO.setDeptName(attendanceDept.getLocalizeName());
            }
            // 设置规则范围的绑定时间
            if (Objects.nonNull(userRangeMap)) {
                List<ReissueCardConfigRangeDO> userRangeList = userRangeMap.getOrDefault(ruleConfigUserInfoDTO.getId(),
                        Collections.emptyList());
                if (CollectionUtils.isNotEmpty(userRangeList)) {
                    ruleConfigUserInfoDTO.setCreateDate(userRangeList.get(0).getEffectTime());
                }
            }
        }

        converterService.withAnnotation(ruleConfigUserInfoList);
        return ruleConfigUserInfoList;
    }

}
