package com.imile.attendance.rule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR>
 * @since 2025/5/8
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class AttendanceArchiveRuleConfigUpdateDTO {

    /**
     * 旧考勤规则配置ID
     */
    private Long oldConfigId;

    /**
     * 新考勤规则配置ID
     */
    private Long newConfigId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 开始生效时间
     */
    private Date startDate;
}
