package com.imile.attendance.rule.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigStatusSwitchCommand implements Serializable {

    /**
     * 班次ID
     */
    @NotNull(message = "id cannot be empty")
    private Long id;

    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    @NotNull(message = "status cannot be empty")
    private String status;
}
