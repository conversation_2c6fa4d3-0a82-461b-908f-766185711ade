package com.imile.attendance.rule;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigQuery;
import com.imile.attendance.rule.bo.PunchConfigBO;
import com.imile.attendance.rule.command.PunchConfigAddCommand;
import com.imile.attendance.rule.command.PunchConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.PunchConfigUpdateCommand;
import com.imile.attendance.rule.dto.AttendanceArchiveRuleConfigUpdateDTO;
import com.imile.attendance.rule.dto.ConfigRangeDTO;
import com.imile.attendance.rule.dto.PunchConfigDetailDTO;
import com.imile.attendance.rule.dto.PunchConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.factory.PunchConfigFactory;
import com.imile.attendance.rule.mapstruct.PunchConfigMapstruct;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;
import com.imile.attendance.rule.permission.RuleConfigPermissionService;
import com.imile.attendance.rule.query.PunchConfigUserQuery;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.WorkStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Slf4j
@Service
public class PunchConfigService {

    @Resource
    private PunchConfigFactory punchConfigFactory;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private PunchConfigQueryService punchConfigQueryService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private ConverterService converterService;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private RuleConfigPermissionService ruleConfigPermissionService;


    /**
     * 添加
     */
    public RuleConfigChangeCheckDTO add(PunchConfigAddCommand addCommand) {
        return punchConfigFactory.add(addCommand);
    }

    /**
     * 检查打卡规则更新的影响范围，分析规则变更受影响的用户数量
     */
    public UpdateRuleReflectResult checkUpdateRule(PunchConfigUpdateCommand updateCommand) {
        return punchConfigFactory.checkUpdateRule(updateCommand);
    }

    /**
     * 更新
     */
    public RuleConfigChangeCheckDTO update(PunchConfigUpdateCommand updateCommand) {
        return punchConfigFactory.update(updateCommand);
    }

    /**
     * 检查打卡规则状态切换的影响范围,分析状态变更（启用/停用）对用户范围的影响
     */
    public UpdateRuleReflectResult checkStatusSwitch(PunchConfigStatusSwitchCommand statusSwitchCommand) {
        return punchConfigFactory.checkStatusSwitch(statusSwitchCommand);
    }

    /**
     * 启用或停用
     */
    public RuleConfigChangeCheckDTO statusSwitch(PunchConfigStatusSwitchCommand statusSwitchCommand) {
        return punchConfigFactory.statusSwitch(statusSwitchCommand);
    }

    /**
     * 用户打卡规则适用范围变更
     * 若旧打卡规则适用范围为用户级别则移除
     * 若旧打卡规则适用范围为非用户级别更新为历史版本
     * 添加新用户级别的打卡规则适用范围
     */
    public void userPunchConfigRangeUpdate(AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO) {
        punchConfigFactory.userPunchConfigRangeUpdate(ruleConfigUpdateDTO);
    }


    /**
     * 处理打卡分页查询的权限参数
     */
    private PaginationResult<PunchConfigPageDTO> dealWithPagePermission(PunchConfigPageQuery query) {
        String country = query.getCountry();
        List<String> paramCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(country)) {
            paramCountryList.add(country);
        }
        Boolean isChooseDept = Boolean.FALSE;
        PermissionCountryDeptVO permissionDept = ruleConfigPermissionService.getPermissionCountryDeptVO(
                query.getDeptIds(), Lists.newArrayList(paramCountryList));

        // 如果传入部门，就按照部门查询
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            // 设置标志
            isChooseDept = Boolean.TRUE;
        }

        // 如果只传入国家，就按照国家查询
        if (StringUtils.isNotBlank(query.getCountry()) && !isChooseDept) {
            permissionDept.setDeptIdList(new ArrayList<>());
            permissionDept.setHasDeptPermission(false);
            if (CollectionUtils.isEmpty(permissionDept.getCountryList())) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
        }

        //权限部门列表（已经取了人的权限部门和前端传入的部门的交集）
        query.setDeptIds(permissionDept.getDeptIdList());
        //权限国家列表 (已经取了人的权限国家和前端传入的国家的交集)
        query.setAuthLocationCountryList(permissionDept.getCountryList());

        // 非系统管理员
        query.setHasDeptPermission(permissionDept.getHasDeptPermission());
        query.setHasCountryPermission(permissionDept.getHasCountryPermission());
        query.setHasOrDeptAndCountryPermission(permissionDept.getHasOrDeptAndCountryPermission());
        query.setHasAndDeptAndCountryPermission(permissionDept.getHasAndDeptAndCountryPermission());
        query.setIsChooseDept(isChooseDept);
        return null;
    }

    /**
     * 打卡规则列表
     */
    public PaginationResult<PunchConfigPageDTO> pagePunchConfigList(PunchConfigPageQuery query) {
        PaginationResult<PunchConfigPageDTO> paginationResult = dealWithPagePermission(query);
        if (paginationResult != null) {
            return paginationResult;
        }
        // 如果传入了用户ID，才做用户维度的范围判断
        List<Long> userIds = query.getUserIdList();
        if (CollectionUtils.isNotEmpty(userIds)) {
            Set<Long> configIdSet = new HashSet<>();
            // 查询用户
            List<AttendanceUser> users = userService.listUsersByIds(userIds);
            if (CollectionUtils.isEmpty(users)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            // 根据 range 表判断哪些用户已有规则
            List<PunchConfigRangeDO> rangeList = punchConfigRangeDao.listNotDeletedConfigRanges(userIds);
            // 用户已有范围的规则ID(包含启用和停用的)
            rangeList.stream()
                    .map(PunchConfigRangeDO::getRuleConfigId)
                    .forEach(configIdSet::add);
            // 如果查询的用户不为空，但最终的规则配置id为空，则返回空列表
            if (CollectionUtils.isEmpty(configIdSet)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            query.setPunchConfigIds(new ArrayList<>(configIdSet));
        }

        PageInfo<PunchConfigDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> punchConfigDao.pageQuery(query));
        List<PunchConfigDO> punchConfigDOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 获取所有打卡ID
        List<Long> punchConfigIds = punchConfigDOList.stream()
                .map(PunchConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        List<PunchConfigRangeDO> allRangeList = punchConfigRangeDao.listNotDeletedByConfigIds(punchConfigIds);

        List<PunchConfigPageDTO> dtoList = new ArrayList<>();
        for (PunchConfigDO punchConfigDO : punchConfigDOList) {
            PunchConfigPageDTO dto = PunchConfigMapstruct.INSTANCE.toPunchConfigPageDTO(punchConfigDO);
            setRangeAndUserInfo(dto, punchConfigDO, allRangeList, query.getArePageExport());
            dtoList.add(dto);
        }
        converterService.withAnnotation(dtoList);
        if (query.getArePageExport()) {
            // 添加导出的操作日志
            logRecordService.recordOperation(new PunchConfigDO(), LogRecordOptions.builder()
                    .operationType(OperationTypeEnum.PUNCH_CONFIG_EXPORT.getCode())
                    .remark(OperationTypeEnum.PUNCH_CONFIG_EXPORT.getDesc())
                    .build());
        }
        return PageUtil.getPageResult(dtoList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 分页查询打卡配置的用户列表(停用规则返回空列表)
     * 支持两种场景：
     * 1. 国家级别配置：查询该国家下所有在职非司机且未配置规则的用户
     * 2. 普通配置：查询已配置的用户列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public PaginationResult<RuleConfigUserInfoDTO> pagePunchConfigUserList(PunchConfigUserQuery query) {
        String punchConfigNo = query.getPunchConfigNo();
        if (Objects.isNull(punchConfigNo)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        PunchConfigBO punchConfigBO = punchConfigManage.getPunchConfigBO(punchConfigNo);
        if (null == punchConfigBO) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<PunchConfigRangeDO> punchConfigRangeDOAllList = punchConfigBO.getRangeDOList();
        Map<Long, List<PunchConfigRangeDO>> userRangeMap = punchConfigRangeDOAllList.stream()
                .collect(Collectors.groupingBy(PunchConfigRangeDO::getBizId));
        List<Long> userIds = new ArrayList<>(userRangeMap.keySet());
        if (CollectionUtils.isEmpty(userIds)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 查询员工
        PageInfo<UserInfoDO> pageInfo = queryUserList(query, userIds);
        List<UserInfoDO> userList = pageInfo.getList();

        List<RuleConfigUserInfoDTO> ruleConfigUserInfoDTOS = transferRuleConfigUserDTO(userList, userRangeMap);
        return PageUtil.getPageResult(ruleConfigUserInfoDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 查询打卡规则查询的用户列表
     */
    private PageInfo<UserInfoDO> queryUserList(PunchConfigUserQuery query, List<Long> userIds) {
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userIds(userIds)
                .deptIds(query.getDeptIds())
                .locationCountry(query.getLocationCountry())
                .codeOrNameLike(query.getUserCodeOrName())
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .isDelete(IsDeleteEnum.NO.getCode())
                .build();
        if (Objects.isNull(query.getDeptId())) {
            userDaoQuery.setDeptId(null);
        }
        return PageHelper.startPage(query.getCurrentPage(), query.getShowCount())
                .doSelectPageInfo(() -> userInfoDao.userList(userDaoQuery));
    }

    /**
     * 转换规则配置用户信息
     *
     * @param userList     用户列表
     * @param userRangeMap 用户范围映射
     * @return 规则配置用户信息列表
     */
    private List<RuleConfigUserInfoDTO> transferRuleConfigUserDTO(List<UserInfoDO> userList,

                                                                  Map<Long, List<PunchConfigRangeDO>> userRangeMap) {
        // 获取部门信息
        List<Long> deptIds = userList.stream()
                .map(UserInfoDO::getDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(deptIds)
                .stream()
                .collect(Collectors.toMap(AttendanceDept::getId, item -> item, (oldVal, newVal) -> oldVal));

        // 转换用户信息
        List<RuleConfigUserInfoDTO> ruleConfigUserInfoList = PunchConfigMapstruct.INSTANCE.toRuleConfigUserInfoDTO(userList);

        // 设置部门信息和创建时间
        for (RuleConfigUserInfoDTO ruleConfigUserInfoDTO : ruleConfigUserInfoList) {
            // 设置部门信息
            AttendanceDept attendanceDept = deptMap.get(ruleConfigUserInfoDTO.getDeptId());
            if (Objects.nonNull(attendanceDept)) {
                ruleConfigUserInfoDTO.setCountry(attendanceDept.getCountry());
                ruleConfigUserInfoDTO.setDeptCode(attendanceDept.getDeptCode());
                ruleConfigUserInfoDTO.setDeptName(attendanceDept.getLocalizeName());
            }

            // 设置规则范围的绑定时间
            if (Objects.nonNull(userRangeMap)) {
                List<PunchConfigRangeDO> userRangeList = userRangeMap.getOrDefault(ruleConfigUserInfoDTO.getId(), Collections.emptyList());
                if (CollectionUtils.isNotEmpty(userRangeList)) {
                    ruleConfigUserInfoDTO.setCreateDate(userRangeList.get(0).getEffectTime());
                }
            }
        }
        converterService.withAnnotation(ruleConfigUserInfoList);
        return ruleConfigUserInfoList;
    }

    /**
     * 查询打卡配置详情
     *
     * @param configNo 配置编码
     * @return 打卡配置详情
     */
    public PunchConfigDetailDTO queryPunchConfigDetail(String configNo) {
        return punchConfigQueryService.queryPunchConfigDetail(configNo);
    }

    /**
     * 设置范围和员工信息
     */
    private void setRangeAndUserInfo(PunchConfigPageDTO dto,
                                     PunchConfigDO punchConfigDO,
                                     List<PunchConfigRangeDO> punchConfigRangeDOAllList,
                                     boolean arePageExport) {
        List<ConfigRangeDTO> configRanges = new ArrayList<>();

        // 获取用户IDs
        List<Long> userIds = punchConfigDO.areActive() ?
                getActiveUserIds(punchConfigRangeDOAllList, punchConfigDO.getId()) :
                getInactiveUserIds(punchConfigRangeDOAllList, punchConfigDO.getId());

        if (punchConfigDO.areCountryLevel()) {
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<AttendanceUser> activeUsers = userService.listActiveAndOnJobUsers(userIds);
                //停用规则的员工数量为0
                dto.setEmployeeCount(punchConfigDO.areActive() ? activeUsers.size() : 0);
            } else {
                dto.setEmployeeCount(0);
            }
            configRanges.add(ConfigRangeDTO.buildCountryRangeDTO(punchConfigDO.getCountry()));
        } else {
            // 处理用户范围
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<AttendanceUser> activeUsers = userService.listActiveAndOnJobUsers(userIds);
                //停用规则的员工数量为0
                dto.setEmployeeCount(punchConfigDO.areActive() ? activeUsers.size() : 0);
                configRanges.addAll(buildUserRangeRecords(activeUsers, punchConfigRangeDOAllList));
            }
            // 处理部门范围
            configRanges.addAll(buildDeptRangeRecords(punchConfigDO));
        }
        dto.setRangeRecords(configRanges);
        if (arePageExport) {
            //导出需要展示全国家的范围
            boolean notContainsCountry = configRanges.stream()
                    .noneMatch(i -> StringUtils.equals(i.getRangeType(), RuleRangeTypeEnum.COUNTRY.getCode()));
            if (notContainsCountry) {
                configRanges.add(ConfigRangeDTO.buildCountryRangeDTO(punchConfigDO.getCountry()));
            }
            //设置导出的范围字段
            Map<String, List<ConfigRangeDTO>> configRangeMap = configRanges.stream()
                    .collect(Collectors.groupingBy(ConfigRangeDTO::getRangeType));
            for (Map.Entry<String, List<ConfigRangeDTO>> entry : configRangeMap.entrySet()) {
                String rangeType = entry.getKey();
                List<ConfigRangeDTO> configRangeDTOList = entry.getValue();
                RuleRangeTypeEnum ruleRangeTypeEnum = RuleRangeTypeEnum.getInstance(rangeType);
                switch (Objects.requireNonNull(ruleRangeTypeEnum)) {
                    case COUNTRY:
                        dto.setCountryRangeStr(configRangeDTOList.get(0).getBizNameByLang());
                        break;
                    case DEPT:
                        dto.setDeptRangeStr(configRangeDTOList.stream()
                                .map(ConfigRangeDTO::getBizNameByLang)
                                .collect(Collectors.joining(",")));
                        break;
                    case USER:
                        dto.setUserRangeStr(configRangeDTOList.stream()
                                .map(ConfigRangeDTO::getBizNameByLang)
                                .collect(Collectors.joining(",")));
                        break;
                    default:
                        break;
                }
            }
            dto.setCreateDateStr(DateHelper.formatYYYYMMDDHHMMSS(punchConfigDO.getCreateDate()));
            dto.setLastUpdDateStr(DateHelper.formatYYYYMMDDHHMMSS(punchConfigDO.getLastUpdDate()));
        }
    }

    private List<Long> getActiveUserIds(List<PunchConfigRangeDO> configRangeDOAllList, Long configId) {
        return configRangeDOAllList.stream()
                .filter(PunchConfigRangeDO::areActive)
                .filter(o -> o.getRuleConfigId().equals(configId))
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    private List<Long> getInactiveUserIds(List<PunchConfigRangeDO> configRangeDOAllList, Long configId) {
        return configRangeDOAllList.stream()
                .filter(PunchConfigRangeDO::areDisabled)
                .filter(o -> o.getRuleConfigId().equals(configId))
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 构建用户范围记录
     */
    private List<ConfigRangeDTO> buildUserRangeRecords(List<AttendanceUser> activeUsers,
                                                       List<PunchConfigRangeDO> punchConfigRangeDOAllList) {

        Set<Long> existingUserIds = punchConfigRangeDOAllList.stream()
                .filter(PunchConfigRangeDO::areUserRange)
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        return activeUsers.stream()
                .filter(user -> existingUserIds.contains(user.getId()))
                .map(ConfigRangeDTO::buildUserRangeDTO)
                .collect(Collectors.toList());
    }

    /**
     * 构建部门范围记录
     */
    private List<ConfigRangeDTO> buildDeptRangeRecords(PunchConfigDO punchConfigDO) {
        if (punchConfigDO == null || StringUtils.isBlank(punchConfigDO.getDeptIds())) {
            return Collections.emptyList();
        }

        List<Long> deptIdList = punchConfigDO.listDeptIds();
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }

        return deptService.listByDeptIds(deptIdList).stream()
                .map(ConfigRangeDTO::buildDeptRangeDTO)
                .collect(Collectors.toList());
    }

}
