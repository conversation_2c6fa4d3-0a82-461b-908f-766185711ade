package com.imile.attendance.rule.mapstruct;

import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.rule.bo.ReissueCardConfig;
import com.imile.attendance.rule.dto.ReissueCardConfigDetailDTO;
import com.imile.attendance.rule.dto.ReissueCardConfigPageDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface ReissueCardConfigMapstruct {

    ReissueCardConfigMapstruct INSTANCE = Mappers.getMapper(ReissueCardConfigMapstruct.class);


    @Mapping(target = "recordVersion", ignore = true)
    ReissueCardConfigDO toReissueCardConfigDO(ReissueCardConfig reissueCardConfig);


    @Mapping(target = "rangeRecords", ignore = true)
    @Mapping(target = "employeeCount", ignore = true)
    ReissueCardConfigPageDTO toReissueCardConfigPageDTO(ReissueCardConfigDO reissueCardConfigDO);

    @Mapping(target = "isCoverOld", ignore = true)
    @Mapping(target = "applyUserList", ignore = true)
    @Mapping(target = "applyDeptList", ignore = true)
    ReissueCardConfigDetailDTO toDetailDTO(ReissueCardConfigDO reissueCardConfigDO);
}
