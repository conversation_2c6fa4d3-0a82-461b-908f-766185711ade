package com.imile.attendance.rule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PunchClassConfigUpdateAutoShiftDTO implements Serializable {

    /**
     * 历史班次ID
     */
    private Long oldClassId;

    /**
     * 最新班次ID
     */
    private Long newClassId;

    /**
     * 班次规则变更
     */
    private Boolean classRuleUpdate;

    /**
     * 自动清空
     */
    private Boolean isOnlyClearSchedule = Boolean.FALSE;

    /**
     * 本次新增适用范围用户集合
     */
    private Set<Long> addRangeUserIdList;

    /**
     * 本次移除适用范围的用户集合
     */
    private Set<Long> removeRangeUserIdList;

    /**
     * 本次未变化的适用范围的用户集合
     */
    private Set<Long> noChangeRangeUserIdList;
}
