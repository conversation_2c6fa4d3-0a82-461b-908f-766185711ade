package com.imile.attendance.rule.handler;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.mq.MqSend;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.bo.ConfigRangeDiff;
import com.imile.attendance.rule.bo.CountryPunchConfig;
import com.imile.attendance.rule.dto.RuleConfigRangeChangeDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 打卡配置范围处理器
 * 负责处理打卡规则配置的用户和部门范围的增删改操作
 * 包括:
 * - 新增配置范围
 * - 更新配置范围
 * - 停用配置范围
 * - 启用配置范围
 * - 配置范围变更检查
 *
 * <AUTHOR> chen
 * @since 2025/4/10
 */
@Slf4j
@Component
public class PunchConfigRangeHandler {

    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private MqSend mqSend;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    /**
     * 处理新增配置的用户/部门范围
     */
    public RuleConfigRangeChangeDTO addConfigRangeHandler(Date currentDate,
                                                          PunchConfigDO addConfigDO,
                                                          String country,
                                                          List<Long> deptIds,
                                                          List<Long> userIds) {

        // 创建和处理配置范围
        List<PunchConfigRangeDO> addConfigRangeList = new ArrayList<>();
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 特殊处理国家级规则
        if (addConfigDO.areCountryLevel()) {
            // 查询当前国家下未加入规则的在职非司机员工
            List<UserInfoDO> userInfoDOList = punchConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                    RuleRangeUserQuery.builder().country(country).build());
            if (CollectionUtils.isEmpty(userInfoDOList)) {
                return ruleConfigRangeChangeDTO;
            }
            List<Long> userIdList = userInfoDOList.stream()
                    .map(UserInfoDO::getId)
                    .collect(Collectors.toList());
            ruleConfigRangeChangeDTO.addUser(userIdList);
            for (Long userId : userIdList) {
                buildConfigRange(
                        userId,
                        addConfigDO.getId(),
                        addConfigDO.getConfigNo(),
                        RuleRangeTypeEnum.COUNTRY.getCode(),
                        currentDate,
                        addConfigRangeList);
            }
        } else {
            // 确保集合非空
            userIds = ensureNonNull(userIds);
            deptIds = ensureNonNull(deptIds);

            // 处理部门级别用户
            List<Long> deptUserIds = getDepartmentUsers(deptIds, country);

            // 处理用户级别关联
            processUserLevelAssociations(userIds, addConfigDO, currentDate, addConfigRangeList,
                    ruleConfigRangeChangeDTO);

            // 处理部门级别关联
            processDeptLevelAssociations(deptUserIds, addConfigDO, currentDate, addConfigRangeList,
                    ruleConfigRangeChangeDTO);

        }

        // 更新现有配置范围并添加新的配置范围
        updateExistingAndAddNewRanges(ruleConfigRangeChangeDTO, addConfigRangeList, currentDate);

        return ruleConfigRangeChangeDTO;
    }

    /**
     * 检查更新配置的用户/部门范围
     */
    public RuleConfigRangeChangeDTO checkConfigRangeHandler(PunchConfigDO oldConfigDO,
                                                            CountryPunchConfig countryPunchConfig,
                                                            String country,
                                                            List<Long> deptIds,
                                                            List<Long> userIds) {
        // 确保集合非空
        userIds = ensureNonNull(userIds);
        deptIds = ensureNonNull(deptIds);

        // 获取差异分析
        ConfigRangeDiff diff = calculateConfigRangeDiff(oldConfigDO, userIds, deptIds);

        // 获取受影响的部门用户
        List<Long> addDeptUserIds = getDepartmentUsers(diff.getAddDeptIds(), country);
        List<Long> delDeptUserIds = getDepartmentUsers(diff.getDelDeptIds(), country);

        // 创建和处理配置范围
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 处理新增的用户和部门关联
        for (Long addUserId : diff.getAddUserIds()) {
            ruleConfigRangeChangeDTO.addUser(addUserId);
        }
        if (CollectionUtils.isNotEmpty(addDeptUserIds)) {
            // 查询部门级别新增的用户已存在的记录，并过滤
            List<Long> existUserIdList = filterExistingUserLevelUsers(addDeptUserIds);
            List<Long> filteredDeptUserIds = addDeptUserIds.stream()
                    .filter(userId -> !existUserIdList.contains(userId))
                    .collect(Collectors.toList());
            for (Long userId : filteredDeptUserIds) {
                if (ruleConfigRangeChangeDTO.containsUser(userId)) {
                    continue;
                }
                ruleConfigRangeChangeDTO.addUser(userId);
            }
        }
        // 处理删除的用户和部门关联
        List<Long> delUserIds = diff.getDelUserIds();
        if (CollectionUtils.isNotEmpty(delUserIds)) {
            List<AttendanceUser> delUserInfoList = userService.listUsersByIds(delUserIds);
            for (AttendanceUser user : delUserInfoList) {
                if (ruleConfigRangeChangeDTO.containsUser(user.getId())) {
                    continue;
                }
                ruleConfigRangeChangeDTO.addRemovedUser(user.getId());
                // 判断用户是否应该进入兜底规则
                String userCountry = user.getLocationCountry();
                // 处理同国家的用户
                if (StringUtils.equals(countryPunchConfig.getCountry(), userCountry)) {
                    // 如果用户属于被删除的部门，直接进入国家级别配置或无规则
                    if (delDeptUserIds.contains(user.getId())) {
                        ruleConfigRangeChangeDTO.addIntoCountryLevelOrNoRuleUser(user.getId());
                    }
                    // 检查用户的部门是否被其他规则指定
                    boolean userDeptIsInOtherConfig = false;
                    Long userDeptId = user.getDeptId();
                    for (PunchConfigDO configDO : countryPunchConfig.getCountryConfigs()) {
                        if (StringUtils.isBlank(configDO.getDeptIds())) {
                            continue;
                        }
                        if (configDO.listDeptIds().contains(userDeptId)) {
                            userDeptIsInOtherConfig = true;
                        }
                    }
                    // 如果用户的部门没有被其他规则指定，使用国家级别配置或无规则
                    if (!userDeptIsInOtherConfig) {
                        ruleConfigRangeChangeDTO.addIntoCountryLevelOrNoRuleUser(user.getId());
                    }
                }
                // 移除的用户不是当前国家的，进入用户所属国家对应的规则不做统计
            }
        }
        if (CollectionUtils.isNotEmpty(delDeptUserIds)) {
            // 过滤已经被用户级别指定的用户
            List<Long> existUserIdList = filterExistingUserLevelUsers(delDeptUserIds);
            List<Long> filteredDelDeptUserIds = delDeptUserIds.stream()
                    .filter(userId -> !existUserIdList.contains(userId))
                    .collect(Collectors.toList());
            for (Long userId : filteredDelDeptUserIds) {
                if (ruleConfigRangeChangeDTO.containsUser(userId)) {
                    continue;
                }
                ruleConfigRangeChangeDTO.addRemovedUser(userId);
                // 被删除部门下的员工没有在其他规则中被指定为员工级，降级为国家级别配置或无规则
                ruleConfigRangeChangeDTO.addIntoCountryLevelOrNoRuleUser(userId);
            }
        }
        return ruleConfigRangeChangeDTO;
    }

    /**
     * 处理更新配置的用户/部门范围
     */
    public RuleConfigRangeChangeDTO updateConfigRangeHandler(Date currentDate,
                                                             PunchConfigDO oldConfigDO,
                                                             PunchConfigDO newConfigDO,
                                                             CountryPunchConfig countryPunchConfig,
                                                             String country,
                                                             List<Long> deptIds,
                                                             List<Long> userIds) {
        // 确保集合非空
        userIds = ensureNonNull(userIds);
        deptIds = ensureNonNull(deptIds);

        // 获取差异分析
        ConfigRangeDiff diff = calculateConfigUpdateRangeDiff(oldConfigDO, userIds, deptIds);

        // 获取受影响的部门用户
        List<Long> addDeptUserIds = getDepartmentUsers(diff.getAddDeptIds(), country);
        List<Long> delDeptUserIds = getDepartmentUsers(diff.getDelDeptIds(), country);

        // 创建和处理配置范围
        List<PunchConfigRangeDO> addConfigRangeList = new ArrayList<>();
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 处理新增的用户和部门关联
        processNewAssociations(diff.getAddUserIds(), addDeptUserIds, newConfigDO, currentDate,
                addConfigRangeList, ruleConfigRangeChangeDTO);

        // 处理删除的用户和部门关联
        processRemovedAssociations(diff.getDelUserIds(), delDeptUserIds,
                countryPunchConfig, currentDate,
                addConfigRangeList, ruleConfigRangeChangeDTO);

        // 更新现有配置范围并添加新的配置范围
        updateExistingAndAddNewRanges(ruleConfigRangeChangeDTO, addConfigRangeList, currentDate);

        return ruleConfigRangeChangeDTO;
    }

    /**
     * 检查停用配置
     */
    public RuleConfigRangeChangeDTO checkDisableConfig(PunchConfigDO currentConfig) {
        if (currentConfig.areCountryLevel()) {
            List<PunchConfigRangeDO> countryLevelConfigRangeList = punchConfigRangeDao.listByConfigId(currentConfig.getId());
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();
            ruleConfigRangeChangeDTO.addRemovedUser(
                    countryLevelConfigRangeList.stream()
                            //国家下的范围类型都是国家，这里过滤无用
                            .filter(PunchConfigRangeDO::areCountryRange)
                            .map(PunchConfigRangeDO::getBizId)
                            .collect(Collectors.toList())
            );
            return ruleConfigRangeChangeDTO;
        }
        String country = currentConfig.getCountry();
        return checkConfigRangeHandler(currentConfig,
                punchConfigManage.getCountryConfig(country),
                country,
                null,
                null
        );
    }

    /**
     * 停用配置
     */
    public RuleConfigRangeChangeDTO disableConfigRangeHandler(Date currentDate,
                                                              PunchConfigDO oldConfigDO,
                                                              PunchConfigDO newConfigDO,
                                                              CountryPunchConfig countryPunchConfig,
                                                              String country) {
        // 确保集合非空
        List<Long> userIds = ensureNonNull(null);
        List<Long> deptIds = ensureNonNull(null);

        // 获取差异分析
        ConfigRangeDiff diff = calculateConfigUpdateRangeDiff(oldConfigDO, userIds, deptIds);

        // 获取受影响的部门用户
        List<Long> addDeptUserIds = getDepartmentUsers(diff.getAddDeptIds(), country);
        List<Long> delDeptUserIds = getDepartmentUsers(diff.getDelDeptIds(), country);

        // 创建和处理配置范围
        List<PunchConfigRangeDO> addConfigRangeList = new ArrayList<>();
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 处理新增的用户和部门关联,todo 走不到这
        processNewAssociations(diff.getAddUserIds(), addDeptUserIds, newConfigDO, currentDate,
                addConfigRangeList, ruleConfigRangeChangeDTO);

        // 处理删除的用户和部门关联
        processRemovedAssociations(diff.getDelUserIds(), delDeptUserIds,
                countryPunchConfig, currentDate,
                addConfigRangeList, ruleConfigRangeChangeDTO);

        // 更新现有配置范围(无需更新最新标记，更新为停用和失效时间即可)，并添加新的配置范围
        if (!ruleConfigRangeChangeDTO.isUpdateUsersEmpty()) {
            List<PunchConfigRangeDO> updateList = punchConfigRangeDao.listConfigRanges(
                    ruleConfigRangeChangeDTO.getUpdateUserIdList());

            Map<Boolean, List<PunchConfigRangeDO>> updateRangeMap = updateList.stream()
                    .collect(Collectors.groupingBy(i -> Objects.equals(i.getRuleConfigId(), oldConfigDO.getId())));

            updateRangeMap.forEach(
                    (isBelongOldConfig, rangeDOList) -> {
                        // 如果属于旧规则，则更新为停用和失效时间为当前时间
                        if (isBelongOldConfig) {
                            rangeDOList.forEach(item -> {
                                item.setStatus(StatusEnum.DISABLED.getCode());
                                item.setExpireTime(currentDate);
                                item.setRemark("页面操作导致旧规则停用");
                                BaseDOUtil.fillDOUpdate(item);
                            });
                        }
                    });
            punchConfigManage.configRangeUpdateOrAdd(updateList, addConfigRangeList);
        }

        return ruleConfigRangeChangeDTO;
    }

    /**
     * 检查启用配置
     */
    public RuleConfigRangeChangeDTO checkEnableConfig(PunchConfigDO currentConfig) {
        if (currentConfig.areCountryLevel()) {
            List<UserInfoDO> userInfoDOList = punchConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                    RuleRangeUserQuery.builder().country(currentConfig.getCountry()).build());
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();
            ruleConfigRangeChangeDTO.addUser(
                    userInfoDOList.stream()
                            .map(UserInfoDO::getId)
                            .collect(Collectors.toList()));
            return ruleConfigRangeChangeDTO;
        }
        // 1.查询停用的员工级别的人员没有在其他启用中的规则中
        List<PunchConfigRangeDO> disabledConfigRangeDOS = punchConfigRangeDao.listDisabledByConfigId(currentConfig.getId());
        List<Long> disabledUserIds = disabledConfigRangeDOS.stream()
                .filter(PunchConfigRangeDO::areUserRange)
                .map(PunchConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(disabledUserIds)) {
            List<PunchConfigRangeDO> hasExistInOtherConfigUsers = punchConfigRangeDao.listConfigRanges(disabledUserIds)
                    .stream()
                    .filter(PunchConfigRangeDO::areUserRange)
                    .filter(i -> !Objects.equals(i.getRuleConfigId(), currentConfig.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hasExistInOtherConfigUsers)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.DISABLED_CONFIG_USER_IN_OTHER_CONFIG);
            }
        }
        // 2.将有【适配人数】名人员会自动匹配该规则并重新计算考勤结果
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 2.1 绑定部门的员工匹配
        String deptIds = currentConfig.getDeptIds();
        if (StringUtils.isNotBlank(deptIds)) {
            List<Long> deptIdList = currentConfig.listDeptIds();
            List<AttendanceUser> deptUsers = userService.listOnJobNonDriverUserByDeptIdList(deptIdList,
                    currentConfig.getCountry());
            List<Long> deptUserIds = deptUsers.stream()
                    .map(AttendanceUser::getId)
                    .collect(Collectors.toList());
            // 查询非国家级的人员
            List<Long> existDeptUserIds = punchConfigRangeDao.listConfigRanges(deptUserIds)
                    .stream()
                    .filter(PunchConfigRangeDO::areNotCountryRange)
                    .map(PunchConfigRangeDO::getBizId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existDeptUserIds)) {
                deptUserIds = deptUserIds.stream()
                        .filter(deptUserId -> !existDeptUserIds.contains(deptUserId))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(deptUserIds)) {
                for (Long deptUserId : deptUserIds) {
                    ruleConfigRangeChangeDTO.addUser(deptUserId);
                }
            }
        }
        // 2.2 绑定员工级别的员工匹配
        for (Long noLatestUserId : disabledUserIds) {
            ruleConfigRangeChangeDTO.addUser(noLatestUserId);
        }
        return ruleConfigRangeChangeDTO;
    }

    /**
     * 启用配置
     */
    public RuleConfigRangeChangeDTO enableConfigRangeHandler(Date currentDate,
                                                             PunchConfigDO oldConfigDO,
                                                             PunchConfigDO newConfigDO) {
        // 1.员工级别的人员没有在其他启用中的规则中
        List<PunchConfigRangeDO> disabledConfigRangeDOS = punchConfigRangeDao.listDisabledByConfigId(oldConfigDO.getId());
        List<Long> disabledUserIds = disabledConfigRangeDOS.stream()
                .filter(PunchConfigRangeDO::areUserRange)
                .map(PunchConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(disabledUserIds)) {
            List<PunchConfigRangeDO> hasExistInOtherConfigUsers = punchConfigRangeDao.listConfigRanges(disabledUserIds)
                    .stream()
                    .filter(PunchConfigRangeDO::areUserRange)
                    .filter(i -> !Objects.equals(i.getRuleConfigId(), oldConfigDO.getId()))
                    .collect(Collectors.toList());
            // 如果存在其他启用中的规则，则抛出异常
            if (CollectionUtils.isNotEmpty(hasExistInOtherConfigUsers)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.DISABLED_CONFIG_USER_IN_OTHER_CONFIG);
            }
        }
        // 2.将有【适配人数】名人员会自动匹配该规则并重新计算考勤结果
        List<PunchConfigRangeDO> addConfigRangeList = new ArrayList<>();
        List<PunchConfigRangeDO> updateConfigRangeList = new ArrayList<>();
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 2.1 绑定部门的员工匹配
        String deptIds = oldConfigDO.getDeptIds();
        if (StringUtils.isNotBlank(deptIds)) {
            List<Long> deptIdList = oldConfigDO.listDeptIds();
            List<AttendanceUser> deptUsers = userService.listOnJobNonDriverUserByDeptIdList(deptIdList, oldConfigDO.getCountry());
            List<Long> deptUserIds = deptUsers.stream()
                    .map(AttendanceUser::getId)
                    .collect(Collectors.toList());
            //查询非国家级的人员
            List<Long> existDeptUserIds = punchConfigRangeDao.listConfigRanges(deptUserIds)
                    .stream()
                    .filter(PunchConfigRangeDO::areNotCountryRange)
                    .map(PunchConfigRangeDO::getBizId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existDeptUserIds)) {
                deptUserIds = deptUserIds.stream()
                        .filter(deptUserId -> !existDeptUserIds.contains(deptUserId))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(deptUserIds)) {
                for (Long deptUserId : deptUserIds) {
                    ruleConfigRangeChangeDTO.addUser(deptUserId);
                    buildConfigRange(deptUserId, newConfigDO.getId(),
                            newConfigDO.getConfigNo(),
                            RuleRangeTypeEnum.DEPT.getCode(), currentDate,
                            addConfigRangeList);
                }
            }
        }
        // 2.2 绑定员工级别的员工匹配
        for (Long noLatestUserId : disabledUserIds) {
            ruleConfigRangeChangeDTO.addUser(noLatestUserId);
            buildConfigRange(noLatestUserId, newConfigDO.getId(),
                    newConfigDO.getConfigNo(),
                    RuleRangeTypeEnum.USER.getCode(), currentDate,
                    addConfigRangeList);
        }

        // 更新现有配置范围
        if (!ruleConfigRangeChangeDTO.isUpdateUsersEmpty()) {
            // 查询启用最新的范围，并更新为过期
            List<PunchConfigRangeDO> updateList = punchConfigRangeDao.listConfigRanges(
                    ruleConfigRangeChangeDTO.getUpdateUserIdList());
            updateList.forEach(item -> {
                item.setIsLatest(BusinessConstant.N);
                item.setExpireTime(currentDate);
                item.setRemark("页面启用操作导致旧规则过期");
                BaseDOUtil.fillDOUpdate(item);
            });
            updateConfigRangeList.addAll(updateList);
        }
        // 将历史的停用范围数据修改为非最新
        disabledConfigRangeDOS.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            item.setRemark("页面启用操作导致旧的停用规则为非最新");
            BaseDOUtil.fillDOUpdate(item);
        });
        updateConfigRangeList.addAll(disabledConfigRangeDOS);

        punchConfigManage.configRangeUpdateOrAdd(updateConfigRangeList, addConfigRangeList);

        return ruleConfigRangeChangeDTO;
    }

    /**
     * 确保集合非空
     */
    private <T> List<T> ensureNonNull(List<T> list) {
        return Optional.ofNullable(list).orElseGet(Lists::newArrayList);
    }

    /**
     * 根据部门ID列表获取部门用户
     */
    private List<Long> getDepartmentUsers(List<Long> deptIds, String country) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Lists.newArrayList();
        }

        // 查询部门下的人员
        List<AttendanceUser> deptUsers = userService.listOnJobNonDriverUserByDeptIdList(deptIds, country);

        // 过滤掉常驻国不是country的用户(只处理部门下为当前常驻国的用户)
        return deptUsers.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());
    }

    /**
     * 处理用户级别关联
     */
    private void processUserLevelAssociations(List<Long> userIds,
                                              PunchConfigDO configDO,
                                              Date currentDate,
                                              List<PunchConfigRangeDO> addConfigRangeList,
                                              RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        for (Long userId : userIds) {
            ruleConfigRangeChangeDTO.addUser(userId);
            buildConfigRange(userId, configDO.getId(),
                    configDO.getConfigNo(),
                    RuleRangeTypeEnum.USER.getCode(), currentDate,
                    addConfigRangeList);
        }
    }

    /**
     * 处理部门级别关联
     */
    private void processDeptLevelAssociations(List<Long> deptUserIds,
                                              PunchConfigDO configDO,
                                              Date currentDate,
                                              List<PunchConfigRangeDO> addConfigRangeList,
                                              RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        if (CollectionUtils.isEmpty(deptUserIds)) {
            return;
        }

        // 查询部门级别新增的用户已存在的记录，并过滤
        List<Long> existUserIdList = filterExistingUserLevelUsers(deptUserIds);

        List<Long> filteredDeptUserIds = deptUserIds.stream()
                .filter(userId -> !existUserIdList.contains(userId))
                .collect(Collectors.toList());

        for (Long userId : filteredDeptUserIds) {
            if (ruleConfigRangeChangeDTO.containsUser(userId)) {
                continue;
            }
            ruleConfigRangeChangeDTO.addUser(userId);
            buildConfigRange(userId, configDO.getId(),
                    configDO.getConfigNo(),
                    RuleRangeTypeEnum.DEPT.getCode(), currentDate,
                    addConfigRangeList);
        }
    }

    /**
     * 过滤已存在用户级别关联的用户
     */
    private List<Long> filterExistingUserLevelUsers(List<Long> userIds) {
        return punchConfigRangeDao.listConfigRanges(userIds)
                .stream()
                .filter(item -> StringUtils.equals(item.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 计算配置变动导致范围的差异
     */
    private ConfigRangeDiff calculateConfigUpdateRangeDiff(PunchConfigDO oldConfigDO,
                                                           List<Long> newUserIds,
                                                           List<Long> newDeptIds) {
        // 获取旧配置的用户级别人员
        List<Long> oldUserIds = punchConfigRangeDao.listByConfigIds(Collections.singletonList(oldConfigDO.getId()))
                .stream()
                .filter(item -> StringUtils.equals(item.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        // 计算用户级别的增删
        List<Long> addUserIds = new ArrayList<>(newUserIds);

        List<Long> delUserIds = oldUserIds.stream()
                .filter(id -> !newUserIds.contains(id))
                .collect(Collectors.toList());

        // 获取旧配置的部门
        List<Long> oldDeptIds = oldConfigDO.listDeptIds();

        // 计算部门级别的增删
        List<Long> addDeptIds = new ArrayList<>(newDeptIds);

        List<Long> delDeptIds = oldDeptIds.stream()
                .filter(id -> !newDeptIds.contains(id))
                .collect(Collectors.toList());

        return new ConfigRangeDiff(addUserIds, delUserIds, addDeptIds, delDeptIds);
    }

    /**
     * 计算实际配置变动导致的范围差异
     */
    private ConfigRangeDiff calculateConfigRangeDiff(PunchConfigDO oldConfigDO,
                                                     List<Long> newUserIds,
                                                     List<Long> newDeptIds) {
        // 获取旧配置的用户级别人员
        List<Long> oldUserIds = punchConfigRangeDao.listByConfigIds(Collections.singletonList(oldConfigDO.getId()))
                .stream()
                .filter(item -> StringUtils.equals(item.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        // 计算用户级别的增删
        List<Long> addUserIds = newUserIds.stream()
                .filter(id -> !oldUserIds.contains(id))
                .collect(Collectors.toList());

        List<Long> delUserIds = oldUserIds.stream()
                .filter(id -> !newUserIds.contains(id))
                .collect(Collectors.toList());

        // 获取旧配置的部门
        List<Long> oldDeptIds = oldConfigDO.listDeptIds();

        // 计算部门级别的增删
        List<Long> addDeptIds = newDeptIds.stream()
                .filter(id -> !oldDeptIds.contains(id))
                .collect(Collectors.toList());

        List<Long> delDeptIds = oldDeptIds.stream()
                .filter(id -> !newDeptIds.contains(id))
                .collect(Collectors.toList());

        return new ConfigRangeDiff(addUserIds, delUserIds, addDeptIds, delDeptIds);
    }

    /**
     * 处理新增的用户/部门关联
     */
    private void processNewAssociations(List<Long> addUserIds,
                                        List<Long> addDeptUserIds,
                                        PunchConfigDO configDO,
                                        Date currentDate,
                                        List<PunchConfigRangeDO> addConfigRangeList,
                                        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        // 处理用户级别关联
        processUserLevelAssociations(addUserIds, configDO, currentDate, addConfigRangeList, ruleConfigRangeChangeDTO);

        // 处理部门级别关联
        processDeptLevelAssociations(addDeptUserIds, configDO, currentDate, addConfigRangeList,
                ruleConfigRangeChangeDTO);
    }

    /**
     * 处理被移除的用户/部门关联
     */
    private void processRemovedAssociations(List<Long> delUserIds,
                                            List<Long> delDeptUserIds,
                                            CountryPunchConfig countryPunchConfig,
                                            Date currentDate,
                                            List<PunchConfigRangeDO> addConfigRangeList,
                                            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        // 处理被移除的用户级别关联
        if (!CollectionUtils.isEmpty(delUserIds)) {
            processRemovedUserLevelAssociations(
                    delUserIds, countryPunchConfig,
                    delDeptUserIds, currentDate,
                    addConfigRangeList, ruleConfigRangeChangeDTO);
        }

        // 处理被移除的部门级别关联
        if (!CollectionUtils.isEmpty(delDeptUserIds)) {
            processRemovedDeptLevelAssociations(
                    delDeptUserIds,
                    countryPunchConfig,
                    currentDate,
                    addConfigRangeList,
                    ruleConfigRangeChangeDTO);
        }
    }

    /**
     * 处理被移除的用户级别关联
     */
    private void processRemovedUserLevelAssociations(List<Long> delUserIds,
                                                     CountryPunchConfig countryPunchConfig,
                                                     List<Long> delDeptUserIds,
                                                     Date currentDate,
                                                     List<PunchConfigRangeDO> addConfigRangeList,
                                                     RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        List<AttendanceUser> delUserInfoList = userService.listUsersByIds(delUserIds);

        // 获取所有可能需要的国家配置
        Map<String, CountryPunchConfig> countryConfigMap = getCountryPunchConfigMap(delUserInfoList,
                countryPunchConfig);

        for (AttendanceUser user : delUserInfoList) {
            if (ruleConfigRangeChangeDTO.containsUser(user.getId())) {
                continue;
            }
            ruleConfigRangeChangeDTO.addRemovedUser(user.getId());

            String userCountry = user.getLocationCountry();

            // 判断用户是否应该进入兜底规则
            if (StringUtils.equals(countryPunchConfig.getCountry(), userCountry)) {
                // 处理同国家的用户
                handleSameCountryUserRemoval(user, delDeptUserIds, countryPunchConfig,
                        currentDate, addConfigRangeList);
            } else {
                // 处理不同国家的用户
                handleDifferentCountryUserRemoval(user, countryConfigMap, currentDate, addConfigRangeList);
            }
        }
    }

    /**
     * 处理同国家被移除用户的重新分配
     */
    private void handleSameCountryUserRemoval(AttendanceUser user,
                                              List<Long> delDeptUserIds,
                                              CountryPunchConfig countryPunchConfig,
                                              Date currentDate,
                                              List<PunchConfigRangeDO> addConfigRangeList) {
        // 如果用户属于被删除的部门，直接进入兜底规则
        if (delDeptUserIds.contains(user.getId())) {
            // buildFallbackConfigRange(user.getId(), currentDate,
            // countryPunchConfigBO.queryLatestCountryLevelConfig(), addConfigRangeList);
            log.info("被删除员工={}属于被删除的部门中，降级为国家级规则", user.getId());
            return;
        }

        // 检查用户的部门是否被其他规则指定
        for (PunchConfigDO configDO : countryPunchConfig.getCountryConfigs()) {
            if (StringUtils.isBlank(configDO.getDeptIds())) {
                continue;
            }

            List<Long> deptIds = configDO.listDeptIds();
            if (deptIds.contains(user.getDeptId())) {
                buildConfigRange(user.getId(), configDO.getId(),
                        configDO.getConfigNo(), RuleRangeTypeEnum.DEPT.getCode(),
                        currentDate, addConfigRangeList);
                return;
            }
        }

        // 如果部门没有被其他规则指定，使用兜底规则
        log.info("被删除员工={}的部门不属于其他规则，降级为国家级规则", user.getId());
        buildFallbackConfigRange(
                user.getId(),
                currentDate,
                countryPunchConfig,
                addConfigRangeList);
    }

    /**
     * 处理不同国家被移除用户的重新分配
     */
    private void handleDifferentCountryUserRemoval(AttendanceUser user,
                                                   Map<String, CountryPunchConfig> countryConfigMap,
                                                   Date currentDate,
                                                   List<PunchConfigRangeDO> addConfigRangeList) {
        String userCountry = user.getLocationCountry();
        CountryPunchConfig userCountryPunchConfig = countryConfigMap.get(userCountry);

        List<PunchConfigDO> userCountryConfigs = userCountryPunchConfig.getCountryConfigs();
        // 如果用户国家没有打卡规则配置，记录日志并跳过
        if (CollectionUtils.isEmpty(userCountryConfigs)) {
            log.info("被删除用户：{}，国家：{}，没有对应的打卡规则，故用户当前无打卡规则",
                    user.getUserCode(), userCountry);
            return;
        }

        // 检查用户的部门是否被用户国家的其他规则指定
        boolean departmentRuleFound = false;
        for (PunchConfigDO configDO : userCountryConfigs) {
            if (StringUtils.isBlank(configDO.getDeptIds())) {
                continue;
            }

            List<Long> deptIds = configDO.listDeptIds();
            if (deptIds.contains(user.getDeptId())) {
                buildConfigRange(user.getId(), configDO.getId(),
                        configDO.getConfigNo(), RuleRangeTypeEnum.DEPT.getCode(),
                        currentDate, addConfigRangeList);
                departmentRuleFound = true;
                break;
            }
        }

        // 如果没有找到部门规则，使用用户国家的兜底规则
        if (!departmentRuleFound) {
            buildFallbackConfigRange(
                    user.getId(),
                    currentDate,
                    userCountryPunchConfig,
                    addConfigRangeList);
            log.info("被删除用户：{}，国家：{}，用户所在部门没有绑定规则，用户进入国家级规则",
                    user.getUserCode(), userCountry);
        }
    }

    /**
     * 处理被移除的部门级别关联
     */
    private void processRemovedDeptLevelAssociations(List<Long> delDeptUserIds,
                                                     CountryPunchConfig countryPunchConfig,
                                                     Date currentDate,
                                                     List<PunchConfigRangeDO> addConfigRangeList,
                                                     RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        // 过滤已经被用户级别指定的用户
        List<Long> existUserIdList = filterExistingUserLevelUsers(delDeptUserIds);

        List<Long> filteredDelDeptUserIds = delDeptUserIds.stream()
                .filter(userId -> !existUserIdList.contains(userId))
                .collect(Collectors.toList());

        for (Long userId : filteredDelDeptUserIds) {
            if (ruleConfigRangeChangeDTO.containsUser(userId)) {
                continue;
            }
            ruleConfigRangeChangeDTO.addRemovedUser(userId);
            buildFallbackConfigRange(
                    userId,
                    currentDate,
                    countryPunchConfig,
                    addConfigRangeList);
            log.info("被删除部门下的员工：{}没有在其他规则中被指定为员工级，降级为国家级规则", userId);
        }
    }

    /**
     * 更新现有配置范围并添加新的配置范围
     */
    private void updateExistingAndAddNewRanges(RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO,
                                               List<PunchConfigRangeDO> addConfigRangeList,
                                               Date currentDate) {
        if (ruleConfigRangeChangeDTO.isUpdateUsersEmpty()) {
            return;
        }

        List<PunchConfigRangeDO> updateList = punchConfigRangeDao.listConfigRanges(
                ruleConfigRangeChangeDTO.getUpdateUserIdList());

        updateList.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            item.setExpireTime(currentDate);
            item.setRemark("页面操作导致旧规则过期");
            BaseDOUtil.fillDOUpdate(item);
        });

        punchConfigManage.configRangeUpdateOrAdd(updateList, addConfigRangeList);
    }

    /**
     * 构建配置范围对象
     */
    private void buildConfigRange(Long userId,
                                  Long configId,
                                  String configNo,
                                  String rangeType,
                                  Date date,
                                  List<PunchConfigRangeDO> addRangeList) {
        PunchConfigRangeDO rangeDO = new PunchConfigRangeDO();
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setBizId(userId);
        rangeDO.setRuleConfigId(configId);
        rangeDO.setRuleConfigNo(configNo);
        rangeDO.setRangeType(rangeType);
        rangeDO.setEffectTime(date);
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        rangeDO.setRemark("页面操作导致新增");
        BaseDOUtil.fillDOInsert(rangeDO);
        addRangeList.add(rangeDO);
    }

    /**
     * 构建兜底配置范围对象
     */
    private void buildFallbackConfigRange(Long userId,
                                          Date date,
                                          CountryPunchConfig countryPunchConfig,
                                          List<PunchConfigRangeDO> addRangeList) {
        if (null == countryPunchConfig) {
            return;
        }
        PunchConfigDO countryLevelConfig = countryPunchConfig.queryCountryLevelConfig();
        if (null == countryLevelConfig) {
            log.info("userId:{}的常驻国:{}没有国家级打卡规则，无法为其分配规则", userId, countryPunchConfig.getCountry());
            return;
        }
        buildBackToCountryConfigRange(
                userId,
                countryLevelConfig.getId(),
                countryLevelConfig.getConfigNo(),
                date,
                addRangeList);
    }

    /**
     * 构建回退到国家级范围对象
     */
    private void buildBackToCountryConfigRange(Long userId,
                                               Long configId,
                                               String configNo,
                                               Date date,
                                               List<PunchConfigRangeDO> addRangeList) {

        PunchConfigRangeDO rangeDO = new PunchConfigRangeDO();
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setBizId(userId);
        rangeDO.setRuleConfigId(configId);
        rangeDO.setRuleConfigNo(configNo);
        rangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
        rangeDO.setEffectTime(date);
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        rangeDO.setRemark("页面操作导致回退到国家级规则");
        BaseDOUtil.fillDOInsert(rangeDO);
        addRangeList.add(rangeDO);
    }

    /**
     * 获取删除用户所在国家的打卡规则配置映射
     */
    private Map<String, CountryPunchConfig> getCountryPunchConfigMap(List<AttendanceUser> delUserInfoList,
                                                                     CountryPunchConfig countryPunchConfig) {
        // 获取需要删除的用户所在的所有国家
        Set<String> delUserCountrySet = delUserInfoList.stream()
                .map(AttendanceUser::getLocationCountry)
                .collect(Collectors.toSet());
        Map<String, CountryPunchConfig> countryConfigMap = new HashMap<>();
        // 添加当前国家的配置
        countryConfigMap.put(countryPunchConfig.getCountry(), countryPunchConfig);
        // 获取其他国家的配置
        for (String delUserCountry : delUserCountrySet) {
            if (!StringUtils.equals(countryPunchConfig.getCountry(), delUserCountry)) {
                CountryPunchConfig delUserCountryPunchConfig = punchConfigManage.getCountryConfig(delUserCountry);
                countryConfigMap.put(delUserCountry, delUserCountryPunchConfig);
            }
        }
        return countryConfigMap;
    }

}
