package com.imile.attendance.rule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PunchClassConfigAddAutoShiftDTO implements Serializable {

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 日期
     */
    private Long dayId;

    /**
     * 本次新增适用范围用户集合
     */
    private Set<Long> newRangeUserIdList;
}
