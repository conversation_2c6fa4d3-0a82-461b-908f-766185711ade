package com.imile.attendance.third.job;

import com.imile.attendance.constants.BusinessConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description 同步中控考勤打卡记录
 */
@Slf4j
@Component
public class SyncEmployeeAttendanceHandler {

    @XxlJob(BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER)
    public ReturnT<String> syncEmployeeAttendanceHandler(String param) {
        return null;
    }
}
