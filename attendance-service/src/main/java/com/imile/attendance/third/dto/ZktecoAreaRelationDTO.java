package com.imile.attendance.third.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Data
public class ZktecoAreaRelationDTO {

    /**
     * 关联表ID
     */
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 中控区域ID
     */
    private Integer zktecoAreaId;

    /**
     * 中控区域名称
     */
    private String zktecoAreaName;

    /**
     * 中控区域编码
     */
    private String zktecoAreaCode;

    /**
     * 考勤机编号
     */
    private List<String> terminalSnList;

    /**
     * 部门,存储多个
     */
    private List<ZktecoAreaRelationDeptDTO> zktecoAreaRelationDeptDTOS;

    /**
     * 用户编码
     */
    private List<ZktecoUserDTO> userList;

    /**
     * 最近修改日期
     */
    private Date lastUpdDate;

    /**
     * 最近修改人编码
     */
    private String lastUpdUserCode;

    /**
     * 最近修改人名称
     */
    private String lastUpdUserName;
}
