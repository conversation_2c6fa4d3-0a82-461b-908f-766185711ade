package com.imile.attendance.third.application;

import com.imile.attendance.infrastructure.repository.third.model.ZktecoAreaSnRelationDO;
import com.imile.attendance.infrastructure.repository.third.query.ZktecoAreaRelationQueryDTO;
import com.imile.attendance.third.ZktecoAreaSnRelationService;
import com.imile.attendance.third.dto.ZktecoAreaRelationDTO;
import com.imile.common.page.PaginationResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/5 
 * @Description
 */
@Service
public class ZktecoAreaSnRelationApplicationService {

    @Resource
    private ZktecoAreaSnRelationService zktecoAreaSnRelationService;

    public PaginationResult<ZktecoAreaRelationDTO> zktecoAreaRelationList(ZktecoAreaRelationQueryDTO queryDTO) {
        return zktecoAreaSnRelationService.zktecoAreaRelationList(queryDTO);
    }

    public boolean zktecoAreaRelationUpdate(Long id, String country, List<Long> deptIdList, List<Long> userIdList) {
        return zktecoAreaSnRelationService.zktecoAreaRelationUpdate(id, country, deptIdList, userIdList);
    }

    public List<ZktecoAreaSnRelationDO> selectByDeptId(Long deptId) {
        return zktecoAreaSnRelationService.selectByDeptId(deptId);
    }

    public List<ZktecoAreaSnRelationDO> selectByUserId(Long userId){
        return zktecoAreaSnRelationService.selectByUserId(userId);
    }

    public boolean zktecoAreaRelationSync(){
        return zktecoAreaSnRelationService.zktecoAreaRelationSync();
    }
}
