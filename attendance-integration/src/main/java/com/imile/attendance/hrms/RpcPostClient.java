package com.imile.attendance.hrms;



import com.imile.hrms.api.base.param.PostConditionParam;
import com.imile.hrms.api.base.result.PostDTO;



import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public interface RpcPostClient {

    /**
     * 根据岗位ID获取岗位
     *
     * @param id 岗位ID
     * @return PostDTO
     */
    PostDTO getPostById(Long id);



    /**
     * 根据动态条件获取岗位
     *
     * @param param PostConditionParam
     * @return List<PostDTO>
     */
    List<PostDTO> listPostByCondition(PostConditionParam param);
}
