package com.imile.attendance.hrms.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.hrms.RpcPostClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hrms.api.base.api.PostApi;
import com.imile.hrms.api.base.param.PostConditionParam;
import com.imile.hrms.api.base.result.PostDTO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Component
public class RpcPostClientImpl implements RpcPostClient {


    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 15000)
    private PostApi postApi;


    @Override
    @Cached(name = Constants.CacheKey.POST_CACHE_KEY,
            key = "#id",
            cacheType = CacheType.REMOTE,
            expire = 10,
            timeUnit = TimeUnit.MINUTES
    )
    public PostDTO getPostById(Long id) {
        return RpcResultProcessor.process(postApi.getPostByCode(String.valueOf(id)));
    }

    @Override
    public List<PostDTO> listPostByCondition(PostConditionParam param) {
        return RpcResultProcessor.process(postApi.listPostByCondition(param));
    }
}
