package com.imile.attendance.hermes;

import com.imile.hermes.business.dto.BusCityResultDTO;
import com.imile.hermes.business.dto.BusZoneListDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public interface RpcHermesBusZoneListClient {

    /**
     * 查询下级地区
     */
    List<BusZoneListDTO> queryByParentId(Long parentId, Long orgId);

    List<BusZoneListDTO> queryBusZoneByNames(List<String> zoneName, Integer regionLevel, Long orgId);

    /**
     * 根据国家查询城市列表
     */
    List<BusCityResultDTO> queryCityByCountry(String countryName, Long orgId);

    /**
     * 根据行政区级别查询行政区列表
     */
    List<BusZoneListDTO> queryBusZoneByRegionLevel(Integer regionLevel, Long orgId);
}
