package com.imile.attendance.hermes.dto;

import com.imile.attendance.util.CommonUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 国家配置dto
 */
@Data
public class CountryDTO implements Serializable {

    /**
     * 国家编码
     */
    private String countryShort;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 时区
     */
    private String timeZone;

    /**
     * 目的国时间
     */
    private Date countryDate;


    /**
     * 获取国家时区时间
     */
    public Date getCountryTimeZoneDate(Date date) {
        return CommonUtil.convertDateByTimeZonePlus(this.timeZone, date);
    }
}
