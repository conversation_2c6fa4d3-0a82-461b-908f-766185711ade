package com.imile.attendance.ipep.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.ipep.RpcIpepClient;
import com.imile.attendance.ipep.dto.OssApiVo;
import com.imile.attendance.util.UserInfoUtil;
import com.imile.common.constant.BusinessConstant;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.ipep.dto.NoLoginTokenInfoDTO;
import com.imile.ipep.dto.OssApiDto;
import com.imile.util.file.FileUtils;
import com.imile.util.http.HttpResult;
import com.imile.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.imile.genesis.api.enums.DatabaseEnum.HRMS;

/**
 * <AUTHOR> chen
 * @Date 2025/4/2 
 * @Description
 */
@Slf4j
@Component
public class RpcIpepClientSupport {

    private final String UPLOAD_PATH = "/ipep/oss/upload";

    private static final String NO_LOGIN_UPLOAD_PATH = "/ipep/oss/noLoginRequired/upload";

    @Resource
    private AttendanceProperties attendanceProperties;

    @Resource
    private RpcIpepClient rpcIpepClient;

    /**
     * 根据fileKey查询下载地址
     *
     * @param fileKey
     * @param bucketType 1私有imile-store 2公有imile-static
     * @return
     */
    public OssApiVo getUrlByFileKey(String fileKey, Integer bucketType) {
        OssApiDto ossApiDto = rpcIpepClient.getUrlByFileKey(fileKey, bucketType);
        OssApiVo ossApiVo = new OssApiVo();
        if (ossApiDto != null) {
            BeanUtils.copyProperties(ossApiDto, ossApiVo);
        }
        return ossApiVo;
    }


    /**
     * 根据fileKeys查询下载地址
     *
     * @param fileKeys   文件相对路径列表
     * @param bucketType 1私有imile-store 2公有imile-static
     * @return
     */
    public List<OssApiVo> getUrlByFileKeys(List<String> fileKeys, Integer bucketType) {
        if (log.isDebugEnabled()) {
            log.debug("ipep根据fileKeys查询下载地址入参fileKeys:{},bucketType:{}", JSON.toJSONString(fileKeys), bucketType);
        }
        List<OssApiDto> ossApiDtos = rpcIpepClient.getUrlByFileKeys(fileKeys, bucketType);
        List<OssApiVo> ossApiVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ossApiDtos)) {
            ossApiVos = com.imile.util.BeanUtils.convert(OssApiVo.class, ossApiDtos);
        }
        return ossApiVos;
    }

    /**
     * 根据fileKeys查询下载地址
     *
     * @param bucketType 1私有imile-store 2公有imile-static
     * @param fileKeys   文件相对路径列表
     * @return
     */
    public Map<String, OssApiVo> getUrlByFileKeys(Integer bucketType, String... fileKeys) {
        List<OssApiVo> ossApiVos = this.getUrlByFileKeys(Arrays.asList(fileKeys), bucketType);
        return ossApiVos.stream().collect(Collectors.toMap(OssApiVo::getFileKey, x -> x, (x1, x2) -> x1));
    }

    /**
     * 根据fileKeys查询下载地址
     *
     * @param bucketType 1私有imile-store 2公有imile-static
     * @param fileKeys   文件相对路径列表
     * @return
     */
    public Map<String, OssApiVo> getUrlByFileKeys(Integer bucketType, List<String> fileKeys) {
        String[] strings = new String[fileKeys.size()];
        fileKeys.toArray(strings);
        return this.getUrlByFileKeys(bucketType, strings);
    }

    /**
     * 根据fileKey查询下载地址,返回可能为空
     *
     * @param fileKey
     * @param bucketType 1私有imile-store 2公有imile-static
     * @return
     */
    public String getNullableUrlByFileKey(String fileKey, Integer bucketType) {
        if (StringUtils.isEmpty(fileKey)) {
            return null;
        }
        OssApiDto ossApiDto = rpcIpepClient.getUrlByFileKey(fileKey, bucketType);
        return ossApiDto == null ? null : ossApiDto.getFileUrl();
    }

    /**
     * 上传图片
     *
     * @param directory
     * @param retainFileName
     * @param fileName
     * @param bucketType
     * @param file
     * @return
     */
    public String upload(String directory, boolean retainFileName, String fileName, Integer bucketType, File file) {
        String url = attendanceProperties.getIpep().getIpepUrl() + UPLOAD_PATH;
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + UserInfoUtil.getUToken());
        Map<String, String> postData = Maps.newHashMap();
        postData.put("userId", "10010");
        postData.put("directory", directory);
        postData.put("retainFileName", String.valueOf(retainFileName));
        postData.put("bucketType", String.valueOf(bucketType));
        Map<String, File> files = Maps.newHashMap();
        files.put("file", file);
        HttpResult result = HttpUtils.postFile(url, files, postData, headers);
        JSONObject jsonObject = JSON.parseObject(result.getStringContent());
        String fileUrl = null;
        if (jsonObject != null && "success".equals(jsonObject.getString("resultCode"))) {
            fileUrl = jsonObject.getString("resultObject");
        }
        return fileUrl;

    }

    /**
     * 上传文件
     * @param directory 地址
     * @param fileName 文件名
     * @param file 文件
     * @param bucketType 文件存放空间类型 1私有 2公有
     * @return 文件url
     */
    public String upload(String directory, String fileName, byte[] file, Integer bucketType) {
        NoLoginTokenInfoDTO noLoginTokenInfo = rpcIpepClient.getNoLoginTokenInfo(HRMS.getCode());
        return this.upload(directory, fileName, file, bucketType,
                noLoginTokenInfo.getHeaderKey(), noLoginTokenInfo.getToken());
    }

    private String upload(String directory, String fileName, byte[] file, Integer bucketType, String tokenHeaderKey, String token) {
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addTextBody("directory", directory);
        builder.addBinaryBody("file", file, ContentType.MULTIPART_FORM_DATA, fileName);
        builder.addTextBody("bucketType", String.valueOf(bucketType));
        HttpEntity build = builder.build();
        String uploadUrl = attendanceProperties.getIpep().getIpepUrl() + NO_LOGIN_UPLOAD_PATH;
        HttpClient client = HttpUtils.getClient(uploadUrl);
        HttpPost httpPost = new HttpPost(uploadUrl);
        httpPost.setEntity(build);
        if (StringUtils.isNotBlank(tokenHeaderKey) && StringUtils.isNotBlank(token)) {
            httpPost.setHeader(tokenHeaderKey, token);
        }
        try {
            HttpResponse httpResponse = client.execute(httpPost);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                log.error(String.format("post: %s 请求失败啊，异常状态: %s", uploadUrl, statusCode));
                throw BusinessException.get(MsgCodeConstant.THIRD_SERVICE_ERROR);
            }
            byte[] resultBs = FileUtils.copyToByteArray(httpResponse.getEntity().getContent());
            String resultStr = new String(resultBs);
            JSONObject result = JSON.parseObject(resultStr);
            if (BusinessConstant.FAILURE.equals(result.getString("status"))) {
                throw BusinessException.get(MsgCodeConstant.THIRD_SERVICE_ERROR, result.getString("message"));
            }
            return result.getString("resultObject");
        } catch (IOException e) {
            log.error("post:" + uploadUrl + "请求失败，发生IOException", e);
            throw BusinessException.get(MsgCodeConstant.THIRD_SERVICE_ERROR);
        }
    }
}
