package com.imile.attendance.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Slf4j
public class CommonUtil {

    /**
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date
     * @return Date
     */
    public static Date convertDateByTimeZone(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }

    /**
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date 日期
     * @return Date
     */
    public static Date convertDateByTimeZonePlus(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            if (Integer.parseInt(timeZone) < 0 ){
                timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            }
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }
}
