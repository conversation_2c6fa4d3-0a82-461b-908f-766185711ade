package com.imile.attendance.util;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 时间格式化工具类
 *
 * <AUTHOR>
 */
public class DateFormatUtils {
    private DateFormatUtils(){}

    /**
     * 默认日期格式
     */
    public static final String DATE = "yyyy-MM-dd";
    /**
     * 默认时间格式
     */
    private static final String DATETIME = "yyyy-MM-dd HH:mm:ss";

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE);

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATETIME);

    public static String formatDate(LocalDateTime time) {
        return format(time, DATE_FORMATTER);
    }

    public static String format(LocalDateTime time) {
        return format(time, DATE_TIME_FORMATTER);
    }

    public static LocalDateTime parse(String timeStr) {
        return LocalDateTime.parse(timeStr, DATE_TIME_FORMATTER);
    }

    public static LocalDate parseDate(String timeStr) {
        return LocalDate.parse(timeStr, DATE_FORMATTER);
    }

    public static String format(LocalDateTime time, DateTimeFormatter formatter) {
        if (time == null) {
            return "";
        }
        return time.format(formatter);
    }

    public static String formatDate(Date date) {
        return format(date, DATE);
    }

    /**
     * 将普通Date转为 yyyy-mm-dd的Date数据
     *
     * @param date
     * @return yyyy-mm-dd个格式的Date数据
     */
    public static Date defaultFormatDate(Date date) {
        String format = format(date);
        return parseDateTimeYYDDMM(format);
    }


    public static String format(Date date) {
        return format(date, DATETIME);
    }

    public static String format(Date date, String patten) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(date, patten);
    }

    public static Date parseYMDDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE);
            Date parse = sdf.parse(dateStr);
            return parse;
        } catch (Exception e) {
            throw BusinessException.get(ErrorCodeEnum.DATE_FORMAT_ERROR.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.DATE_FORMAT_ERROR.getDesc()));
        }
    }

    public static Date parseDateTime(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DATETIME);
            Date parse = sdf.parse(dateStr);
            return parse;
        } catch (Exception e) {
            throw BusinessException.get(ErrorCodeEnum.DATE_FORMAT_ERROR.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.DATE_FORMAT_ERROR.getDesc()));
        }
    }

    public static Date parseDateTimeYYDDMM(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE);
            Date parse = sdf.parse(dateStr);
            return parse;
        } catch (Exception e) {
            throw BusinessException.get(ErrorCodeEnum.DATE_FORMAT_ERROR.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.DATE_FORMAT_ERROR.getDesc()));
        }
    }


}
