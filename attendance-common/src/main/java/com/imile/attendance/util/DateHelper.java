package com.imile.attendance.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.MonthEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;



/**
 * 日期计算工具类
 *
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
public class DateHelper {

    private static final String HOUR_MIN = "HH:mm";

    private static final double HOUR = 3600 * 1000d;

    private static final double MINUS = 60 * 1000d;


    /**
     * yyyy/MM/dd 格式的日期格式化器
     */
    private static final String PATTERN_DATE_SLASH = "yyyy/MM/dd";

    /**
     * 获得年的部分
     *
     * @param date 日期
     * @return 年的部分
     */
    public static int year(Date date) {
        return DateUtil.year(date);
    }

    /**
     * 获得月份，从1开始计数
     *
     * @param date 日期
     * @return 月份，从1开始计数
     */
    public static int month(Date date) {
        return DateUtil.month(date) + 1;
    }

    /**
     * 获得指定日期是这个日期所在月份的第几天
     *
     * @param date 日期
     * @return 天
     */
    public static int dayOfMonth(Date date) {
        return DateUtil.dayOfMonth(date);
    }

    /**
     * 获取格式化的月份字符串（01-12）
     *
     * @param date 日期
     * @return 两位数的月份字符串
     */
    public static String formatMonth(Date date) {
        int month = month(date);
        return month < 10 ? "0" + month : String.valueOf(month);
    }

    /**
     * 将日期转为dayId,格式为yyyyMMdd
     */
    public static long getDayId(Date date) {
        // yyyyMMdd
        return Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
    }

    /**
     * 将dayId转换为date,格式为yyyyMMdd
     */
    public static Date transferDayIdToDate(Long dayId) {
        if (dayId == null) {
            return null;
        }
        // yyyyMMdd
        return DateUtil.parse(dayId.toString(), DatePattern.PURE_DATE_PATTERN);
    }

    /**
     * 将dayId转换为date,格式为yyyyMMdd
     */
    public static Date transferDayIdToDate(String dayId) {
        if (StringUtils.isEmpty(dayId)) {
            return null;
        }
        // yyyyMMdd
        return DateUtil.parse(dayId, DatePattern.PURE_DATE_PATTERN);
    }

    /**
     * 获取偏移指定天数后的dayId
     *
     * @param dayId  当前dayId (格式：yyyyMMdd)
     * @param offset 偏移天数，正数表示后移，负数表示前移
     * @return 偏移后的dayId
     */
    public static Long getOffsetDayId(Long dayId, int offset) {
        Date date = transferDayIdToDate(dayId);
        Date offsetDay = DateUtil.offsetDay(date, offset);
        return getDayId(offsetDay);
    }

    /**
     * 获取后一天的dayId
     *
     * @param dayId 当前dayId (格式：yyyyMMdd)
     * @return 后一天的dayId
     */
    public static Long getNextDayId(Long dayId) {
        return getOffsetDayId(dayId, 1);
    }

    /**
     * 获取前一天的dayId
     *
     * @param dayId 当前dayId (格式：yyyyMMdd)
     * @return 前一天的dayId
     */
    public static Long getPreviousDayId(Long dayId) {
        return getOffsetDayId(dayId, -1);
    }

    /**
     * dayId格式转换为 15-Nov-22格式 20220101
     */
    public static String dayIdFormat(Long dayId) {
        String day = dayId.toString().substring(6);
        String monthInt = dayId.toString().substring(4, 6);
        MonthEnum instance = MonthEnum.getInstance(monthInt);
        String month = instance.getDesc();
        String year = dayId.toString().substring(0, 4);
        return day + "-" + month + "-" + year;
    }

    /**
     * 获取从startDayId到endDayId之间的所有dayId列表（包含开始和结束日期）
     *
     * @param startDayId 开始日期的dayId，格式为yyyyMMdd
     * @param endDayId   结束日期的dayId，格式为yyyyMMdd
     * @return 包含所有日期dayId的列表
     */
    public static List<Long> getDayIdList(long startDayId, long endDayId) {
        if (startDayId > endDayId) {
            throw new IllegalArgumentException("开始日期不能大于结束日期");
        }

        List<Long> dayIdList = new ArrayList<>();

        // 当前日期
        Date currentDate = transferDayIdToDate(startDayId);

        // 循环直到超过endDayId
        while (true) {
            long currentDayId = getDayId(currentDate);
            dayIdList.add(currentDayId);

            // 如果已经到达结束日期，则退出循环
            if (currentDayId >= endDayId) {
                break;
            }

            // 日期加1天
            currentDate = DateUtil.offsetDay(currentDate, 1);
        }

        return dayIdList;
    }

    /**
     * 将打卡时间添加默认日期前缀
     *
     * @param timeStr 输入的时间（格式为 HH:mm:ss）
     * @return 格式化后的时间字符串（格式为 "1970-01-01 HH:mm:ss"），若输入为空则返回空字符串
     */
    public static String appendDefaultDateStrToTimeStr(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            // 输入为空时返回空字符串
            return "";
        }
        // 添加默认日期前缀：1970-01-01
        return BusinessConstant.DEFAULT_TIME + timeStr;
    }

    /**
     * 将打卡时间添加默认日期前缀(DatePattern.NORM_DATETIME_PATTERN)
     *
     * @param timeStr 输入的时间（格式为 HH:mm:ss）
     * @return 格式化后的时间（格式为 "1970-01-01 HH:mm:ss"），若输入为空则返回null
     */
    public static Date appendDefaultDateToTime(String timeStr) {
        String defaultDateToTimeStr = appendDefaultDateStrToTimeStr(timeStr);
        if (StringUtils.isBlank(defaultDateToTimeStr)) {
            return null;
        }
        return DateUtil.parse(defaultDateToTimeStr);
    }

    /**
     * 判断两个时间是否跨天
     *
     * @param firstTime  进入时间
     * @param secondTime 退出时间
     * @return 如果时间跨天返回 BusinessConstant.Y（代表“是”），否则返回 BusinessConstant.N（代表“否”）
     */
    public static Integer judgeCrossDay(Date firstTime, Date secondTime) {
        if (firstTime.before(secondTime)) {
            return BusinessConstant.N;
        }
        return BusinessConstant.Y;
    }

    /**
     * 将时间格式化为 HH:mm:ss
     *
     * @param date 日期
     * @return 格式化后的时间字符串
     */
    public static String formatHHMMSS(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_TIME_PATTERN) : "";
    }

    /**
     * 将日期格式化为 yyyy-MM-dd
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatYYYYMMDD(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_DATE_PATTERN) : "";
    }

    /**
     * 将日期格式化为 yyyy-MM-dd HH:mm:ss
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatYYYYMMDDHHMMSS(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN) : "";
    }

    /**
     * 连接日期和时间字符串 yyyy-MM-dd HH:mm:ss
     *
     * @param dateStr 日期字符串 yyyy-MM-dd
     * @param timeStr 时间字符串 HH:mm:ss
     * @return 连接后的日期时间字符串
     */
    public static String concatDateAndTimeStr(String dateStr, String timeStr) {
        return dateStr + " " + timeStr;
    }

    /**
     * 将日期字符串转换为日期
     *
     * @param dateStr 日期字符串 yyyy-MM-dd HH:mm:ss
     * @return Date 对应的日期
     */
    public static Date parseYYYYMMDDHHMMSS(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN);
    }

    public static Date parseYYYYMMDD(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, DatePattern.NORM_DATE_PATTERN);
    }

    /**
     * 将日期格式化为 yyyy/MM/dd
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDateWithSlash(Date date) {
        return date != null ? DateUtil.format(date, PATTERN_DATE_SLASH) : "";
    }

    /**
     * 将 yyyy/MM/dd 格式的字符串转换为日期
     *
     * @param dateStr yyyy/MM/dd 格式的日期字符串
     * @return Date 对象，如果转换失败返回 null
     */
    public static Date parseDateWithSlash(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, PATTERN_DATE_SLASH);
    }

    /**
     * 检查字符串是否为有效的 yyyy/MM/dd 格式日期
     *
     * @param dateStr 待检查的日期字符串
     * @return 是否为有效日期
     */
    public static boolean isValidDateWithSlash(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return false;
        }
        try {
            DateUtil.parse(dateStr, PATTERN_DATE_SLASH);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static Date pushDate(Date date, int pushDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date == null ? new Date() : date);
        calendar.add(Calendar.DATE, pushDay);
        return calendar.getTime();
    }

    /**
     * 判断两个日期相差几天
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int getDateDiff(Date startDate, Date endDate) {
        return (int) ((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));
    }

    /**
     * 判断两个日期是否跨年
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 是否跨年
     */
    public static Boolean judgeCrossYear(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        int firstYear = year(startDate);
        int secondYear = year(endDate);
        return firstYear != secondYear;
    }

    /**
     * 判断两个dayId是否跨年（dayId格式：yyyyMMdd）
     *
     * @param startDayId 第一个dayId
     * @param endDayId   第二个dayId
     * @return 是否跨年
     */
    public static Boolean judgeCrossYearByDayId(Long startDayId, Long endDayId) {
        if (startDayId == null || endDayId == null) {
            return false;
        }
        // 提取年份部分（前4位）
        int firstYear = (int) (startDayId / 10000);
        int secondYear = (int) (endDayId / 10000);
        return firstYear != secondYear;
    }

    /**
     * 根据时区转换日期
     *
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date     日期
     * @return Date
     */
    public static Date convertDateByTimeZonePlus(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone()
                    .getOffset(System.currentTimeMillis());
            if (Integer.parseInt(timeZone) < 0) {
                timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT" + timeZone)).getTimeZone()
                        .getOffset(System.currentTimeMillis());
            }
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }

    /**
     * 将日期字符串转换为对应时区的日期
     *
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param dateStr  日期字符串 yyyy-MM-dd HH:mm:ss
     * @return Date 返回对应时区的日期
     */
    public static Date convertDateStrByTimeZonePlus(String timeZone, String dateStr) {
        return convertDateByTimeZonePlus(timeZone,
                DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN));
    }

    public static BigDecimal diffMins(Date var1, Date var2) {
        if (var1 == null || var2 == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimalUtil.setDoubleScale((var1.getTime() - var2.getTime()) / MINUS, 2);
    }

    public static void main(String[] args) {
        // Date date = new Date();
        //// DateTime offset1 = DateUtil.offset(date, DateField.HOUR, 1);
        //// //DateTime直接调用方法会直接改源对象，除非限制设置不可变
        //// offset1.setMutable(false);
        //// DateTime offset2 = offset1.offset(DateField.MINUTE, 1);
        //// //DateUtil.offset会创建一个新的DateTime
        ////// DateTime offset2 = DateUtil.offset(offset1, DateField.HOUR, 1);
        //// System.out.println(date);
        //// System.out.println(offset1);
        //// System.out.println(offset2);
        // System.out.println(date);
        // DateTime dateTime = DateTime.of(date);
        // System.out.println(dateTime);
        // LocalTime now = LocalTime.now();
        // String format = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        // System.out.println(format);
        // LocalDate now = LocalDate.now();
        // String format = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // System.out.println(format);
        // long dayId = Long.parseLong(format);
        // System.out.println(dayId);
        // List<Long> dayIdList = getDayIdList(20250401, 20250410);
        // System.out.println(dayIdList);

        // System.out.println(judgeCrossYearByDayId(20250401L, 20250430L));
        // System.out.println(judgeCrossYearByDayId(20250401L, 20260430L));
        System.out.println(formatDateWithSlash(new Date()));
    }
}
