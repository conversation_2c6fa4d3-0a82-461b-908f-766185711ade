package com.imile.attendance.dto;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 状态切换参数
 */
@Data
public class StatusSwitchParamDTO implements Serializable {

    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;
    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private String status;

    /**
     * 是否模板
     */
    private Integer isTemplate;
}
