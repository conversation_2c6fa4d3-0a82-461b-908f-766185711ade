package com.imile.attendance.dto;

import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/21 
 * @Description 日期和时区日期数据传输对象，用于封装原始日期和根据时区转换后的日期信息。
 * 该类主要用于处理跨时区的日期转换，保存原始日期和转换后的日期，以及原始日期的时间戳。
 * 在需要处理不同时区日期的场景中使用，如国际化业务中的时间处理。
 */
@Data
public class DateAndTimeZoneDate {

    /**
     * 原始日期，未经时区转换的日期对象
     */
    private Date date;

    /**
     * 时区转换后的日期对象，根据指定时区转换后的日期
     */
    private Date timeZoneDate;

    /**
     * 原始日期的时间戳，单位为毫秒
     */
    private Long dateTimeStamp;

    /**
     * 创建一个 DateAndTimeZoneDate 对象
     * 
     * @param date 原始日期，未经时区转换的日期
     * @param timeZoneDate 时区转换后的日期
     * @return 包含原始日期和转换后日期的 DateAndTimeZoneDate 对象
     */
    public static DateAndTimeZoneDate of(Date date, Date timeZoneDate) {
        DateAndTimeZoneDate dateAndTimeZoneDate = new DateAndTimeZoneDate();
        dateAndTimeZoneDate.setDate(date);
        dateAndTimeZoneDate.setTimeZoneDate(timeZoneDate);
        dateAndTimeZoneDate.setDateTimeStamp(date.getTime());
        return dateAndTimeZoneDate;
    }

    /**
     * 创建一个表示结束日期的 DateAndTimeZoneDate 对象
     * 使用系统默认的结束时间和时间戳常量
     * 
     * @return 表示结束日期的 DateAndTimeZoneDate 对象
     */
    public static DateAndTimeZoneDate ofEndDate(){
        DateAndTimeZoneDate dateAndTimeZoneDate = new DateAndTimeZoneDate();
        dateAndTimeZoneDate.setDate(BusinessConstant.DEFAULT_END_TIME);
        dateAndTimeZoneDate.setTimeZoneDate(BusinessConstant.DEFAULT_END_TIME);
        dateAndTimeZoneDate.setDateTimeStamp(BusinessConstant.DEFAULT_END_TIMESTAMP);
        return dateAndTimeZoneDate;
    }
}
