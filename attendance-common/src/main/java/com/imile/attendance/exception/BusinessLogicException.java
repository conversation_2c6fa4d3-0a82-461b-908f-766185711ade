package com.imile.attendance.exception;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.common.exception.BusinessException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 业务逻辑异常类
 * 继承自BusinessException，提供了一系列静态方法用于业务逻辑校验
 * 当校验失败时，会抛出带有国际化消息的BusinessException
 *
 * <AUTHOR> chen
 * @Date 2025/3/21
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class BusinessLogicException extends BusinessException {

    /**
     * 检查表达式是否为true，如果为false则抛出异常
     * 使用自定义错误码和国际化编码
     *
     * @param expression 需要检查的表达式
     * @param errorCode 错误码
     * @param i18nCode 国际化编码
     * @throws BusinessException 当表达式为false时抛出
     */
    public static void check(boolean expression, String errorCode, String i18nCode) {
        if (!expression) {
            throw BusinessException.ofI18nCode(errorCode, i18nCode);
        }
    }

    /**
     * 检查表达式是否为true，如果为false则抛出异常
     * 使用错误码枚举中的code和desc
     *
     * @param expression 需要检查的表达式
     * @param errorCodeEnums 错误码枚举
     * @throws BusinessException 当表达式为false时抛出
     */
    public static void check(boolean expression, ErrorCodeEnum errorCodeEnums) {
        if (!expression) {
            throw BusinessException.ofI18nCode(errorCodeEnums.getCode(), errorCodeEnums.getDesc());
        }
    }

    /**
     * 检查表达式是否为false，如果为true则抛出异常
     * 使用自定义错误码和国际化编码
     *
     * @param expression 需要检查的表达式
     * @param errorCode 错误码
     * @param i18nCode 国际化编码
     * @throws BusinessException 当表达式为true时抛出
     */
    public static void checkTrue(boolean expression, String errorCode, String i18nCode) {
        if (expression) {
            throw BusinessException.ofI18nCode(errorCode, i18nCode);
        }
    }

    /**
     * 检查表达式是否为false，如果为true则抛出异常
     * 使用错误码枚举中的code和desc
     *
     * @param expression 需要检查的表达式
     * @param errorCodeEnums 错误码枚举
     * @throws BusinessException 当表达式为true时抛出
     */
    public static void checkTrue(boolean expression, ErrorCodeEnum errorCodeEnums) {
        if (expression) {
            throw BusinessException.ofI18nCode(errorCodeEnums.getCode(), errorCodeEnums.getDesc());
        }
    }

    /**
     * 检查表达式是否为false，如果为true则抛出异常
     * 使用错误码枚举中的code和desc，并支持参数格式化
     *
     * @param expression 需要检查的表达式
     * @param errorCodeEnums 错误码枚举
     * @param params 用于格式化国际化消息的参数
     * @throws BusinessException 当表达式为true时抛出
     */
    public static void checkTrue(boolean expression, ErrorCodeEnum errorCodeEnums, Object... params) {
        if (expression) {
            throw BusinessException.ofI18nCode(errorCodeEnums.getCode(), errorCodeEnums.getDesc(), params);
        }
    }

    /**
     * 检查表达式是否为false，如果为true则抛出异常
     * 使用自定义错误码和国际化编码，并支持参数格式化
     *
     * @param expression 需要检查的表达式
     * @param errorCode 错误码
     * @param i18nCode 国际化编码
     * @param params 用于格式化国际化消息的参数
     * @throws BusinessException 当表达式为true时抛出
     */
    public static void checkTrue(boolean expression, String errorCode, String i18nCode, Object... params) {
        if (expression) {
            throw BusinessException.ofI18nCode(errorCode, i18nCode, params);
        }
    }

    /**
     * 创建一个带有国际化消息的业务异常
     * 使用错误码枚举中的code和desc，并支持参数格式化
     *
     * @param errorCodeEnums 错误码枚举
     * @param params 用于格式化国际化消息的参数
     * @return 业务异常实例
     */
    public static BusinessException getException(ErrorCodeEnum errorCodeEnums, Object... params) {
        return BusinessException.ofI18nCode(errorCodeEnums.getCode(), errorCodeEnums.getDesc(), params);
    }
}
