package com.imile.attendance.enums;

import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
public interface BaseErrorMsg {

    /**
     * 异常消息
     *
     * @return 消息
     */
    String getMessage();

    /**
     * 异常码
     *
     * @return 异常码
     */
    String getCode();


    String getDesc();

    /**
     * 断言为 true, 就抛出异常
     *
     * @param v1 断言值
     */
    default void assertTrueThrows(boolean v1) {
        if (v1) {
            throw BusinessException.get(getCode(), I18nUtils.getMessage(getDesc()));
        }
    }

    /**
     * A method that throws a BusinessException if the given boolean value is true.
     *
     * @param v1  the boolean value to be evaluated
     * @param msg the message to be included in the BusinessException
     */
    default void assertTrueThrows(boolean v1, String msg) {
        if (v1) {
            throw BusinessException.get(getCode(), msg);
        }
    }

    /**
     * 断言必须是 非null, 否则抛出异常
     *
     * @param value 断言值
     */
    default void assertNonNull(Object value) {
        assertTrue(Objects.nonNull(value));
    }

    /**
     * 断言必须是 非null, 否则抛出指定异常信息
     * @param value 断言值
     * @param msg 异常信息
     */
    default void assertNonNull(Object value, String msg) {
        assertTrue(Objects.nonNull(value), msg);
    }

    /**
     * 断言必须是 false, 否则抛出异常
     *
     * @param v1 断言值
     */
    default void assertFalse(boolean v1) {
        this.assertTrue(!v1);
    }

    /**
     * 断言必须是 false, 否则抛出异常
     *
     * @param v1  断言值
     * @param msg 自定义消息
     */
    default void assertFalse(boolean v1, String msg) {
        this.assertTrue(!v1, msg);
    }

    /**
     * 断言必须是 true, 否则抛出异常
     *
     * @param v1 断言值
     */
    default void assertTrue(boolean v1) {
        if (v1) {
            return;
        }
        throw BusinessException.get(getCode(), I18nUtils.getMessage(getDesc()));
    }


    /**
     * 断言必须是 true, 否则抛出异常
     *
     * @param v1  断言值
     * @param msg 自定义消息
     */
    default void assertTrue(boolean v1, String msg) {
        if (v1) {
            return;
        }
        if (StringUtils.isBlank(msg)) {
            throw BusinessException.get(getCode(), I18nUtils.getMessage(getDesc()));
        }
        throw BusinessException.get(getCode(), msg);

    }

    /**
     * 断言必须是 true, 否则抛出异常
     *
     * @param v1    断言值
     * @param param 错误码参数
     */
    default void assertTrue(boolean v1, Object[] param) {
        if (v1) {
            return;
        }
        if (param == null) {
            throw BusinessException.get(getCode(), I18nUtils.getMessage(getDesc()));
        }
        throw BusinessException.ofI18nCode(getCode(), getDesc(), param);

    }
}
