package com.imile.attendance.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 考勤具体明细类型
 */
@Getter
public enum AttendanceConcreteTypeEnum {

    P("P", "PRESENT", "出勤", "present", "required", "required", "noRequired", "hidden", "hidden"),
    OJT("OJT", "PRESENT", "新员工培训", "New employee Training", "required", "required", "noRequired", "hidden", "hidden"),
    VI("VI/AA", "PRESENT", "设备问题无法工作", "The device fails to work", "required", "required", "noRequired", "hidden", "required"),
    SHIFT("SHIFT", "PRESENT", "排班错误", "shift error", "required", "required", "noRequired", "hidden", "hidden"),

    AL("AL", "LEAVE", "年假", "Annual leave", "hidden", "hidden", "hidden", "required", "required"),
    SL("SL", "LEAVE", "病假", "Sick Leave", "hidden", "hidden", "hidden", "required", "required"),
    BL("BL", "LEAVE", "丧假", "Bereavement Leave", "hidden", "hidden", "hidden", "required", "required"),
    ML("ML", "LEAVE", "婚假", "Marriage Leave", "hidden", "hidden", "hidden", "required", "required"),
    CBL("CBL", "LEAVE", "新生儿假", "Child Birth Leave", "hidden", "hidden", "hidden", "required", "required"),
    HJL("HJL", "LEAVE", "朝圣假", "Hajj Leave", "hidden", "hidden", "hidden", "required", "required"),
    MTL("MTL", "LEAVE", "产假", "Maternity Leave", "hidden", "hidden", "hidden", "required", "required"),
    PL("PL", "LEAVE", "事假", "Personal Leave", "hidden", "hidden", "hidden", "required", "required"),


 /*   ML("ML", "LEAVE", "医疗假", "Medical Leave", "hidden", "hidden", "hidden", "required", "required"),
    UL("UL", "LEAVE", "事假", "Private Affair Leave", "hidden", "hidden", "hidden", "required", "hidden"),
    SICK_LEAVE("Sick Leave", "LEAVE", "病假", "Sick Leave", "hidden", "hidden", "hidden", "required", "required"),
    MARRIAGE_LEAVE("Marriage Leave", "LEAVE", "婚假", "Marriage Leave", "hidden", "hidden", "hidden", "required", "required"),
    MATERNITY_LEAVE("Maternity Leave", "LEAVE", "产假", "Maternity Leave", "hidden", "hidden", "hidden", "required", "required"),
    PARENTAL_LEAVE("Parental Leave", "LEAVE", "育婴假", "Parental Leave", "hidden", "hidden", "hidden", "required", "required"),
    BEREAVEMENT_LEAVE("Bereavement Leave", "LEAVE", "丧亲假", "Bereavement Leave", "hidden", "hidden", "hidden", "required", "required"),
    STUDY_LEAVE("Study Leave", "LEAVE", "进修假", "Study Leave", "hidden", "hidden", "hidden", "required", "required"),
    PUBLIC_LEAVE("Public Holiday", "LEAVE", "公共假期", "Public Holiday", "hidden", "hidden", "hidden", "required", "required"),
//    HAJJ_LEAVE("Hajj Holiday", "LEAVE", "开斋节", "Hajj Holiday", "hidden", "hidden", "hidden", "hidden", "hidden"),*/


    A("A", "ABSENT", "缺勤", "absent", "hidden", "hidden", "hidden", "hidden", "hidden"),
    R("R", "ABSENT", "辞职", "resign", "hidden", "hidden", "hidden", "hidden", "required"),
    T("T", "ABSENT", "辞退", "Lay off", "hidden", "hidden", "hidden", "hidden", "required"),

    /**
     * TODO 日历这里是否显示，等产品后期确定
     */
    OFF("OFF", "OFF", "休息", "off", "hidden", "hidden", "required", "hidden", "hidden"),
    //PH是请假的一种特殊表现
    PH("PH", "PH", "法定节假日", "Public Holiday", "hidden", "hidden", "required", "hidden", "hidden"),

    OOO("OOO", "OOO", "外勤", "ooo", "hidden", "hidden", "required", "hidden", "hidden"),

    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 考勤类型
     */
    private String attendanceType;

    /**
     * 描述
     */
    private String desc;

    /**
     * 描述英文
     */
    private String descEn;

    /**
     * 是否展示应出勤时长  不展示：hidden  必填：required  选填：noRequired
     */
    private String isShouldAttendanceHours;

    /**
     * 是否展示出勤时长 不展示：hidden  必填：required  选填：noRequired
     */
    private String isAttendanceHours;

    /**
     * 是否展示加班时长 不展示：hidden  必填：required  选填：noRequired
     */
    private String isOvertimeHours;

    /**
     * 是否展示请假时长 不展示：hidden  必填：required  选填：noRequired
     */
    private String isLeaveHours;

    /**
     * 是否展示证明资料 不展示：hidden  必填：required  选填：noRequired
     */
    private String isMaterialDetail;

    AttendanceConcreteTypeEnum(String code, String attendanceType, String desc, String descEn, String isShouldAttendanceHours, String isAttendanceHours, String isOvertimeHours, String isLeaveHours, String isMaterialDetail) {
        this.code = code;
        this.attendanceType = attendanceType;
        this.desc = desc;
        this.descEn = descEn;
        this.isShouldAttendanceHours = isShouldAttendanceHours;
        this.isAttendanceHours = isAttendanceHours;
        this.isOvertimeHours = isOvertimeHours;
        this.isLeaveHours = isLeaveHours;
        this.isMaterialDetail = isMaterialDetail;
    }

    public static AttendanceConcreteTypeEnum getInstanceByCode(String code) {
        for (AttendanceConcreteTypeEnum value : AttendanceConcreteTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }

    public static List<AttendanceConcreteTypeEnum> getInstanceByType(String attendanceType) {
        List<AttendanceConcreteTypeEnum> concreteTypeEnumList = new ArrayList<>();
        for (AttendanceConcreteTypeEnum value : AttendanceConcreteTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(attendanceType, value.getAttendanceType())) {
                concreteTypeEnumList.add(value);
            }
        }
        return concreteTypeEnumList;
    }
}
