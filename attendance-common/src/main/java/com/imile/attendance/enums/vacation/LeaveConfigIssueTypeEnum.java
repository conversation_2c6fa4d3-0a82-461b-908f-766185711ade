package com.imile.attendance.enums.vacation;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigIssueType
 * {@code @since:} 2024-04-15 16:23
 * {@code @description:}
 */
@Getter
public enum LeaveConfigIssueTypeEnum {
    DEFAULT(0, "", ""),
    FIXED_AMOUNT(1, "固定额度", "Fixed amount"),
    INCREASING_NUMBER_OF_LEADERS(2, "司领递增", "Increasing number of leaders"),
    NO_LIMIT_TO_THE_AMOUNT(3, "不限定额度", "No limit to the amount"),
    NO_INITIAL_QUOTA(4, "无初始额度", "No initial quota"),
    DISPATCH_COUNTRY_DISTANCE(5, "按派遣国远近", "By dispatch country distance"),
    INCREASING_WITH_AGE(6, "随年龄增加", "Increasing with age"),
    INCREASING_WITH_LENGTH_OF_SERVICE(7, "随工龄增加", "Increasing with length of service"),
    RELEASE_BY_PROVINCE_OR_CITY(8, "按省市配置", "Release by province or city"),

    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, LeaveConfigIssueTypeEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConfigIssueTypeEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConfigIssueTypeEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConfigIssueTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }

    /**
     * 获取阶段范围类型
     */
    public static List<Integer> getRangeTypeList() {
        return Lists.newArrayList(INCREASING_NUMBER_OF_LEADERS, INCREASING_WITH_AGE
                        , INCREASING_WITH_LENGTH_OF_SERVICE, RELEASE_BY_PROVINCE_OR_CITY)
                .stream()
                .map(LeaveConfigIssueTypeEnum::getType)
                .collect(Collectors.toList());
    }
}
