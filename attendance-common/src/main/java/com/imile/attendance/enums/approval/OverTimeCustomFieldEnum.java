package com.imile.attendance.enums.approval;

import lombok.Getter;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeCustomFieldEnum
 * {@code @since:} 2024-06-12 19:44
 * {@code @description:}
 */
@Getter
public enum OverTimeCustomFieldEnum {
    USER_NAME("userName", "被申请人姓名"),
    USER_CODE("userCode", "被申请人编码"),
    DEPT_NAME("deptName", "被申请人部门"),
    POST_NAME("postName", "被申请人岗位"),

    OVER_TIME_DAY("overTimeDay", "加班日"),
    OVER_TIME_START_DATE("overTimeStartDate", "加班开始时间"),
    OVER_TIME_END_DATE("overTimeEndDate", "加班结束时间"),
    OVER_TIME_ESTIMATE_DURATION("overTimeEstimateDuration", "预计加班时长"),

    REMARK("remark", "备注"),
    ATTACHMENT("attachment", "附件"),

    //多值
    USER_NAME_LIST("userNameList","被申请姓名"),
    USER_CODE_LIST("userCodeList","被申请人编码"),
    USER_DEPT_LIST("userDeptList","被申请人部门"),
    USER_POST_LIST("userPostList","被申请人岗位"),
    DAY_ID_LIST("dayIdList", "日期"),
    OVER_TIME_START_DATE_LIST("overTimeStartDateList", "加班开始时间"),
    OVER_TIME_END_DATE_LIST("overTimeEndDateList", "加班结束时间"),
    OVER_TIME_ESTIMATE_DURATION_List("overTimeEstimateDurationList", "预计加班时长"),
    REMARK_LIST("remarkList", "备注"),



    DAYS_LIST("daysList", "天集合"),
    HOURS_LIST("hoursList", "小时集合"),
    MINUTES_LIST("minutesList", "分钟集合"),
    LEGAL_WORKING_HOURS_LIST("legalWorkingHoursList", "该天法定工作时间集合"),
    DAY_SHIFT_INFO_LIST("dayShiftInfoList", "当天的排班信息集合"),
    LEAVE_INFO_LIST("leaveInfoList", "当天的请假信息集合"),
    REST_INFO_LIST("restInfoList", "当天的班次对应时刻的休息时间信息集合"),

    //角色字段
    BE_APPROVERED_USER_ID("beApproveredUserId", "被审批人ID"),
    DEPT_ID("deptId", "被申请人部门ID"),

    //判断条件
    USER_COUNTRY("userCountry", "被申请人所在国"),
    ORIGIN_COUNTRY("originCountry", "被申请人结算国"),


    //外勤-销假的特殊字段
    REVOKE_REASON("revokeReason", "撤销原因"),

    ;

    private String code;

    private String desc;

    OverTimeCustomFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
