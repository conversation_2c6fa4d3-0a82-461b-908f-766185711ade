package com.imile.attendance.enums.abnormal;

import lombok.Getter;

@Getter
public enum AbnormalOperationTypeEnum {
    LEAVE("LEAVE", "请假", "LEAVE"),
    OUT_OF_OFFICE("OUT_OF_OFFICE", "外勤", "OUT_OF_OFFICE"),
    REISSUE_CARD("REISSUE_CARD", "补卡", "REISSUE_CARD"),
    ABNORMAL_CONFIRM("ABNORMAL_CONFIRM", "确认异常", "ABNORMAL_CONFIRM"),
    ABNORMAL_EXPIRED("ABNORMAL_EXPIRED", "过期异常", "ABNORMAL_EXPIRED"),
    OFF("OFF", "休息日", "OFF"),
    PH("PH", "节假日", "PH"),
    P("P", "工作日", "P"),
    ADD_DURATION("ADD_DURATION", "补时长", "ADD_DURATION"),


    ;

    private String code;

    private String desc;

    private String descEn;

    AbnormalOperationTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static AbnormalOperationTypeEnum getInstance(String code) {
        for (AbnormalOperationTypeEnum value : AbnormalOperationTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
