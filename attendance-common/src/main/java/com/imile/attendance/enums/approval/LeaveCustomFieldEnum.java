package com.imile.attendance.enums.approval;

import lombok.Getter;

/**
 * 请假审批模板自定义的字段
 */
@Getter
public enum LeaveCustomFieldEnum {

    USER_NAME("userName", "被申请人姓名"),
    USER_CODE("userCode", "被申请人编码"),
    DEPT_NAME("deptName", "被申请人部门"),
    POST_NAME("postName", "被申请人岗位"),

    CONFIG_ID("configId", "假期规则主键"),
    LEAVE_TYPE("leaveType", "假期类型"),
    LEAVE_SHORT_NAME("leaveShortName", "假期简称"),
    LEAVE_RESIDUAL("leaveResidual", "假期可用余额"),
    LEAVE_START_DATE("leaveStartDate", "请假开始时间"),
    LEAVE_END_DATE("leaveEndDate", "请假结束时间"),
    EXPECTED_LEAVE_TIME("expectedLeaveTime", "预计请假时长"),

    REMARK("remark", "备注"),
    ATTACHMENT("attachment", "附件"),

    //多值
    DAY_ID_LIST("dayIdList", "日期集合"),
    DAYS_LIST("daysList", "天集合"),
    HOURS_LIST("hoursList", "小时集合"),
    MINUTES_LIST("minutesList", "分钟集合"),
    LEGAL_WORKING_HOURS_LIST("legalWorkingHoursList", "该天法定工作时间集合"),
    DAY_SHIFT_INFO_LIST("dayShiftInfoList", "当天的排班信息集合"),
    LEAVE_INFO_LIST("leaveInfoList", "当天的请假信息集合"),
    REST_INFO_LIST("restInfoList", "当天的班次对应时刻的休息时间信息集合"),

    //角色字段
    BE_APPROVERED_USER_ID("beApproveredUserId", "被审批人ID"),
    DEPT_ID("deptId", "被申请人部门ID"),

    //判断条件
    USER_COUNTRY("userCountry", "被申请人所在国"),
    USER_ORIGIN_COUNTRY("userOriginCountry", "被申请人结算国"),
    IS_WAREHOUSE_STAFF("isWarehouseStaff", "被申请人是否仓内人员"),

    //请假-销假的特殊字段
    REVOKE_REASON("revokeReason", "撤销原因"),

    ;

    private String code;

    private String desc;

    LeaveCustomFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
