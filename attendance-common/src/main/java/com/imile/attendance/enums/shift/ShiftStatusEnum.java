package com.imile.attendance.enums.shift;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description 排班情况枚举
 */
@Getter
public enum ShiftStatusEnum {

    /**
     * 已排班
     */
    SHIFTED("SHIFTED", "已排班", "shifted"),
    /**
     * 未排班
     */
    UN_SHIFTED("UN_SHIFTED", "未排班", "unShifted");

    private final String code;
    private final String desc;
    private final String descEn;

    private static final Map<String, ShiftStatusEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (ShiftStatusEnum statusEnum : values()) {
            cacheMap.put(statusEnum.getCode(), statusEnum);
        }
    }

    ShiftStatusEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static ShiftStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        return cacheMap.get(code);
    }
}
