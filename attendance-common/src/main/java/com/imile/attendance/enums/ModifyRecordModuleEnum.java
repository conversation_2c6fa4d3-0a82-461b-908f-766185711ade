package com.imile.attendance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 变更记录模块枚举
 *
 * <AUTHOR>
 * @since 2025/5/10
 */
@Getter
@AllArgsConstructor
public enum ModifyRecordModuleEnum {

    CLASS_NATURE("CLASS_NATURE", "班次类型", "class type"),
    CALENDAR("FIXED_WORK", "日历", "calendar"),
    CLASS("CLASS", "班次", "class"),
    PUNCH_CONFIG("PUNCH_CONFIG", "打卡规则", "punch config"),
    REISSUE_CARD_CONFIG("REISSUE_CARD_CONFIG", "加班规则", "reissue card config"),
    OVERTIME_CONFIG("OVERTIME_CONFIG", "补卡规则", "overtime config"),

    ;


    private String code;

    private String desc;

    private String descEn;


    public static ModifyRecordModuleEnum getInstance(String code) {
        for (ModifyRecordModuleEnum value : ModifyRecordModuleEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }


}
