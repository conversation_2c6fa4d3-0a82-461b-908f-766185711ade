package com.imile.attendance.enums.punch;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 打卡方式类型枚举值（搜索条件）
 * PS:手动添加，不用字典转换，效率太低
 *
 * <AUTHOR>
 */
@Getter
public enum PunchCardTypeSearchEnum {
    CARD("Card", "刷卡", "Card"),
    FACE("Face", "人脸", "Face"),
    FACE_PASSWORD("Face & Password", "人脸和密码", "Face & Password"),
    FACE_CN("人脸", "人脸中文", "Face_CN"),
    FINGER("Fingerprint", "指纹", "Fingerprint"),
    FINGER_CN("指纹", "指纹", "Finger_CN"),
    PALM("Palm", "掌纹", "Palm"),
    PALM_FACE("Palm & Face", "掌纹和人脸", "Palm & Face"),
    PALM_CN("掌纹", "掌纹中文", "Palm_CN"),
    GPS("GPS", "地理定位", "Palm"),
    WIFI("WIFI", "无线网络", "WIFI"),
    PASSWORD("Password", "密码", "Password"),
    PASSWORD_CN("密码", "密码中文", "Password_CN"),
    WPM_FACE("WPM_FACE", "仓内人脸", "Wpm Face"),
    ;

    private String code;

    private String desc;

    private String descEn;

    PunchCardTypeSearchEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }


    public static PunchCardTypeSearchEnum getInstance(String code) {
        for (PunchCardTypeSearchEnum value : PunchCardTypeSearchEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getFingerList() {
        return Lists.newArrayList(FINGER, FINGER_CN).stream().map(PunchCardTypeSearchEnum::getCode).collect(Collectors.toList());
    }

    public static List<String> getFaceList() {
        return Lists.newArrayList(FACE, FACE_PASSWORD, FACE_CN, PALM_FACE).stream().map(PunchCardTypeSearchEnum::getCode).collect(Collectors.toList());
    }

    public static List<String> getPalmList() {
        return Lists.newArrayList(PALM, PALM_CN, PALM_FACE).stream().map(PunchCardTypeSearchEnum::getCode).collect(Collectors.toList());
    }

    public static List<String> getPasswordList() {
        return Lists.newArrayList(PASSWORD, PASSWORD_CN, FACE_PASSWORD).stream().map(PunchCardTypeSearchEnum::getCode).collect(Collectors.toList());
    }
}
