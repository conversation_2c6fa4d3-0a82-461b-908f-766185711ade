package com.imile.attendance.enums.approval;

import lombok.Getter;

/**
 * 补卡审批模板自定义的字段
 */
@Getter
public enum ReissueCardCustomFieldEnum {
    USER_NAME("userName", "被申请人姓名"),
    USER_CODE("userCode", "被申请人编码"),
    DEPT_NAME("deptName", "被申请人部门"),
    POST_NAME("postName", "被申请人岗位"),
    // 仓内考勤特有字段
    WORK_OC_NAME("workOcName", "被申请人工作网点"),
    WORK_VENDOR_NAME("workVendorName", "被申请人工作供应商"),
    EMPLOYMENT_TYPE("employmentType", "被申请人用工类型"),


    REISSUE_CARD_DATE("reissueCardDate", "补卡日期"),
    REISSUE_CARD_TYPE("reissueCardType", "异常类型"),
    REISSUE_CARD_TYPE_DESC("reissueCardTypeDesc", "异常类型描述"),
    RESIDUE_REISSUE_CARD_COUNT("residueReissueCardCount", "剩余可用补卡次数"),
    ATTENDANCE_START_DATE("attendanceStartDate", "当前补卡日期对应的考勤周期起始时间"),
    ATTENDANCE_END_DATE("attendanceEndDate", "当前补卡日期对应的考勤周期截止时间"),
    PUNCH_CONFIG_CLASS_ITEM_INFO("punchConfigClassItemInfo", "打卡规则对应的班次的所有的时刻信息"),
    ACTUAL_PUNCH_TIME("actualPunchTime", "实际打卡时间(没有就为空)，有多个取离上下班最近的一个"),
    CORRECT_PUNCH_TIME("correctPunchTime", "补卡后的时间(根据时刻时间来补)"),
    EARLY_PUNCH_TIME("earlyPunchTime", "最早打卡时间"),
    LATE_PUNCH_TIME("latePunchTime", "最晚打卡时间"),

    REMARK("remark", "备注"),
    ATTACHMENT("attachment", "附件"),

    //角色字段
    BE_APPROVERED_USER_ID("beApproveredUserId", "被审批人ID"),
    DEPT_ID("deptId", "被申请人部门ID"),

    //判断条件
    USER_COUNTRY("userCountry", "被申请人所在国"),
    USER_ORIGIN_COUNTRY("userOriginCountry", "被申请人结算国"),


    //补卡-撤销的特殊字段
    REVOKE_REASON("revokeReason", "撤销原因"),

    ;

    private String code;

    private String desc;

    ReissueCardCustomFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
