package com.imile.attendance.enums;

import lombok.Getter;

@Getter
public enum AbnormalPunchStatusEnum {

    NO_PUNCH_CARD("NO_PUNCH_CARD", "No Punch Card", "免打卡"),
    N0_CLASS("N0_CLASS", "No shift owner", "无排班"),
    N0_CLASS_REST("N0_CLASS_REST", "No schedule - Day off", "无排班-休息日"),
    REST("REST", "Day off", "休息日"),
    ;

    private String code;

    private String descEn;

    private String desc;

    AbnormalPunchStatusEnum(String code, String descEn, String desc) {
        this.code = code;
        this.descEn = descEn;
        this.desc = desc;
    }
}
