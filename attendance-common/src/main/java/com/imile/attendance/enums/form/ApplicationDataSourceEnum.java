package com.imile.attendance.enums.form;

import lombok.Getter;

@Getter
public enum ApplicationDataSourceEnum {
    IMPORT("IMPORT", "用户导入"),
    ;

    private String code;

    private String desc;


    ApplicationDataSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static ApplicationDataSourceEnum getInstance(String code) {
        for (ApplicationDataSourceEnum value : ApplicationDataSourceEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
