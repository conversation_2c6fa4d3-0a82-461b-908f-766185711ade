package com.imile.attendance.enums.punch;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 补贴方式枚举值
 * </p>
 *
 * <AUTHOR>
 * @menu
 * @since 2022/1/25
 */
@Getter
public enum PunchTypeEnum {


    ON_DUTY("ON_DUTY", "上班卡"),
    OUT_DUTY("OUT_DUTY", "下班打卡"),
    ;

    private String code;

    private String desc;

    private static final Map<String, PunchTypeEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (PunchTypeEnum codeEnum : values()) {
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }
    }

    PunchTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PunchTypeEnum parserEnum(String code) {
        if (code == null) {
            return null;
        }
        return cacheMap.get(code);
    }
}
