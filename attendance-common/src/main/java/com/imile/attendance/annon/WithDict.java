package com.imile.attendance.annon;

/**
 * <AUTHOR> chen
 * @Date 2025/1/17 
 * @Description
 */

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数字字典转换
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface WithDict {

    /**
     * 指定字典类型
     */
    String typeCode();

    /**
     * 指定value 值写入字段
     */
    String ref();
}
